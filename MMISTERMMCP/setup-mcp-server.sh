#!/bin/bash

# Setup script for mister-ada-mcp server

echo "Setting up mister-ada-mcp server..."

# Navigate to the MCP server directory
cd mcp-server

# Install dependencies
echo "Installing dependencies..."
npm install

# Create a symbolic link to the trading bot source directory if needed
# Uncomment and modify this if your trading bot source is in a different location
# ln -sf ../src ./src

# Make the server executable
chmod +x server.js

echo "Setup complete!"
echo ""
echo "To start the MCP server, run:"
echo "cd mcp-server && npm start"
echo ""
echo "To configure the server, edit the .env file in the mcp-server directory."
