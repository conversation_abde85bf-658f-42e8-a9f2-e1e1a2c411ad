/**
 * MISTER - Autonomous Cardano DEX Trading Agent that combines TapTools data, GPT-4 analysis, and Dexter/Iris for Cardano token swaps.
 *
 * Features:
 * - Fetches top volume tokens and extended market data from TapTools
 * - Uses GPT-4 for trading decisions based on comprehensive market analysis
 * - Executes trades through Dexter & Iris on Cardano DEXes
 * - Implements portfolio management with category-based allocation
 * - Includes safety checks and error handling
 *
 * Environmental Requirements:
 * @requires TAP_TOOLS_API_KEY - API key for TapTools integration
 * @requires OPENAI_API_KEY - API key for GPT-4 access
 * @requires BLOCKFROST_PROJECT_ID - Blockfrost project ID for Cardano network access
 * @requires SEED_PHRASE - Seed phrase for the trading wallet
 * @requires CARDANO_ADDRESS - (Optional) Address for portfolio monitoring
 *
 * Core Components:
 * 1. Data Collection: TapTools API integration for market data
 * 2. Analysis: GPT-4 trading decision engine
 * 3. Portfolio Management: Category-based position sizing
 * 4. Execution: Dexter/Iris integration for DEX swaps
 *
 * Trading Logic:
 * - Monitors top volume tokens on Cardano
 * - Analyzes price, volume, and market structure
 * - Validates trades against portfolio allocations
 * - Executes trades with slippage protection
 *
 * Safety Features:
 * - Confidence thresholds for trade execution
 * - Portfolio balance checks
 * - Position size limits
 * - Error handling and logging
 *
 * @example
 * ```typescript
 * // Start the trading bot
 * mainLoop().catch(console.error);
 * ```
 *
 * @remarks
 * The bot runs in continuous mode with 60-second intervals between iterations.
 * Ensure all environment variables are properly set before running.
 *
 * @see {@link https://docs.taptools.io} for TapTools API documentation
 * @see {@link https://docs.indigoprotocol.io} for Iris documentation
 */


// 1) ENV & Imports
import 'dotenv/config';
import axios, { AxiosError } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { AGENT_BIO, AGENT_LORE } from './config/prompts';
import {
  ANALYSIS_GUIDELINES,
  DATA_FIELDS,
  ANALYSIS_STEPS,
  EXPECTED_RESPONSE_FORMAT
} from './config/analysis-guidelines';
import {
  saveTrade,
  updateTradeExit,
  getAllTrades,
  getTradesByToken,
  Trade
} from './database';
import { analyzeRSI } from './indicators';
import {
  TokenRiskCategory,
  TokenAgeCategory,
  determineRiskCategory,
  determineTokenAge,
  calculateDynamicStopLoss,
  calculateTrailingStop,
  calculateTimeBasedExit,
  isHighVolumeToken
} from './risk-management';
import { userConfigManager, getUserTradingParams } from './config/user-config-manager';
import { UserTradingConfig } from './config/user-trading-config';


// Dexter
import {
  Dexter,
  DexterConfig,
  RequestConfig,
  BaseWalletProvider,
  LucidProvider,
  BaseDataProvider,
  BlockfrostProvider,
  TokenRegistryProvider,
  FetchRequest,
  SwapRequest,
  LiquidityPool,
  SundaeSwapV1,
  DexTransaction
} from '@indigo-labs/dexter';

import {
  Asset as DexterAsset,
  Token as DexterToken,
  LiquidityPool as DexterLiquidityPool
} from '@indigo-labs/dexter';


import {
  IrisApiService,
  Asset,
  LiquidityPool as IrisLiquidityPool,
  Token as IrisToken
} from '@indigo-labs/iris-sdk';

import {
  Category,
  TARGET_RATIOS as targetRatios,
  classifyToken
} from './config/portfolio-settings';

import { LlmDecision } from './types';
import { createLLMService, LLMService } from './llm-service';
import discordBot from './discord-bot';
import { botAnalysisBridge } from './bot-analysis-bridge.js';
import express from 'express';

///////////////////////////////////////////
// 2) ENV VARS
///////////////////////////////////////////
const {
  TAP_TOOLS_API_KEY,
  BLOCKFROST_PROJECT_ID,
  SEED_PHRASE,         // Seed phrase for Agent's wallet
  CARDANO_ADDRESS,     // The user's Cardano address to check portfolio
} = process.env;

if (!TAP_TOOLS_API_KEY) {
  console.error('Missing TAP_TOOLS_API_KEY in .env');
  process.exit(1);
}
if (!BLOCKFROST_PROJECT_ID) {
  console.error('Missing BLOCKFROST_PROJECT_ID in .env');
  process.exit(1);
}
if (!CARDANO_ADDRESS) {
  console.warn('No CARDANO_ADDRESS provided. Portfolio checks will be skipped.');
}

///////////////////////////////////////////
// 3) Setup Dexter & LLM Service
///////////////////////////////////////////

// Initialize LLM Service
let llmService: LLMService;
try {
  llmService = createLLMService();
  console.log(`[LLM] Initialized ${llmService.getConfig().provider} with model ${llmService.getConfig().model}`);
} catch (error) {
  console.error('[LLM] Failed to initialize LLM service:', error);
  process.exit(1);
}

///////////////////////////////////////////
// Enhanced Caching & Multi-Timeframe Analysis
///////////////////////////////////////////

/**
 * Cache configuration for different data types
 */
interface CacheConfig {
  priceData: number;        // 30 seconds for price data
  twitterSentiment: number; // 5 minutes for sentiment
  tokenMetadata: number;    // 1 hour for token info
  rsiIndicators: number;    // 1 minute for technical indicators
  krakenData: number;       // 2 minutes for ADA price data
}

const CACHE_CONFIG: CacheConfig = {
  priceData: 30 * 1000,        // 30 seconds
  twitterSentiment: 5 * 60 * 1000, // 5 minutes
  tokenMetadata: 60 * 60 * 1000,   // 1 hour
  rsiIndicators: 60 * 1000,        // 1 minute
  krakenData: 2 * 60 * 1000        // 2 minutes
};

/**
 * Simple in-memory cache with TTL
 */
class SimpleCache {
  private cache = new Map<string, { data: any; expiry: number }>();

  set(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }
}

const globalCache = new SimpleCache();

// Default user ID for testing (can be overridden via environment variable)
const DEFAULT_USER_ID = process.env.DEFAULT_USER_ID || 'default-trader';

/**
 * Calculate Exponential Moving Average
 */
function calculateEMA(prices: number[], period: number): number {
  if (prices.length < period) return prices[prices.length - 1] || 0;

  const multiplier = 2 / (period + 1);
  let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period;

  for (let i = period; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
  }

  return ema;
}

/**
 * Calculate price volatility (standard deviation)
 */
function calculateVolatility(prices: number[]): number {
  if (prices.length < 2) return 0;

  const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
  const squaredDiffs = prices.map(price => Math.pow(price - mean, 2));
  const variance = squaredDiffs.reduce((a, b) => a + b, 0) / prices.length;

  return Math.sqrt(variance) / mean * 100; // Return as percentage
}

/**
 * Calculate support and resistance levels
 */
function calculateSupportResistance(highs: number[], lows: number[], prices: number[]): { support: number; resistance: number } {
  if (highs.length === 0 || lows.length === 0 || prices.length === 0) {
    const currentPrice = prices.length > 0 ? prices[prices.length - 1] : 1;
    return { support: currentPrice * 0.95, resistance: currentPrice * 1.05 };
  }

  const recentHighs = highs.slice(-20);
  const recentLows = lows.slice(-20);
  const currentPrice = prices[prices.length - 1];

  // Filter out invalid values and calculate meaningful levels
  const validHighs = recentHighs.filter(h => h > 0 && !isNaN(h) && isFinite(h));
  const validLows = recentLows.filter(l => l > 0 && !isNaN(l) && isFinite(l));

  if (validHighs.length === 0 || validLows.length === 0) {
    return { support: currentPrice * 0.95, resistance: currentPrice * 1.05 };
  }

  // Find significant levels using recent price action
  const resistance = Math.max(...validHighs);
  const support = Math.min(...validLows);

  // Sanity check: resistance should be higher than support and both should be reasonable
  if (resistance <= support || resistance > currentPrice * 10 || support < currentPrice * 0.1) {
    return { support: currentPrice * 0.95, resistance: currentPrice * 1.05 };
  }

  return { support, resistance };
}

/**
 * Get Kraken ADA price data with caching
 */
async function getKrakenADAPrice(): Promise<any> {
  const cacheKey = 'kraken_ada_price';
  const cached = globalCache.get(cacheKey);
  if (cached) return cached;

  try {
    const response = await axios.get('https://api.kraken.com/0/public/OHLC?pair=ADAUSD&interval=60');
    const data = response.data;

    if (data.error && data.error.length > 0) {
      throw new Error(`Kraken API error: ${data.error.join(', ')}`);
    }

    globalCache.set(cacheKey, data.result.ADAUSD, CACHE_CONFIG.krakenData);
    return data.result.ADAUSD;
  } catch (error) {
    console.error('[KRAKEN] Error fetching ADA price:', error);
    return null;
  }
}

/**
 * Calculate relative strength vs ADA
 */
async function getRelativeStrengthVsADA(unit: string, ticker: string): Promise<RelativeStrengthData | null> {
  const cacheKey = `relative_strength_${unit}`;
  const cached = globalCache.get(cacheKey);
  if (cached) return cached;

  try {
    // Get token 24h performance
    const tokenOhlcv = await taptoolsGetTokenOhlcv(unit, '1d', 2);
    if (!tokenOhlcv || tokenOhlcv.length < 2) return null;

    const tokenOpen = tokenOhlcv[0].open;
    const tokenClose = tokenOhlcv[tokenOhlcv.length - 1].close;
    const tokenPerformance24h = ((tokenClose - tokenOpen) / tokenOpen) * 100;

    // Get ADA 24h performance from Kraken
    const adaData = await getKrakenADAPrice();
    if (!adaData || adaData.length < 2) return null;

    const adaOpen = parseFloat(adaData[0][1]); // Open price
    const adaClose = parseFloat(adaData[adaData.length - 1][4]); // Close price
    const adaPerformance24h = ((adaClose - adaOpen) / adaOpen) * 100;

    // Calculate relative strength
    const relativeStrength = tokenPerformance24h - adaPerformance24h;
    const outperforming = relativeStrength > 0;

    // Categorize strength
    let strengthCategory: 'strong' | 'moderate' | 'weak' | 'underperforming';
    if (relativeStrength > 10) strengthCategory = 'strong';
    else if (relativeStrength > 2) strengthCategory = 'moderate';
    else if (relativeStrength > -2) strengthCategory = 'weak';
    else strengthCategory = 'underperforming';

    const result: RelativeStrengthData = {
      tokenPerformance24h,
      adaPerformance24h,
      relativeStrength,
      outperforming,
      strengthCategory
    };

    globalCache.set(cacheKey, result, CACHE_CONFIG.priceData);
    console.log(`[RELATIVE_STRENGTH] ${ticker}: ${relativeStrength.toFixed(2)}% vs ADA (${strengthCategory})`);
    return result;
  } catch (error) {
    console.error(`[RELATIVE_STRENGTH] Error for ${ticker}:`, error);
    return null;
  }
}

/**
 * Multi-timeframe analysis with confluence scoring
 */
async function getMultiTimeframeAnalysis(unit: string, ticker: string): Promise<MultiTimeframeData[]> {
  const cacheKey = `multi_timeframe_${unit}`;
  const cached = globalCache.get(cacheKey);
  if (cached) return cached;

  const timeframes = [
    { name: '15m', resolution: '15m', periods: 96 },   // 24 hours of 15m candles
    { name: '1h', resolution: '1h', periods: 48 },     // 48 hours of 1h candles
    { name: '4h', resolution: '4h', periods: 42 }      // 7 days of 4h candles
  ];

  const results: MultiTimeframeData[] = [];

  try {
    for (const tf of timeframes) {
      // Get OHLCV data for this timeframe
      const ohlcv = await taptoolsGetTokenOhlcv(unit, tf.resolution, tf.periods);
      if (!ohlcv || ohlcv.length < 10) continue;

      // Enhanced technical analysis calculations
      const prices = ohlcv.map(candle => candle.close);
      const highs = ohlcv.map(candle => candle.high);
      const lows = ohlcv.map(candle => candle.low);
      const volumes = ohlcv.map(candle => candle.volume || 0);

      const currentPrice = prices[prices.length - 1];
      const previousPrice = prices[prices.length - 2] || currentPrice;
      const high24h = Math.max(...highs.slice(-24));
      const low24h = Math.min(...lows.slice(-24));

      // Calculate price action metrics
      const priceChange24h = currentPrice - (prices[prices.length - 24] || currentPrice);
      const priceChangePercent = ((priceChange24h / (prices[prices.length - 24] || currentPrice)) * 100);
      const volatility = calculateVolatility(prices.slice(-20));

      // Calculate EMAs
      const ema20 = calculateEMA(prices, 20);
      const ema50 = calculateEMA(prices, 50);

      // Determine trend with multiple factors
      let trend: 'bullish' | 'bearish' | 'neutral' = 'neutral';
      let trendStrength = 0;

      // EMA trend analysis
      const emaTrend = currentPrice > ema20 && ema20 > ema50 ? 'bullish' :
                      currentPrice < ema20 && ema20 < ema50 ? 'bearish' : 'neutral';

      // Price momentum analysis
      const recentPrices = prices.slice(-10);
      const avgRecent = recentPrices.reduce((a, b) => a + b, 0) / recentPrices.length;
      const olderPrices = prices.slice(-20, -10);
      const avgOlder = olderPrices.reduce((a, b) => a + b, 0) / olderPrices.length;
      const momentumStrength = ((avgRecent - avgOlder) / avgOlder) * 100;

      // Combine trend signals with proper strength calculation (0-100)
      const baseStrength = Math.min(Math.abs(momentumStrength) * 10, 50); // Scale momentum to 0-50
      const emaBonus = currentPrice > ema20 && ema20 > ema50 ? 25 : currentPrice < ema20 && ema20 < ema50 ? 25 : 0;
      const volumeBonus = 0; // Will be calculated later with volume data

      if (emaTrend === 'bullish' && momentumStrength > 1) {
        trend = 'bullish';
        trendStrength = Math.min(baseStrength + emaBonus + volumeBonus, 100);
      } else if (emaTrend === 'bearish' && momentumStrength < -1) {
        trend = 'bearish';
        trendStrength = Math.min(baseStrength + emaBonus + volumeBonus, 100);
      } else {
        trend = 'neutral';
        trendStrength = Math.min(baseStrength, 30); // Neutral trends max 30% strength
      }

      // Ensure trend strength is always 0-100
      trendStrength = Math.max(0, Math.min(100, trendStrength));

      // Calculate RSI for this timeframe with enhanced error handling
      let rsi: number = 50; // Default neutral RSI
      let rsiTrend: 'rising' | 'falling' | 'neutral' = 'neutral';

      // Try to get real RSI data from TapTools
      try {
        console.log(`[MULTI_TIMEFRAME] ${ticker} ${tf.name}: Requesting RSI with interval '${tf.resolution}'`);
        const rsiData = await taptoolsGetTokenRSI(unit, tf.resolution, 14, 10);

        if (rsiData && Array.isArray(rsiData) && rsiData.length > 0) {
          // Parse the last RSI value
          const lastRsiValue = rsiData[rsiData.length - 1];
          const parsedRsi = typeof lastRsiValue === 'number' ? lastRsiValue : parseFloat(lastRsiValue);

          if (!isNaN(parsedRsi) && parsedRsi >= 0 && parsedRsi <= 100) {
            rsi = parsedRsi;

            // Calculate RSI trend if we have multiple values
            if (rsiData.length > 1) {
              const previousRsiValue = rsiData[rsiData.length - 2];
              const previousRsi = typeof previousRsiValue === 'number' ? previousRsiValue : parseFloat(previousRsiValue);

              if (!isNaN(previousRsi)) {
                rsiTrend = rsi > previousRsi + 2 ? 'rising' : rsi < previousRsi - 2 ? 'falling' : 'neutral';
              }
            }

            console.log(`[MULTI_TIMEFRAME] ${ticker} ${tf.name} RSI: ${rsi.toFixed(2)} (${rsiTrend}) - REAL DATA`);
          } else {
            console.log(`[MULTI_TIMEFRAME] ${ticker} ${tf.name} RSI: Invalid value ${parsedRsi}, using fallback ${rsi}`);
          }
        } else {
          console.log(`[MULTI_TIMEFRAME] ${ticker} ${tf.name} RSI: Empty/invalid response, using fallback ${rsi}`);
        }
      } catch (error) {
        console.log(`[MULTI_TIMEFRAME] ${ticker} ${tf.name} RSI: API error, using fallback ${rsi}:`, error instanceof Error ? error.message : error);
      }

      // Calculate volume trend
      const recentVolumes = volumes.slice(-5);
      const olderVolumes = volumes.slice(-10, -5);
      const avgRecentVol = recentVolumes.reduce((a, b) => a + b, 0) / recentVolumes.length;
      const avgOlderVol = olderVolumes.reduce((a, b) => a + b, 0) / olderVolumes.length;
      const volumeTrend = avgRecentVol > avgOlderVol * 1.2 ? 'increasing' :
                         avgRecentVol < avgOlderVol * 0.8 ? 'decreasing' : 'neutral';

      // Calculate support and resistance
      const { support, resistance } = calculateSupportResistance(highs, lows, prices);

      // Determine breakout potential
      const distanceToResistance = (resistance - currentPrice) / currentPrice * 100;
      const distanceToSupport = (currentPrice - support) / currentPrice * 100;
      const breakoutPotential = distanceToResistance < 3 && volumeTrend === 'increasing' ? 'high' :
                               distanceToResistance < 8 ? 'medium' : 'low';

      // Market structure analysis
      const trendDirection = trend === 'bullish' ? 'uptrend' : trend === 'bearish' ? 'downtrend' : 'sideways';

      results.push({
        timeframe: tf.name,
        resolution: parseInt(tf.resolution),
        ohlcv: ohlcv.slice(-20),
        rsi,
        trend,
        strength: trendStrength,
        priceAction: {
          currentPrice,
          priceChange24h,
          priceChangePercent,
          high24h,
          low24h,
          volatility
        },
        technicalIndicators: {
          rsi,
          rsiTrend,
          ema20,
          ema50,
          macdSignal: trend, // Simplified MACD signal
          volumeTrend
        },
        marketStructure: {
          trendDirection,
          trendStrength,
          supportLevel: support,
          resistanceLevel: resistance,
          breakoutPotential
        }
      });
    }

    // Calculate confluence score (0-1 based on timeframe agreement)
    const bullishCount = results.filter(r => r.trend === 'bullish').length;
    const bearishCount = results.filter(r => r.trend === 'bearish').length;
    const totalCount = results.length;

    let confluence = 0;
    if (bullishCount > bearishCount) {
      confluence = bullishCount / totalCount;
    } else if (bearishCount > bullishCount) {
      confluence = bearishCount / totalCount;
    } else {
      confluence = 0.5; // Neutral
    }

    // Add confluence to each result
    results.forEach(r => r.confluence = confluence);

    globalCache.set(cacheKey, results, CACHE_CONFIG.priceData);
    console.log(`[MULTI_TIMEFRAME] ${ticker}: ${results.length} timeframes analyzed, confluence: ${(confluence * 100).toFixed(1)}%`);
    return results;
  } catch (error) {
    console.error(`[MULTI_TIMEFRAME] Error for ${ticker}:`, error);
    return [];
  }
}

/**
 * Calculate dynamic position size based on multiple factors
 */
function calculateDynamicPositionSize(
  baseAmount: number,
  portfolioValue: number,
  decision: any,
  tokenData: any,
  riskCategory: string,
  userConfig?: any
): number {
  try {
    // Safety checks to prevent NaN
    if (!baseAmount || isNaN(baseAmount) || baseAmount <= 0) {
      console.log(`[DYNAMIC_SIZING] Invalid baseAmount: ${baseAmount}, using fallback 50 ADA`);
      baseAmount = 50;
    }

    if (!portfolioValue || isNaN(portfolioValue) || portfolioValue <= 0) {
      console.log(`[DYNAMIC_SIZING] Invalid portfolioValue: ${portfolioValue}, using fallback 100 ADA`);
      portfolioValue = 100;
    }

    console.log(`[DYNAMIC_SIZING] Input values - Base: ${baseAmount}, Portfolio: ${portfolioValue}, Risk: ${riskCategory}`);

    // Use user configuration if provided, otherwise use defaults
    const sizingConfig = userConfig?.dynamicSizing || DYNAMIC_SIZING_CONFIG;

    // Base allocation based on volatility/risk category
    let baseAllocation = 0.10; // Default 10%

    switch (riskCategory) {
      case 'low':
        baseAllocation = sizingConfig.volatilityBased.lowVol;
        break;
      case 'medium':
        baseAllocation = sizingConfig.volatilityBased.medVol;
        break;
      case 'high':
        baseAllocation = sizingConfig.volatilityBased.highVol;
        break;
    }

    // Confidence multiplier
    let confidenceMultiplier = 1.0;
    const confidence = decision.confidence || 5;

    if (confidence >= 8) {
      confidenceMultiplier = sizingConfig.confidenceBased.highConfidence;
    } else if (confidence >= 6) {
      confidenceMultiplier = sizingConfig.confidenceBased.medConfidence;
    } else {
      confidenceMultiplier = sizingConfig.confidenceBased.lowConfidence;
    }

    // Multi-timeframe confluence multiplier
    let confluenceMultiplier = 1.0;
    if (tokenData.multiTimeframe && tokenData.multiTimeframe.length > 0) {
      const confluence = tokenData.multiTimeframe[0].confluence || 0.5;

      if (confluence > 0.8) {
        confluenceMultiplier = sizingConfig.confluenceBased.highConfluence;
      } else if (confluence > 0.5) {
        confluenceMultiplier = sizingConfig.confluenceBased.medConfluence;
      } else {
        confluenceMultiplier = sizingConfig.confluenceBased.lowConfluence;
      }
    }

    // Relative strength multiplier
    let relativeStrengthMultiplier = 1.0;
    if (tokenData.relativeStrength) {
      const rs = tokenData.relativeStrength;
      if (rs.strengthCategory === 'strong') {
        relativeStrengthMultiplier = 1.2;
      } else if (rs.strengthCategory === 'moderate') {
        relativeStrengthMultiplier = 1.0;
      } else if (rs.strengthCategory === 'weak') {
        relativeStrengthMultiplier = 0.8;
      } else { // underperforming
        relativeStrengthMultiplier = 0.6;
      }
    }

    // Calculate final position size with safety checks
    const maxAllocation = baseAllocation * confidenceMultiplier * confluenceMultiplier * relativeStrengthMultiplier;
    const maxPositionValue = portfolioValue * maxAllocation;

    // Safety checks for calculations
    if (isNaN(maxAllocation) || maxAllocation <= 0) {
      console.log(`[DYNAMIC_SIZING] Invalid maxAllocation: ${maxAllocation}, using fallback`);
      return Math.min(baseAmount, portfolioValue * 0.10);
    }

    if (isNaN(maxPositionValue) || maxPositionValue <= 0) {
      console.log(`[DYNAMIC_SIZING] Invalid maxPositionValue: ${maxPositionValue}, using fallback`);
      return Math.min(baseAmount, portfolioValue * 0.10);
    }

    // Apply minimum and maximum limits
    const minPosition = 5; // Minimum 5 ADA
    const maxPosition = Math.min(maxPositionValue, portfolioValue * 0.25); // Never more than 25% of portfolio

    const calculatedAmount = Math.max(minPosition, Math.min(baseAmount, maxPosition));

    // Final safety check
    if (isNaN(calculatedAmount) || calculatedAmount <= 0) {
      console.log(`[DYNAMIC_SIZING] Final amount is NaN or invalid: ${calculatedAmount}, using fallback`);
      return Math.round(Math.min(baseAmount, portfolioValue * 0.10));
    }

    // Round to whole ADA values
    const finalAmount = Math.round(calculatedAmount);

    console.log(`[DYNAMIC_SIZING] ${tokenData.ticker}: Base: ${Math.round(baseAmount)} ADA, Risk: ${riskCategory}, Confidence: ${confidence}/10, Confluence: ${((tokenData.multiTimeframe?.[0]?.confluence || 0.5) * 100).toFixed(1)}%, Final: ${finalAmount} ADA`);

    return finalAmount;
  } catch (error) {
    console.error('[DYNAMIC_SIZING] Error calculating position size:', error);
    return Math.min(baseAmount, portfolioValue * 0.10); // Fallback to 10% max
  }
}

/**
 * Check for correlation-based exit signals
 */
function checkCorrelationBasedExits(tokenData: any, decision: any): { shouldExit: boolean; reason: string } {
  try {
    const ticker = tokenData.ticker;

    // Check multi-timeframe trend breakdown
    if (tokenData.multiTimeframe && tokenData.multiTimeframe.length > 0) {
      const mtfData = tokenData.multiTimeframe;
      const confluence = mtfData[0]?.confluence || 0.5;

      // If confluence drops below 30% (timeframes disagree), consider exit
      if (confluence < 0.3) {
        return {
          shouldExit: true,
          reason: `Multi-timeframe confluence breakdown: ${(confluence * 100).toFixed(1)}% (below 30% threshold)`
        };
      }

      // Check for bearish divergence across timeframes
      const bearishTimeframes = mtfData.filter((tf: any) => tf.trend === 'bearish').length;
      const totalTimeframes = mtfData.length;

      if (bearishTimeframes >= totalTimeframes * 0.7) { // 70% or more timeframes bearish
        return {
          shouldExit: true,
          reason: `Bearish trend dominance: ${bearishTimeframes}/${totalTimeframes} timeframes bearish`
        };
      }
    }

    // Check relative strength breakdown vs ADA
    if (tokenData.relativeStrength) {
      const rs = tokenData.relativeStrength;

      // If token starts severely underperforming ADA (>-10%), consider exit
      if (rs.relativeStrength < -10 && rs.strengthCategory === 'underperforming') {
        return {
          shouldExit: true,
          reason: `Severe underperformance vs ADA: ${rs.relativeStrength.toFixed(2)}% (below -10% threshold)`
        };
      }
    }

    // Check for technical breakdown signals
    if (decision.technicalAnalysis) {
      const rsi = decision.technicalAnalysis.technicalIndicators?.rsiValue || 50;
      const rsiTrend = decision.technicalAnalysis.technicalIndicators?.rsiTrend || 'neutral';

      // RSI breakdown: below 25 with downtrend indicates strong selling pressure
      if (rsi < 25 && rsiTrend === 'downtrend') {
        return {
          shouldExit: true,
          reason: `RSI breakdown: ${rsi.toFixed(1)} with downtrend (oversold with momentum)`
        };
      }
    }

    return { shouldExit: false, reason: '' };
  } catch (error) {
    console.error(`[CORRELATION_EXIT] Error checking exits for ${tokenData.ticker}:`, error);
    return { shouldExit: false, reason: '' };
  }
}

/**
 * Get risk category from token data
 */
function getRiskCategory(tokenData: any): string {
  // Use existing risk assessment if available
  if (tokenData.riskCategory) {
    return tokenData.riskCategory;
  }

  // Fallback to classification based on available data
  const ticker = tokenData.ticker;

  // Known stable/low risk tokens
  const lowRiskTokens = ['ADA', 'MIN', 'DJED', 'iUSD', 'USDM', 'USDA'];
  if (lowRiskTokens.includes(ticker)) {
    return 'low';
  }

  // Known high risk tokens (memes, new projects)
  const highRiskTokens = ['SNEK', 'HOSKY', 'DONK', 'MULLET', 'BERG'];
  if (highRiskTokens.includes(ticker)) {
    return 'high';
  }

  // Default to medium risk
  return 'medium';
}

/**
 * Multi-timeframe analysis data structure
 */
interface MultiTimeframeData {
  timeframe: string;
  resolution: number;
  ohlcv: any[];
  rsi?: number;
  trend?: 'bullish' | 'bearish' | 'neutral';
  strength?: number;
  confluence?: number; // 0-1 score based on timeframe agreement
  // Enhanced technical data
  priceAction?: {
    currentPrice: number;
    priceChange24h: number;
    priceChangePercent: number;
    high24h: number;
    low24h: number;
    volatility: number;
  };
  technicalIndicators?: {
    rsi: number;
    rsiTrend: 'rising' | 'falling' | 'neutral';
    ema20: number;
    ema50: number;
    macdSignal: 'bullish' | 'bearish' | 'neutral';
    volumeTrend: 'increasing' | 'decreasing' | 'neutral';
  };
  marketStructure?: {
    trendDirection: 'uptrend' | 'downtrend' | 'sideways';
    trendStrength: number; // 0-100
    supportLevel: number;
    resistanceLevel: number;
    breakoutPotential: 'high' | 'medium' | 'low';
  };
}

/**
 * Relative strength analysis vs ADA
 */
interface RelativeStrengthData {
  tokenPerformance24h: number;
  adaPerformance24h: number;
  relativeStrength: number;
  outperforming: boolean;
  strengthCategory: 'strong' | 'moderate' | 'weak' | 'underperforming';
}

/**
 * Dynamic position sizing configuration
 */
interface DynamicSizingConfig {
  volatilityBased: {
    lowVol: number;     // 15% of portfolio for low volatility tokens
    medVol: number;     // 10% for medium volatility
    highVol: number;    // 5% for high volatility
  };
  confidenceBased: {
    highConfidence: number;  // 8-10 confidence
    medConfidence: number;   // 6-7 confidence
    lowConfidence: number;   // 4-5 confidence
  };
  confluenceBased: {
    highConfluence: number;  // >80% timeframe agreement
    medConfluence: number;   // 50-80% agreement
    lowConfluence: number;   // <50% agreement
  };
  correlationLimits: {
    maxSectorExposure: number;    // Max % in any sector (DeFi, Meme, etc.)
    maxSimilarTokens: number;     // Max % in correlated tokens
  };
}

const DYNAMIC_SIZING_CONFIG: DynamicSizingConfig = {
  volatilityBased: {
    lowVol: 0.15,    // 15% for stable tokens
    medVol: 0.10,    // 10% for medium volatility
    highVol: 0.05    // 5% for high volatility
  },
  confidenceBased: {
    highConfidence: 1.5,  // 1.5x multiplier for 8-10 confidence
    medConfidence: 1.0,   // 1.0x multiplier for 6-7 confidence
    lowConfidence: 0.5    // 0.5x multiplier for 4-5 confidence
  },
  confluenceBased: {
    highConfluence: 1.3,  // 1.3x multiplier for >80% confluence
    medConfluence: 1.0,   // 1.0x multiplier for 50-80% confluence
    lowConfluence: 0.7    // 0.7x multiplier for <50% confluence
  },
  correlationLimits: {
    maxSectorExposure: 0.30,  // Max 30% in any sector
    maxSimilarTokens: 0.20    // Max 20% in correlated tokens
  }
};

// Dexter Config
const dexterConfig: DexterConfig = {
  shouldFetchMetadata: true,
  shouldFallbackToApi: true,
  shouldSubmitOrders: true,
  metadataMsgBranding: 'MISTER-Agent'
};

const requestConfig: RequestConfig = {
  timeout: 10_000,
  retries: 3
};

const dexter = new Dexter(dexterConfig, requestConfig);
const iris = new IrisApiService('https://iris.indigoprotocol.io');

///////////////////////////////////////////
// 4) Setup Providers
///////////////////////////////////////////
async function setupDexterProviders(): Promise<void> {
  // Data Provider
  const dataProvider: BaseDataProvider = new BlockfrostProvider({
    projectId: BLOCKFROST_PROJECT_ID!,
    url: 'https://cardano-mainnet.blockfrost.io/api/v0'
  });
  dexter.withDataProvider(dataProvider);

  // Metadata Provider
  const metadataProvider = new TokenRegistryProvider();
  dexter.withMetadataProvider(metadataProvider);

  if (!SEED_PHRASE) {
    throw new Error('No SEED_PHRASE provided in .env for seed-based wallet approach');
  }
  const seedWords = SEED_PHRASE.trim().split(/\s+/);
  const lucidProvider: BaseWalletProvider = new LucidProvider();

  console.log('[INIT] Loading wallet...')

  await lucidProvider.loadWalletFromSeedPhrase(
    seedWords,
    {
      accountIndex: 0
    },
    {
      projectId: BLOCKFROST_PROJECT_ID!,
      url: 'https://cardano-mainnet.blockfrost.io/api/v0'
    }
  );
  console.log('[INIT] Done loading wallet...')

  dexter.withWalletProvider(lucidProvider);
}

///////////////////////////////////////////
// 5) TapTools API Calls
///////////////////////////////////////////
interface TapToolsVolumeToken {
  price: number;
  ticker: string;
  unit: string;
  volume: number;
  [key: string]: any;
}

async function taptoolsGetTopVolumeTokens(perPage = 10): Promise<TapToolsVolumeToken[]> {
  try {
    const url = 'https://openapi.taptools.io/api/v1/token/top/volume';
    const resp = await axios.get<TapToolsVolumeToken[]>(url, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      }
    });
    return resp.data.slice(0, perPage);
  } catch (err) {
    console.error('Error fetching top volume tokens:', err);
    return [];
  }
}

// Example: address info for portfolio checks
async function taptoolsGetAddressInfo(address: string): Promise<Record<string, any> | null> {
  try {
    const baseUrl = 'https://openapi.taptools.io/api/v1/address/info';
    const resp = await axios.get(baseUrl, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
      params: { address }
    });
    return resp.data;
  } catch (err) {
    console.error(`Error fetching address info: ${err}`);
    return null;
  }
}

export async function taptoolsGetTokenPrices(units: string[]): Promise<Record<string, number>> {
  try {
    const url = 'https://openapi.taptools.io/api/v1/token/prices';
    const resp = await axios.post(url, units, {
      headers: {
        'Content-Type': 'application/json',
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
    });
    return resp.data;
  } catch (err) {
    console.error('Error fetching token prices:', err);
    return {};
  }
}

export async function taptoolsGetTokenPriceChg(unit: string, timeframes = '1h,4h,24h'): Promise<Record<string, number>> {
  try {
    const baseUrl = 'https://openapi.taptools.io/api/v1/token/prices/chg';
    const resp = await axios.get(baseUrl, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
      params: { unit, timeframes }
    });
    if (resp.status === 200) {
      return resp.data;
    } else {
      console.error(`Error ${resp.status} fetching price change for ${unit}:`, resp.data);
      return {};
    }
  } catch (err) {
    console.error('taptoolsGetTokenPriceChg error:', err);
    return {};
  }
}

export async function taptoolsGetTokenPools(unit: string, adaOnly = 1): Promise<any[]> {
  try {
    const url = 'https://openapi.taptools.io/api/v1/token/pools';
    const resp = await axios.get(url, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
      params: { unit, adaOnly }
    });
    if (resp.status === 200) {
      return resp.data;
    } else {
      console.error(`Error ${resp.status} fetching pools for ${unit}:`, resp.data);
      return [];
    }
  } catch (err) {
    console.error('taptoolsGetTokenPools error:', err);
    return [];
  }
}

export async function taptoolsGetTokenOhlcv(unit: string, interval = '1d', numIntervals = 30): Promise<any[]> {
  try {
    const url = 'https://openapi.taptools.io/api/v1/token/ohlcv';
    const resp = await axios.get(url, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
      params: { unit, interval, numIntervals }
    });
    if (resp.status === 200) {
      return resp.data;
    } else {
      console.error(`Error ${resp.status} fetching OHLCV for ${unit}:`, resp.data);
      return [];
    }
  } catch (err) {
    console.error('taptoolsGetTokenOhlcv error:', err);
    return [];
  }
}

export async function taptoolsGetTokenTradingStats(unit: string, timeframe = '24h'): Promise<Record<string, any>> {
  try {
    const url = 'https://openapi.taptools.io/api/v1/token/trading/stats';
    const resp = await axios.get(url, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
      params: { unit, timeframe }
    });
    if (resp.status === 200) {
      return resp.data;
    } else {
      console.error(`Error ${resp.status} fetching trading stats for ${unit}:`, resp.data);
      return {};
    }
  } catch (err) {
    console.error('taptoolsGetTokenTradingStats error:', err);
    return {};
  }
}

export async function taptoolsGetTokenMcap(unit: string): Promise<Record<string, any>> {
  try {
    const url = 'https://openapi.taptools.io/api/v1/token/mcap';
    const resp = await axios.get(url, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
      params: { unit }
    });
    if (resp.status === 200) {
      return resp.data;
    } else {
      console.error(`Error ${resp.status} fetching mcap for ${unit}:`, resp.data);
      return {};
    }
  } catch (err) {
    console.error('taptoolsGetTokenMcap error:', err);
    return {};
  }
}

/**
 * Get RSI values for a token directly from TapTools API
 *
 * @param unit Token unit (policy + hex name)
 * @param interval Time interval (e.g., '1h', '4h', '1d')
 * @param length RSI period length (default: 14)
 * @param items Number of items to return (default: 30)
 * @returns Array of RSI values
 */
export async function taptoolsGetTokenRSI(
  unit: string,
  interval: string = '1d',
  length: number = 14,
  items: number = 30
): Promise<any[]> {
  try {
    const url = 'https://openapi.taptools.io/api/v1/token/indicators';
    const resp = await axios.get(url, {
      headers: {
        accept: 'application/json',
        'X-API-Key': TAP_TOOLS_API_KEY!
      },
      params: {
        unit,
        interval,
        indicator: 'rsi',
        length,
        items,
        quote: 'ADA'
      }
    });

    if (resp.status === 200) {
      console.log(`[INDICATORS] Successfully fetched RSI data for ${unit}`);
      return resp.data;
    } else {
      console.error(`Error ${resp.status} fetching RSI for ${unit}:`, resp.data);
      return [];
    }
  } catch (err) {
    console.error('taptoolsGetTokenRSI error:', err);
    return [];
  }
}

///////////////////////////////////////////
// 6) Professional Safety Checks
///////////////////////////////////////////

/**
 * Professional trader safety check - prevents trading obvious bad setups
 */
function performProfessionalSafetyCheck(decision: any, tokenData: any): { isSafe: boolean; reason: string } {
  // Check for extremely low liquidity
  const volume24h = tokenData.volume_24h || 0;
  if (volume24h < 5000) {
    return {
      isSafe: false,
      reason: `Extremely low 24h volume (${volume24h} ADA) - liquidity risk too high`
    };
  }

  // Check for pump and dump characteristics
  if (tokenData.ohlcv && tokenData.ohlcv.length > 0) {
    const prices = tokenData.ohlcv.map((candle: any) =>
      candle.close || candle.c || (Array.isArray(candle) ? candle[4] : 0)
    );

    if (prices.length > 1) {
      const maxPrice = Math.max(...prices);
      const minPrice = Math.min(...prices);
      const currentPrice = prices[prices.length - 1];

      // If current price is less than 25% of recent high, it's likely a dump
      if (currentPrice < maxPrice * 0.25) {
        return {
          isSafe: false,
          reason: `Price is ${((currentPrice / maxPrice) * 100).toFixed(1)}% of recent high - likely post-dump`
        };
      }
    }
  }

  // Check for very low confidence with buy recommendation
  if (decision.recommendation === 'buy' && decision.confidence <= 5) {
    return {
      isSafe: false,
      reason: `Confidence too low (${decision.confidence}/10) for buy recommendation`
    };
  }

  // Check for contradictory analysis - EXPANDED LIST
  const summary = decision.summary?.toLowerCase() || '';
  const reasoning = JSON.stringify(decision.reasoning || {}).toLowerCase();
  const allText = summary + ' ' + reasoning;

  const badSignals = [
    'severe', 'crash', 'collapse', 'dump', 'falling knife',
    'extremely poor', 'major red flag', 'exceptionally high-risk',
    'overwhelmingly bearish', 'complete failure', 'bubble pop',
    'failed launch', 'high-risk token', 'poor liquidity', 'total loss',
    'non-existent liquidity', 'maximum risk', 'purely speculative',
    'could easily collapse', 'extreme volatility', 'no sustained interest'
  ];

  if (decision.recommendation === 'buy') {
    for (const signal of badSignals) {
      if (allText.includes(signal)) {
        return {
          isSafe: false,
          reason: `Analysis contains warning "${signal}" but recommends buying - contradictory`
        };
      }
    }
  }

  // TECHNICAL TIMING CHECK - Don't buy into resistance
  const technicalTiming = checkTechnicalTiming(decision, tokenData);
  if (!technicalTiming.goodTiming) {
    return {
      isSafe: false,
      reason: technicalTiming.reason
    };
  }

  return { isSafe: true, reason: 'Passes professional safety checks' };
}

/**
 * Check technical timing - sophisticated Fibonacci-based entry logic
 */
function checkTechnicalTiming(decision: any, tokenData: any): { goodTiming: boolean; reason: string } {
  if (!tokenData.ohlcv || tokenData.ohlcv.length < 5) {
    return { goodTiming: true, reason: 'Insufficient data - allowing trade' };
  }

  const prices = tokenData.ohlcv.map((candle: any) =>
    candle.close || candle.c || (Array.isArray(candle) ? candle[4] : 0)
  );

  const currentPrice = prices[prices.length - 1];
  const recentHigh = Math.max(...prices.slice(-20)); // Last 20 candles for better context
  const recentLow = Math.min(...prices.slice(-20));
  const swingRange = recentHigh - recentLow;

  // Calculate Fibonacci levels
  const fib236 = recentHigh - (swingRange * 0.236);
  const fib382 = recentHigh - (swingRange * 0.382);
  const fib500 = recentHigh - (swingRange * 0.500);
  const fib618 = recentHigh - (swingRange * 0.618);
  const fib786 = recentHigh - (swingRange * 0.786);

  // Distance from recent high
  const distanceFromHigh = ((recentHigh - currentPrice) / recentHigh) * 100;

  // SOPHISTICATED FIBONACCI LOGIC

  // 1. If we're in a strong uptrend and at good Fib levels, allow entry
  const summary = decision.summary?.toLowerCase() || '';
  const isUptrend = summary.includes('uptrend') || summary.includes('bullish') || summary.includes('breakout');

  if (isUptrend) {
    // In uptrend, allow entries at Fib retracements
    if (currentPrice <= fib382 && currentPrice >= fib618) {
      return { goodTiming: true, reason: `Good Fib entry: price at ${distanceFromHigh.toFixed(1)}% pullback (38.2%-61.8% zone)` };
    }

    // Allow breakout entries (within 3% of high)
    if (distanceFromHigh <= 3 && summary.includes('breakout')) {
      return { goodTiming: true, reason: `Breakout entry: ${distanceFromHigh.toFixed(1)}% from high with breakout confirmation` };
    }
  }

  // 2. If analysis mentions specific Fib levels, respect them
  if (summary.includes('fibonacci') || summary.includes('retracement')) {
    if (currentPrice <= fib500) {
      return { goodTiming: true, reason: 'At Fibonacci support level mentioned in analysis' };
    }
  }

  // 3. Volume confirmation for entries
  if (tokenData.volume_24h && tokenData.volume_24h < 2000) {
    return {
      goodTiming: false,
      reason: `Volume too low (${tokenData.volume_24h} ADA) - need volume confirmation`
    };
  }

  // 4. Don't buy if we're too close to highs WITHOUT breakout confirmation
  if (distanceFromHigh < 2 && !summary.includes('breakout') && !summary.includes('break')) {
    return {
      goodTiming: false,
      reason: `Too close to recent high (${distanceFromHigh.toFixed(1)}% below) without breakout - wait for pullback or confirmation`
    };
  }

  // 5. Don't buy if price is in free fall (>15% drop in last 5 candles)
  if (prices.length >= 5) {
    const last5 = prices.slice(-5);
    const fallPercentage = ((last5[0] - last5[4]) / last5[0]) * 100;
    if (fallPercentage > 15) {
      return {
        goodTiming: false,
        reason: `Price in free fall (${fallPercentage.toFixed(1)}% drop in 5 candles) - wait for stabilization`
      };
    }
  }

  // 6. If analysis explicitly says "wait", respect it
  if (summary.includes('wait for pullback') || summary.includes('patience')) {
    return {
      goodTiming: false,
      reason: 'Analysis explicitly suggests waiting for better entry'
    };
  }

  // Default: Allow the trade if no red flags
  return { goodTiming: true, reason: 'Technical timing acceptable' };
}

/**
 * Final coherence check - block terrible trades and contradictions
 */
function performFinalCoherenceCheck(decision: any): { isCoherent: boolean; reason: string } {
  // If recommendation is hold, it should never be traded
  if (decision.recommendation === 'hold') {
    return { isCoherent: true, reason: 'Hold recommendation - no trade' };
  }

  if (decision.recommendation !== 'buy') {
    return { isCoherent: true, reason: 'Not a buy recommendation' };
  }

  const allText = JSON.stringify(decision).toLowerCase();

  // Block SEVERE contradictions - expanded list
  const severeRedFlags = [
    'pump-and-dump', 'pump and dump', 'failed launch', 'total loss',
    'rug pull', 'scam', 'avoid at all costs', 'extremely dangerous',
    'complete failure', 'bubble pop', 'crashed', 'collapsed over',
    'zero volume', 'no liquidity', 'impossible to trade',
    'high-risk token', 'critically high', 'extreme volatility',
    'total loss of capital', 'practically untradeable', 'market failure',
    'maximum volatility', 'maximum risk', 'critically low',
    'extremely poor', 'poor liquidity', 'non-existent liquidity',
    'massive slippage', 'untradeable', 'market failure'
  ];

  for (const flag of severeRedFlags) {
    if (allText.includes(flag)) {
      return {
        isCoherent: false,
        reason: `Contains severe red flag "${flag}" but recommends buying - INCOHERENT`
      };
    }
  }

  // Check for confidence vs recommendation mismatch
  if (decision.confidence <= 3 && decision.recommendation === 'buy') {
    return {
      isCoherent: false,
      reason: `Extremely low confidence (${decision.confidence}/10) contradicts buy recommendation`
    };
  }

  // Additional check: if direction is 'buy' but recommendation is not 'buy'
  if (decision.direction === 'buy' && decision.recommendation !== 'buy') {
    return {
      isCoherent: false,
      reason: `Direction is 'buy' but recommendation is '${decision.recommendation}' - CONTRADICTORY`
    };
  }

  return { isCoherent: true, reason: 'Final coherence check passed' };
}

///////////////////////////////////////////
// 7) Twitter & News Integration
///////////////////////////////////////////

/**
 * Fetch Twitter sentiment and news data for a token
 */
async function enhanceTokenDataWithSentiment(tokenData: any): Promise<any> {
  try {
    console.log(`[TWITTER] Fetching sentiment data for ${tokenData.ticker}`);

    // Hardcoded Twitter usernames for known tokens
    const hardcodedTwitter: { [key: string]: string } = {
      'SNEK': 'snek',
      'WMTX': 'WorldMobileTeam',
      'MIN': 'MinswapDEX',
      'FLOW': 'FlowHydration',
      'MILK': 'MuesliSwapTeam'
    };

    let twitterUsername: string | null = null;

    // Check if we have a hardcoded username first
    if (hardcodedTwitter[tokenData.ticker]) {
      twitterUsername = hardcodedTwitter[tokenData.ticker];
      console.log(`[TWITTER] Using hardcoded username for ${tokenData.ticker}: @${twitterUsername}`);
    } else {
      // Step 1: Get token social links from TapTools
      const socialLinks = await getTokenSocialLinks(tokenData.unit);

      if (socialLinks?.twitter) {
        // Step 2: Extract Twitter username from URL
        twitterUsername = extractTwitterUsername(socialLinks.twitter);
        console.log(`[TWITTER] Extracted username from TapTools for ${tokenData.ticker}: @${twitterUsername}`);
      } else {
        console.log(`[TWITTER] ⚠️ No Twitter profile found for ${tokenData.ticker} in TapTools`);
      }
    }

    if (twitterUsername) {
      // Step 3: Fetch recent tweets
      const tweets = await fetchRecentTweets(twitterUsername, 10);

      if (tweets && tweets.length > 0) {
        // Step 4: Analyze sentiment
        const sentimentAnalysis = analyzeTweetSentiment(tweets);

        // Step 5: Add to token data
        tokenData.sentiment_data = {
          twitter_username: twitterUsername,
          recent_tweets: tweets,
          sentiment_analysis: sentimentAnalysis,
          last_updated: new Date().toISOString()
        };

        console.log(`[TWITTER] ✅ Added sentiment data for ${tokenData.ticker} (@${twitterUsername})`);
      } else {
        console.log(`[TWITTER] ⚠️ No tweets found for ${tokenData.ticker} (@${twitterUsername})`);
      }
    } else {
      console.log(`[TWITTER] ⚠️ Could not find Twitter username for ${tokenData.ticker}`);
    }
  } catch (error) {
    console.error(`[TWITTER] ❌ Error fetching sentiment for ${tokenData.ticker}:`, error instanceof Error ? error.message : error);
  }

  return tokenData;
}

/**
 * Get token social links from TapTools API
 */
async function getTokenSocialLinks(unit: string): Promise<any> {
  const TAPTOOLS_API_KEY = process.env.TAPTOOLS_API_KEY || 'WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO';
  const url = `https://openapi.taptools.io/api/v1/token/links?unit=${unit}`;

  const response = await fetch(url, {
    headers: {
      'x-api-key': TAPTOOLS_API_KEY,
      'Accept': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`TapTools API Error: ${response.status}`);
  }

  return response.json();
}

/**
 * Extract Twitter username from various Twitter URL formats
 */
function extractTwitterUsername(twitterUrl: string): string | null {
  if (!twitterUrl) return null;

  // Handle various Twitter URL formats
  const patterns = [
    /twitter\.com\/([^\/\?]+)/i,
    /x\.com\/([^\/\?]+)/i,
    /@([a-zA-Z0-9_]+)/
  ];

  for (const pattern of patterns) {
    const match = twitterUrl.match(pattern);
    if (match && match[1]) {
      return match[1].replace('@', '');
    }
  }

  return null;
}

/**
 * Fetch recent tweets from your Twitter server
 */
async function fetchRecentTweets(username: string, limit: number = 10): Promise<any[]> {
  const url = `https://twitscap-production.up.railway.app/api/user_tweets/${username}?limit=${limit}`;

  const response = await fetch(url, {
    headers: {
      'Accept': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`Twitter API Error: ${response.status}`);
  }

  const data = await response.json();
  return data.tweets || [];
}

/**
 * Analyze sentiment from tweets with sophisticated catalyst detection
 */
function analyzeTweetSentiment(tweets: any[]): any {
  if (!tweets || tweets.length === 0) {
    return { overall_sentiment: 'neutral', confidence: 0, tweet_count: 0 };
  }

  let positiveCount = 0;
  let negativeCount = 0;
  let neutralCount = 0;
  let catalysts: any[] = [];

  // Enhanced sentiment analysis
  const sentimentIndicators = {
    // Strong positive indicators
    strongPositive: [
      'partnership', 'collaboration', 'secured', 'milestone', 'launched', 'live',
      'rewards', 'earnings', 'adoption', 'users', 'growth', 'expansion',
      'breakthrough', 'achievement', 'success', 'winner', 'champion',
      'revolutionary', 'game-changing', 'first', 'leading', 'pioneer'
    ],
    // Moderate positive
    positive: [
      'bullish', 'moon', 'pump', 'buy', 'hodl', 'gem', 'rocket', '🚀', '📈',
      'ath', 'breakout', 'surge', 'up', 'gain', 'profit', 'win', 'good',
      'great', 'amazing', 'awesome', 'excited', 'optimistic'
    ],
    // Negative indicators
    negative: [
      'bearish', 'dump', 'sell', 'crash', 'rekt', 'rug', 'scam', '📉',
      'dip', 'correction', 'bear', 'down', 'loss', 'fail', 'problem',
      'issue', 'concern', 'worry', 'risk', 'danger'
    ],
    // Catalyst keywords
    catalysts: [
      'announcement', 'news', 'update', 'launch', 'release', 'partnership',
      'collaboration', 'integration', 'expansion', 'milestone', 'achievement',
      'rewards', 'earnings', 'distribution', 'airdrop', 'listing', 'exchange'
    ]
  };

  tweets.forEach((tweet, index) => {
    const text = (tweet.content || tweet.text || '').toLowerCase();
    const metrics = tweet.metrics || {};

    let sentimentScore = 0;
    let catalystDetected = false;

    // Check for strong positive indicators (weight: 3)
    sentimentIndicators.strongPositive.forEach(word => {
      if (text.includes(word)) {
        sentimentScore += 3;
      }
    });

    // Check for moderate positive indicators (weight: 1)
    sentimentIndicators.positive.forEach(word => {
      if (text.includes(word)) {
        sentimentScore += 1;
      }
    });

    // Check for negative indicators (weight: -2)
    sentimentIndicators.negative.forEach(word => {
      if (text.includes(word)) {
        sentimentScore -= 2;
      }
    });

    // Check for catalysts
    sentimentIndicators.catalysts.forEach(word => {
      if (text.includes(word)) {
        catalystDetected = true;
      }
    });

    // Factor in engagement metrics
    const engagementScore = (metrics.likes || 0) + (metrics.retweets || 0) * 2 + (metrics.replies || 0);
    if (engagementScore > 100) sentimentScore += 1; // High engagement = more important
    if (engagementScore > 500) sentimentScore += 1; // Very high engagement

    // Determine sentiment
    let sentiment = 'neutral';
    if (sentimentScore >= 3) {
      sentiment = 'very_positive';
      positiveCount++;
    } else if (sentimentScore >= 1) {
      sentiment = 'positive';
      positiveCount++;
    } else if (sentimentScore <= -2) {
      sentiment = 'negative';
      negativeCount++;
    } else {
      sentiment = 'neutral';
      neutralCount++;
    }

    tweet.sentiment = sentiment;
    tweet.sentiment_score = sentimentScore;

    // Extract catalysts
    if (catalystDetected || sentimentScore >= 3) {
      catalysts.push({
        text: text.substring(0, 150),
        sentiment: sentiment,
        engagement: engagementScore,
        date: tweet.date || tweet.created_at,
        is_catalyst: catalystDetected
      });
    }
  });

  // Calculate overall sentiment
  const total = tweets.length;
  const positiveRatio = positiveCount / total;
  const negativeRatio = negativeCount / total;

  let overallSentiment = 'neutral';
  let confidence = 0;

  if (positiveRatio > 0.7) {
    overallSentiment = 'very_bullish';
    confidence = positiveRatio;
  } else if (positiveRatio > 0.5) {
    overallSentiment = 'bullish';
    confidence = positiveRatio;
  } else if (negativeRatio > 0.5) {
    overallSentiment = 'bearish';
    confidence = negativeRatio;
  } else if (positiveRatio > negativeRatio) {
    overallSentiment = 'slightly_bullish';
    confidence = positiveRatio;
  } else if (negativeRatio > positiveRatio) {
    overallSentiment = 'slightly_bearish';
    confidence = negativeRatio;
  }

  // Extract key insights
  const recentCatalysts = catalysts.slice(0, 3);
  const avgEngagement = tweets.reduce((sum, t) => {
    const metrics = t.metrics || {};
    return sum + ((metrics.likes || 0) + (metrics.retweets || 0) + (metrics.replies || 0));
  }, 0) / tweets.length;

  return {
    overall_sentiment: overallSentiment,
    confidence: confidence,
    tweet_count: total,
    positive_tweets: positiveCount,
    negative_tweets: negativeCount,
    neutral_tweets: neutralCount,
    catalysts: recentCatalysts,
    avg_engagement: Math.round(avgEngagement),
    key_insights: extractKeyInsights(tweets, catalysts),
    recent_tweets_summary: tweets.slice(0, 3).map(t => ({
      text: (t.content || t.text || '').substring(0, 120) + '...',
      sentiment: t.sentiment,
      engagement: (t.metrics?.likes || 0) + (t.metrics?.retweets || 0),
      date: t.date || t.created_at
    }))
  };
}

/**
 * Extract key insights from tweets
 */
function extractKeyInsights(tweets: any[], catalysts: any[]): string[] {
  const insights: string[] = [];

  // Check for partnership announcements
  const partnerships = tweets.filter(t =>
    (t.content || t.text || '').toLowerCase().includes('partnership') ||
    (t.content || t.text || '').toLowerCase().includes('collaboration')
  );
  if (partnerships.length > 0) {
    insights.push(`${partnerships.length} partnership/collaboration announcement(s) detected`);
  }

  // Check for product launches
  const launches = tweets.filter(t =>
    (t.content || t.text || '').toLowerCase().includes('launch') ||
    (t.content || t.text || '').toLowerCase().includes('live') ||
    (t.content || t.text || '').toLowerCase().includes('released')
  );
  if (launches.length > 0) {
    insights.push(`${launches.length} product launch/release announcement(s)`);
  }

  // Check for user/adoption metrics
  const adoptionTweets = tweets.filter(t => {
    const text = (t.content || t.text || '').toLowerCase();
    return text.includes('users') || text.includes('adoption') || text.includes('million');
  });
  if (adoptionTweets.length > 0) {
    insights.push('User adoption metrics mentioned');
  }

  // Check for rewards/earnings
  const rewardTweets = tweets.filter(t => {
    const text = (t.content || t.text || '').toLowerCase();
    return text.includes('reward') || text.includes('earning') || text.includes('claim');
  });
  if (rewardTweets.length > 0) {
    insights.push('Reward/earnings distribution activity');
  }

  // High engagement indicator
  const highEngagementTweets = tweets.filter(t => {
    const metrics = t.metrics || {};
    return (metrics.likes || 0) + (metrics.retweets || 0) > 200;
  });
  if (highEngagementTweets.length > 0) {
    insights.push(`${highEngagementTweets.length} high-engagement tweet(s) (>200 interactions)`);
  }

  return insights;
}

///////////////////////////////////////////
// 8) LLM Decision
///////////////////////////////////////////
async function getLlmTradingDecision(tokenData: any, portfolioHoldings?: any): Promise<LlmDecision> {
  try {
    const systemPrompt = `
        bio: ${JSON.stringify(AGENT_BIO)}
        lore: ${JSON.stringify(AGENT_LORE)}

    Data Analysis Guidelines:
    ${Object.entries(ANALYSIS_GUIDELINES)
      .map(([category, guidelines]) =>
        `${category}:\n${guidelines.map(g => `       - ${g}`).join('\n')}`
      ).join('\n\n')}

    Your job:
      - Evaluate ALL available token data with PROFESSIONAL TRADER STANDARDS
      - Consider market structure, liquidity, and technical factors
      - Decide whether to buy, sell, or hold the token on a Cardano DEX
      - IMPORTANT: These are SPOT tokens on Cardano DEXs, NOT perpetual futures
      - You can only BUY tokens if you have ADA available
      - You CANNOT short tokens (except SNEK which has special shorting capability)

      ENHANCED SELL SIGNAL LOGIC:
      - Recommend SELL when technical indicators suggest distribution/exit points, even if not currently holding
      - SELL signals help investors trim existing positions or avoid new entries at poor risk/reward levels
      - Key SELL triggers: price at recent highs + declining volume, RSI >85 (overbought), bearish divergences
      - Factor in Twitter sentiment: very bullish news/catalysts may override technical sell signals → recommend HOLD
      - SELL recommendations provide early warning signals about potential reversals for better re-entry timing

      PROFESSIONAL TRADING RULES:
      - NEVER recommend buying tokens with SEVERE problems (pump-and-dump, failed launches, zero liquidity)
      - Confidence must be 6+ for any buy recommendation (professional minimum)
      - Be COHERENT: Don't say "total loss" then recommend buying
      - Use Fibonacci analysis: 38.2%-61.8% pullbacks in uptrends are good entries
      - Breakouts above resistance with volume are good entries
      - Don't buy into free-falling prices (>15% drop recently)
      - Don't buy with extremely low volume (<2000 ADA/24h)
      - Consider market structure: uptrends allow higher entries, downtrends require deeper pullbacks
      - BE PROFESSIONAL: Allow good setups, block obvious traps

      ENHANCED SELL SIGNAL RULES:
      - Recommend SELL at distribution zones: recent highs + declining volume + RSI >85
      - SELL on bearish divergences: price makes higher highs while RSI/volume makes lower highs
      - SELL on parabolic moves: unsustainable price spikes (>50% in 24h) with exhaustion signals
      - Twitter sentiment override: very bullish catalysts (partnerships, launches) may justify HOLD over SELL
      - Provide SELL signals for risk management, even if not currently holding the token

      MULTI-TIMEFRAME CONFLUENCE ANALYSIS:
      - Use multiTimeframe data to confirm signals across 15m, 1h, and 4h timeframes
      - High confluence (>70%) strengthens signal confidence
      - Low confluence (<50%) suggests caution and lower position sizes
      - Timeframe agreement is crucial for high-confidence trades

      RELATIVE STRENGTH ANALYSIS:
      - Factor in relativeStrength vs ADA performance
      - Strong outperformance (>10% vs ADA) supports buy decisions
      - Underperformance (<-2% vs ADA) suggests weakness
      - Consider ADA trend when evaluating relative strength

      - Provide comprehensive reasoning in JSON

    Format your response in JSON:
    ${JSON.stringify(EXPECTED_RESPONSE_FORMAT, null, 2)}
    CRITICAL FORMATTING REQUIREMENTS:
    - Return ONLY valid JSON, no markdown formatting
    - Do NOT use code blocks or backticks
    - Do NOT include any text before or after the JSON
    - The 'recommendation' field must be exactly 'buy', 'sell', or 'hold'
    - You must include a 'direction' field that is exactly 'buy' or 'sell'
    - Ensure all strings are properly quoted and escaped
    - End with a complete closing brace }

    PRICING PRECISION REQUIREMENTS:
    - ALL target prices must be specified to exactly 4 decimal places (e.g., "0.2847", "1.2345")
    - ALL stop loss prices must be specified to exactly 4 decimal places (e.g., "0.2534", "1.1892")
    - Use precise technical analysis to calculate exact entry/exit levels
    - Consider support/resistance levels, Fibonacci retracements, and volume profiles for precise pricing
    - Example: Instead of "0.29" use "0.2847", instead of "0.25" use "0.2534"
    `;

    const userPrompt = `
    Available Data Fields:
    ${Object.entries(DATA_FIELDS)
      .map(([category, fields]) => `- ${category}: ${fields.join(', ')}`)
      .join('\n')}

    Consider ALL data points when making your decision:
    ${ANALYSIS_STEPS.map((step, i) => `${i + 1}. ${step}`).join('\n')}

    Provide a detailed analysis and trading decision.

    CURRENT PORTFOLIO CONTEXT:
    ${portfolioHoldings ? `You currently own: ${JSON.stringify(portfolioHoldings, null, 2)}` : 'You currently own no tokens (only ADA)'}

    TRADING CONSTRAINTS:
    - You can only SELL tokens you currently own
    - You can only BUY tokens if you have ADA available
    - If bearish on a token you don't own, recommend 'hold' not 'sell'
    - EXCEPTION: SNEK token supports shorting (you can sell SNEK even if you don't own it)
    - For all other tokens: only buy/hold decisions if you don't own them

    SENTIMENT ANALYSIS INTEGRATION (CRITICAL):
    - ALWAYS check for sentiment_data in the token data
    - If Twitter sentiment is available, MUST include it in fundamentalFactors.news section
    - Bullish Twitter sentiment (especially with catalysts) strongly supports buy decisions
    - Bearish Twitter sentiment should increase caution and lower confidence
    - Recent project tweets may indicate upcoming catalysts or issues
    - Include specific insights from Twitter analysis in your reasoning
    - Factor sentiment confidence level into your overall confidence score

    Analyze the following token data and respond in strict JSON:
    ${JSON.stringify(tokenData, null, 2)}

    ${tokenData.sentiment_data ? `
    🐦 TWITTER SENTIMENT ANALYSIS - MANDATORY TO INCLUDE IN YOUR ANALYSIS:
    Overall Sentiment: ${tokenData.sentiment_data.sentiment_analysis?.overall_sentiment || 'N/A'}
    Confidence: ${tokenData.sentiment_data.sentiment_analysis?.confidence || 'N/A'}
    Recent Catalysts: ${tokenData.sentiment_data.sentiment_analysis?.catalysts?.length || 0}
    Key Insights: ${tokenData.sentiment_data.sentiment_analysis?.key_insights?.join(', ') || 'None'}

    CRITICAL: You MUST mention this Twitter sentiment in your fundamentalFactors.news and fundamentalFactors.sentiment sections.
    Do NOT ignore this data - it's crucial for the analysis!
    ` : ''}
    `;

    // Use our new LLM service instead of direct OpenAI call
    return await llmService.getTradingDecision(systemPrompt, userPrompt, tokenData);
  } catch (err) {
    console.error('LLM Decision Error:', err);
    return {
      trade: 'false',
      direction: 'buy',
      confidence: 0,
      size: 0,
      reasoning: {
        price_analysis: 'LLM error',
        volume_analysis: '',
        risk_assessment: '',
        confidence_explanation: '',
        size_explanation: ''
      }
    };
  }
}

///////////////////////////////////////////
// 7) Portfolio Logic
///////////////////////////////////////////

/**
 * Check if the user has enough of a specific token in their wallet
 * @param addressInfo - The wallet address info from TapTools
 * @param unit - The token unit to check
 * @param minQuantity - The minimum quantity required (1 is the default)
 * @returns boolean - Whether the user has enough tokens
 */
function hasEnoughTokens(addressInfo: Record<string, any> | null, unit: string, minQuantity: number = 1): boolean {
  if (!addressInfo || !addressInfo.assets) {
    return false;
  }

  // Find the asset in the user's wallet
  const asset = addressInfo.assets.find((a: any) => a.unit === unit);

  // Check if the asset exists and has a sufficient quantity
  if (asset && parseInt(asset.quantity, 10) >= minQuantity) {
    console.log(`[PORTFOLIO] Found ${asset.quantity} units of token ${unit} in wallet`);
    return true;
  }

  console.log(`[PORTFOLIO] Token ${unit} not found in wallet or insufficient quantity`);
  return false;
}

/**
 * Advanced portfolio management with dynamic position sizing
 * based on risk category, token age, and market conditions.
 */
function adjustTradeForPortfolio(
  direction: 'buy' | 'sell',
  category: Category,
  recommendedAda: number,
  totalAdaValue: number,
  currentCategoryValue: number,
  userAdaBalance: number,
  addressInfo: Record<string, any> | null = null,
  unit: string = '',
  tokenData: any = null,
  confidence: number = 5,
  ticker: string = ''
): number {
  // Define maximum percentage of ADA balance to use for any trade
  // Use environment variable if available, otherwise default to 50%
  const MAX_ADA_PERCENTAGE = process.env.MAX_ADA_TRADE_PERCENTAGE
    ? parseFloat(process.env.MAX_ADA_TRADE_PERCENTAGE) / 100
    : 0.5; // Default: 50% of available balance

  // Define minimum ADA to keep for fees and future transactions
  // Use environment variable if available, otherwise default to 5 ADA
  const MIN_ADA_RESERVE = process.env.MIN_ADA_RESERVE
    ? parseFloat(process.env.MIN_ADA_RESERVE)
    : 5; // Default: Always keep at least 5 ADA in wallet

  // Calculate maximum ADA amount that can be used for trading
  const maxTradeableAda = Math.max(0, userAdaBalance - MIN_ADA_RESERVE) * MAX_ADA_PERCENTAGE;

  // Get historical trades for this token
  const tokenTrades = ticker ? getTradesByToken(ticker) : [];

  // Determine risk category if we have token data
  let riskCategory = TokenRiskCategory.MEDIUM; // Default to medium risk
  if (tokenData) {
    riskCategory = determineRiskCategory(tokenData, tokenTrades);
    console.log(`[RISK] Token ${ticker || unit} risk category: ${riskCategory}`);
  }

  if (direction === 'buy') {
    // First check if we even have enough ADA for a meaningful trade
    if (maxTradeableAda < 2) {
      console.log(`[PORTFOLIO] Not enough ADA available for trading. Available: ${userAdaBalance} ADA, Tradeable: ${maxTradeableAda} ADA`);
      return 0;
    }

    const maxAllowedCategory = targetRatios[category] * totalAdaValue;
    const roomLeft = maxAllowedCategory - currentCategoryValue;
    if (roomLeft <= 0) {
      console.log(`[PORTFOLIO] Category ${category} is at/over target ratio. Skipping buy.`);
      return 0;
    }

    // Calculate dynamic position size based on risk category
    const dynamicSize = calculateDynamicPositionSize(
      recommendedAda,
      totalAdaValue, // portfolio value
      { confidence: confidence }, // decision object
      { ticker: ticker, riskCategory: riskCategory }, // token data
      riskCategory
    );

    // We can't exceed what's left in the category target
    let finalAda = Math.min(dynamicSize, roomLeft);

    // Special handling for new high-volume tokens
    if (tokenData && isHighVolumeToken(tokenData) &&
        determineTokenAge(tokenData, tokenTrades) === TokenAgeCategory.NEW) {
      console.log(`[PORTFOLIO] New high-volume token detected: ${ticker || unit}`);

      // For new high-volume tokens, we use a smaller position size but ensure it's meaningful
      finalAda = Math.min(finalAda, maxTradeableAda * 0.3); // Max 30% of tradeable ADA
      finalAda = Math.max(finalAda, 10); // Ensure at least 10 ADA for high-volume new tokens

      console.log(`[PORTFOLIO] Adjusted position size for new high-volume token: ${finalAda} ADA`);
    }

    // Round to whole ADA values
    finalAda = Math.round(finalAda);

    console.log(`[PORTFOLIO] Trade calculation:
      - Recommended by AI: ${recommendedAda} ADA
      - Risk-adjusted size: ${dynamicSize} ADA
      - Category room left: ${roomLeft} ADA
      - Max tradeable (${MAX_ADA_PERCENTAGE * 100}% of balance): ${maxTradeableAda} ADA
      - Risk category: ${riskCategory}
      - Final trade amount: ${finalAda} ADA`);

    if (finalAda < 1) {
      console.log(`[PORTFOLIO] finalAda < 1, skipping buy.`);
      return 0;
    }

    return finalAda;
  } else {
    // For sells, check if the user actually has the token they're trying to sell
    if (addressInfo && unit) {
      if (!hasEnoughTokens(addressInfo, unit)) {
        console.log(`[PORTFOLIO] Cannot sell token ${unit} - not found in wallet or zero balance`);
        return 0;
      }

      // For high-risk tokens, especially new ones, consider selling the entire position
      let sellAmount = recommendedAda;

      if (riskCategory === TokenRiskCategory.ULTRA_HIGH || riskCategory === TokenRiskCategory.HIGH) {
        console.log(`[PORTFOLIO] High-risk token detected for sell order. Considering full position exit.`);

        // If we have token data and it's a new high-volume token, sell entire position
        if (tokenData && isHighVolumeToken(tokenData) &&
            determineTokenAge(tokenData, tokenTrades) === TokenAgeCategory.NEW) {
          console.log(`[PORTFOLIO] New high-volume token detected for sell: ${ticker || unit}. Recommending full exit.`);

          // For new high-volume tokens, we recommend selling the entire position
          // This is just a recommendation - the actual amount depends on what we have in the wallet
          sellAmount = Math.max(recommendedAda, currentCategoryValue);
        }
      }

      console.log(`[PORTFOLIO] Sell order:
        - Recommended sell value: ${recommendedAda} ADA equivalent
        - Risk-adjusted sell value: ${sellAmount} ADA equivalent
        - Category balance: ${currentCategoryValue} ADA equivalent
        - Risk category: ${riskCategory}
        - Token available in wallet: yes`);

      return sellAmount;
    } else {
      console.log(`[PORTFOLIO] Cannot verify token balance for selling - missing address info or unit`);
      return 0;
    }
  }
}

///////////////////////////////////////////
// 8) Dexter "Find Pool & Execute Swap"
///////////////////////////////////////////
async function findDexPoolForToken(unit: string): Promise<LiquidityPool | null> {
  console.log('[DEXTER] Loading pools from Iris...')
  const allPools = await iris.liquidityPools().match({tokenA: 'lovelace', tokenB: Asset.fromIdentifier(unit)});
  console.log('[DEXTER] Done loading pools from Iris...')

  const dexterPools = allPools.data
    .sort((a: IrisLiquidityPool, b: IrisLiquidityPool) => Number((b.state?.tvl ?? 0n) - (a.state?.tvl ?? 0n)))
    .map(toDexterLiquidityPool);

  if (dexterPools.length > 0) {
    return dexterPools[0];
  }

  return null;
}

function toDexterLiquidityPool(liquidityPool: IrisLiquidityPool): DexterLiquidityPool {
  let dex: string = liquidityPool.dex;

  if (dex === 'SundaeSwap') {
      dex = SundaeSwapV1.identifier;
  }

  const pool: DexterLiquidityPool = new DexterLiquidityPool(
      dex,
      toDexterToken(liquidityPool.tokenA),
      toDexterToken(liquidityPool.tokenB),
      BigInt(liquidityPool.state?.reserveA ?? 0),
      BigInt(liquidityPool.state?.reserveB ?? 0),
      liquidityPool.address,
      liquidityPool.orderAddress,
      liquidityPool.orderAddress,
  );

  pool.poolFeePercent = liquidityPool.state?.feePercent ?? 0;
  pool.identifier = liquidityPool.identifier;

  if (liquidityPool.lpToken) {
      pool.lpToken = new DexterAsset(liquidityPool.lpToken.policyId, liquidityPool.lpToken.nameHex);
  }
  if (liquidityPool.state && liquidityPool.state.lpToken) {
      pool.lpToken = new DexterAsset(liquidityPool.state.lpToken.policyId, liquidityPool.state.lpToken.nameHex);
  }

  return pool;
}

function toDexterToken(token: IrisToken): DexterToken {
  if (token === 'lovelace') return 'lovelace';

  return new DexterAsset(
      token.policyId,
      token.nameHex,
      token.decimals ?? 0,
  );
}

//TODO: remove?
interface SwapTransactionResult {
  txId: string;
  success: boolean;
  waitForConfirmation(): Promise<{ success: boolean }>;
}


function logToConsoleAndFile(message: string) {
  console.log(message);
}


async function getAssetDecimals(unit: string): Promise<number> {
  try {
    // Split the unit into policyId and nameHex
    const policyId = unit.slice(0, 56);
    const nameHex = unit.slice(56);

    const iris = new IrisApiService('https://iris.indigoprotocol.io');

    // Use the proper match method from AssetService
    const response = await iris.assets().match({
      policyId,
      nameHex
    });

    if (response.data.length > 0) {
      return response.data[0].decimals || 0;
    }

    return 0;
  } catch (err) {
    console.error(`[IRIS] Error fetching decimals for ${unit}:`, err);
    return 0;
  }
}

// Update executeDexSwap to record trades
async function executeDexSwap(
  direction: 'buy'|'sell',
  quantityAda: number,
  unit: string,
  ticker: string = '',  // Add ticker parameter
  decision?: LlmDecision  // Add decision parameter
): Promise<void> {
  try {
    // Get token price data for recording the trade
    let tokenPrice = 0;
    try {
      const priceData = await taptoolsGetTokenPrices([unit]);
      tokenPrice = priceData[unit] || 0;
    } catch (priceError) {
      console.error(`[DEXTER] Error fetching token price for ${unit}:`, priceError);
    }

    // Prepare to record the trade
    if (ticker) {
      try {
        // First check if this is a sell and we have a matching buy to update with exit information
        if (direction === 'sell') {
          // Update the exit information for the most recent buy trade of this token
          updateTradeExit(ticker, tokenPrice);
        }

        // Record the current trade with risk management information
        const tradeId = uuidv4();

        // Get historical trades for this token
        const tokenTrades = ticker ? getTradesByToken(ticker) : [];

        // Determine risk category - we don't have tokenData here, so use default
        const riskCategory = TokenRiskCategory.MEDIUM;

        // Calculate stop loss if we have price data and it's a buy
        let stopLoss = 0;
        let targetPrice = 0;
        let riskRewardRatio = 0;

        if (direction === 'buy' && tokenPrice > 0) {
          // Get support levels from decision if available
          const supportLevels: number[] = [];

          if (decision?.stopLoss) {
            const stopLossValue = typeof decision.stopLoss === 'string'
              ? parseFloat(decision.stopLoss)
              : decision.stopLoss;

            if (!isNaN(stopLossValue) && stopLossValue > 0) {
              supportLevels.push(stopLossValue);
            }
          }

          // Calculate dynamic stop loss
          stopLoss = calculateDynamicStopLoss(tokenPrice, riskCategory, supportLevels);

          // Get target price from decision if available
          if (decision?.targetPrice) {
            // Handle different formats of targetPrice
            if (typeof decision.targetPrice === 'string') {
              const parsedValue = parseFloat(decision.targetPrice);
              if (!isNaN(parsedValue) && parsedValue > 0) {
                targetPrice = parsedValue;
              }
            } else if (typeof decision.targetPrice === 'number') {
              if (decision.targetPrice > 0) {
                targetPrice = decision.targetPrice;
              }
            } else if (typeof decision.targetPrice === 'object' && decision.targetPrice !== null) {
              // Handle object format with shortTerm/longTerm
              if (decision.targetPrice.shortTerm && typeof decision.targetPrice.shortTerm === 'number') {
                targetPrice = decision.targetPrice.shortTerm;
              } else if (decision.targetPrice.longTerm && typeof decision.targetPrice.longTerm === 'number') {
                targetPrice = decision.targetPrice.longTerm;
              }
            }
          }

          // Calculate risk/reward ratio if we have both stop loss and target price
          if (stopLoss > 0 && targetPrice > 0) {
            const potentialLoss = tokenPrice - stopLoss;
            const potentialGain = targetPrice - tokenPrice;

            if (potentialLoss > 0 && potentialGain > 0) {
              riskRewardRatio = potentialGain / potentialLoss;
            }
          }
        }

        // Determine if this is a new token based on trade history
        const tokenAge = tokenTrades.length === 0 ? TokenAgeCategory.NEW :
                         tokenTrades.length < 3 ? TokenAgeCategory.RECENT :
                         TokenAgeCategory.ESTABLISHED;
        const isNewToken = tokenAge === TokenAgeCategory.NEW;

        // Determine if it's high volume based on recent trades
        // If we have more than 3 trades in the last 3 days, consider it high volume
        const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
        const recentTrades = tokenTrades.filter(t => new Date(t.timestamp) >= threeDaysAgo);
        const isHighVolume = recentTrades.length >= 3;

        // Calculate max hold time based on token age
        let maxHoldTime = 168; // 7 days default
        if (isNewToken) {
          maxHoldTime = isHighVolume ? 24 : 48; // 24 hours for new high-volume tokens, 48 for other new tokens
        } else if (tokenAge === TokenAgeCategory.RECENT) {
          maxHoldTime = 72; // 3 days for recent tokens
        }

        // Create the trade object with risk management info
        const trade: Trade = {
          id: tradeId,
          timestamp: new Date().toISOString(),
          token: unit,
          ticker: ticker,
          unit: unit,
          direction: direction,
          amount: quantityAda,
          price: tokenPrice,
          total: quantityAda, // This is in ADA, not token amount
          status: 'pending' as 'pending', // Explicitly type as 'pending'
          confidence: decision?.confidence || 0,
          llmDecision: decision || {},
          riskManagement: {
            riskCategory,
            stopLoss,
            targetPrice,
            riskRewardRatio,
            timeBasedExit: {
              maxHoldTime,
              exitTime: new Date(Date.now() + maxHoldTime * 60 * 60 * 1000).toISOString()
            }
          }
        };

        // Save the trade to the database
        saveTrade(trade);

        console.log(`[DATABASE] Recorded ${direction} trade for ${ticker} with ID ${tradeId}`);
      } catch (dbError) {
        console.error(`[DATABASE] Error recording trade:`, dbError);
        // Continue with the swap even if recording fails
      }
    }

    // Only proceed with the actual swap if we're not in test mode
    if (process.env.TEST_MODE === 'true') {
      console.log(`[TEST MODE] Would execute ${direction} of ${quantityAda} ADA for token ${unit} (${ticker})`);
      return;
    }

    // Original swap execution code
    logToConsoleAndFile(`[DEXTER] Starting swap execution for ${unit}`);
    logToConsoleAndFile(`[DEXTER] Direction: ${direction}, Amount: ${quantityAda} ADA`);

    const pool = await findDexPoolForToken(unit);
    if (!pool) {
      logToConsoleAndFile(`[DEXTER] ❌ No valid pool found for ${unit} / ADA`);

      // Update trade status to failed if we were tracking it
      if (ticker) {
        try {
          // Find the most recent trade for this token with status = 'pending'
          const trades = getAllTrades();
          const pendingTradeIndex = [...trades].reverse().findIndex(
            t => (t.ticker === ticker || t.token === unit) && t.status === 'pending'
          );

          if (pendingTradeIndex !== -1) {
            const actualIndex = trades.length - 1 - pendingTradeIndex;
            const trade = trades[actualIndex];

            // Update the trade status to failed
            const updatedTrade: Trade = {
              ...trade,
              status: 'failed' as 'failed'
            };

            // Save the updated trade
            saveTrade(updatedTrade);

            console.log(`[DATABASE] Updated trade ${trade.id} for ${ticker} with status: failed (no pool found)`);
          }
        } catch (dbError) {
          console.error(`[DATABASE] Error updating trade status:`, dbError);
        }
      }
      return;
    }

    // Get decimals for the token
    const decimals = await getAssetDecimals(unit);
    logToConsoleAndFile(`[DEXTER] Token decimals: ${decimals}`);

    logToConsoleAndFile(`[DEXTER] ✓ Found pool: ${pool.identifier}`);
    logToConsoleAndFile(`[DEXTER] Pool details: ${JSON.stringify({
      assetA: pool.assetA.toString(),
      assetB: pool.assetB.toString(),
      dex: pool.dex
    }, null, 2)}`);

    try {
      const swapReq: SwapRequest = dexter.newSwapRequest()
        .forLiquidityPool(pool)
        .withSlippagePercent(2.0);

      if (direction === 'buy') {
        logToConsoleAndFile('[DEXTER] Configuring buy: ADA → Token');
        swapReq
          .withSwapInToken('lovelace')
          .withSwapInAmount(BigInt(quantityAda * 1_000_000));
        logToConsoleAndFile(`[DEXTER] Set swap in: ${quantityAda} ADA (${quantityAda * 1_000_000} lovelace)`);
      } else {
        logToConsoleAndFile('[DEXTER] Configuring sell: Token → ADA');
        swapReq
          .withSwapOutToken('lovelace')
          .withSwapOutAmount(BigInt(quantityAda * 1_000_000));
        logToConsoleAndFile(`[DEXTER] Set swap out: ${quantityAda} ADA (${quantityAda * 1_000_000} lovelace)`);
      }

      logToConsoleAndFile('[DEXTER] Submitting swap request...');

      try {
        const tx = swapReq.submit();

        tx
          .onBuilding(() => {
            logToConsoleAndFile('[DEXTER] Building swap order...');
          })
          .onSubmitted((dexterTx: DexTransaction) => {
            logToConsoleAndFile(`[DEXTER] ✓ Swap TX Submitted: ${dexterTx.hash}`);

            // Update the trade with transaction hash if we were tracking it
            if (ticker) {
              try {
                // Find the most recent trade for this token with status = 'pending'
                const trades = getAllTrades();
                const pendingTradeIndex = [...trades].reverse().findIndex(
                  t => (t.ticker === ticker || t.token === unit) && t.status === 'pending'
                );

                if (pendingTradeIndex !== -1) {
                  const actualIndex = trades.length - 1 - pendingTradeIndex;
                  const trade = trades[actualIndex];

                  // Update the trade status
                  const updatedTrade: Trade = {
                    ...trade,
                    status: 'completed' as 'completed',
                    txHash: dexterTx.hash
                  };

                  // Save the updated trade
                  saveTrade(updatedTrade);

                  console.log(`[DATABASE] Updated trade ${trade.id} for ${ticker} with status: completed`);
                }
              } catch (dbError) {
                console.error(`[DATABASE] Error updating trade status:`, dbError);
              }
            }

            // Send Discord notification with transaction hash
            if (ticker) {
              if (decision) {
                // If we have a decision with analysis, use the enhanced notification
                // Find the trade we just created to get its risk management info
                const trades = getAllTrades();
                const recentTrade = [...trades].reverse().find(
                  t => (t.ticker === ticker || t.token === unit) &&
                       t.direction === direction &&
                       t.txHash === dexterTx.hash
                );

                // Transaction notification already sent before execution
                // Just log the successful transaction hash
                console.log(`[DEXTER] ✅ Transaction successful: ${dexterTx.hash}`);
              }
            }
          })
          .onError((dexterTx: DexTransaction) => {
            logToConsoleAndFile(`[DEXTER] ❌ ${dexterTx.error?.reasonRaw}`);

            // Update the trade as failed if we were tracking it
            if (ticker) {
              try {
                // Find the most recent trade for this token with status = 'pending'
                const trades = getAllTrades();
                const pendingTradeIndex = [...trades].reverse().findIndex(
                  t => (t.ticker === ticker || t.token === unit) && t.status === 'pending'
                );

                if (pendingTradeIndex !== -1) {
                  const actualIndex = trades.length - 1 - pendingTradeIndex;
                  const trade = trades[actualIndex];

                  // Update the trade status
                  const updatedTrade: Trade = {
                    ...trade,
                    status: 'failed' as 'failed',
                    errorMessage: dexterTx.error?.reasonRaw || 'Unknown error'
                  };

                  // Save the updated trade
                  saveTrade(updatedTrade);

                  console.log(`[DATABASE] Updated trade ${trade.id} for ${ticker} with status: failed`);
                }
              } catch (dbError) {
                console.error(`[DATABASE] Error updating trade status:`, dbError);
              }
            }
          });
      } catch (submitError) {
        logToConsoleAndFile(`[DEXTER] ❌ Error during swap submission: ${submitError instanceof Error ? submitError.message : String(submitError)}`);

        // Mark trade as failed if we were tracking it
        if (ticker) {
          try {
            // Find the most recent trade for this token with status = 'pending'
            const trades = getAllTrades();
            const pendingTradeIndex = [...trades].reverse().findIndex(
              t => (t.ticker === ticker || t.token === unit) && t.status === 'pending'
            );

            if (pendingTradeIndex !== -1) {
              const actualIndex = trades.length - 1 - pendingTradeIndex;
              const trade = trades[actualIndex];

              // Update the trade status
              const updatedTrade: Trade = {
                ...trade,
                status: 'failed' as 'failed',
                errorMessage: submitError instanceof Error ? submitError.message : String(submitError)
              };

              // Save the updated trade
              saveTrade(updatedTrade);

              console.log(`[DATABASE] Updated trade ${trade.id} for ${ticker} with status: failed (submission error)`);
            }
          } catch (dbError) {
            console.error(`[DATABASE] Error updating trade status:`, dbError);
          }
        }
      }
    } catch (swapSetupError) {
      logToConsoleAndFile(`[DEXTER] ❌ Error setting up swap: ${swapSetupError instanceof Error ? swapSetupError.message : String(swapSetupError)}`);

      // Mark trade as failed if we were tracking it
      if (ticker) {
        try {
          // Find the most recent trade for this token with status = 'pending'
          const trades = getAllTrades();
          const pendingTradeIndex = [...trades].reverse().findIndex(
            t => (t.ticker === ticker || t.token === unit) && t.status === 'pending'
          );

          if (pendingTradeIndex !== -1) {
            const actualIndex = trades.length - 1 - pendingTradeIndex;
            const trade = trades[actualIndex];

            // Update the trade status
            const updatedTrade: Trade = {
              ...trade,
              status: 'failed' as 'failed',
              errorMessage: swapSetupError instanceof Error ? swapSetupError.message : String(swapSetupError)
            };

            // Save the updated trade
            saveTrade(updatedTrade);

            console.log(`[DATABASE] Updated trade ${trade.id} for ${ticker} with status: failed (setup error)`);
          }
        } catch (dbError) {
          console.error(`[DATABASE] Error updating trade status:`, dbError);
        }
      }
    }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.stack || err.message : String(err);
    logToConsoleAndFile(`[DEXTER] ❌ Swap error for ${unit}:`);
    logToConsoleAndFile(errorMessage);

    // Mark trade as failed if we were tracking it
    if (ticker) {
      try {
        // Find the most recent trade for this token with status = 'pending'
        const trades = getAllTrades();
        const pendingTradeIndex = [...trades].reverse().findIndex(
          t => (t.ticker === ticker || t.token === unit) && t.status === 'pending'
        );

        if (pendingTradeIndex !== -1) {
          const actualIndex = trades.length - 1 - pendingTradeIndex;
          const trade = trades[actualIndex];

          // Update the trade status
          const updatedTrade: Trade = {
            ...trade,
            status: 'failed' as 'failed',
            errorMessage: errorMessage
          };

          // Save the updated trade
          saveTrade(updatedTrade);

          console.log(`[DATABASE] Updated trade ${trade.id} for ${ticker} with status: failed (general error)`);
        }
      } catch (dbError) {
        console.error(`[DATABASE] Error updating trade status:`, dbError);
      }
    }
  }
}

///////////////////////////////////////////
// Stablecoin Filter
///////////////////////////////////////////
function isStablecoin(ticker: string, unit: string): boolean {
  // List of known stablecoin and wrapped asset tickers (case insensitive)
  const stablecoinTickers = [
    'usda', 'iusd', 'usdm', 'djed', 'usdt', 'usdc',
    'usd', 'dai', 'tusd', 'busd', 'gusd', 'husd',
    'stable', 'stablecoin',
    'ibtc', 'ieth', 'wbtc', 'weth', 'wada'  // Wrapped assets
  ];

  // Check if the ticker contains any stablecoin keyword
  const normalizedTicker = ticker.toLowerCase();
  if (stablecoinTickers.some(stable => normalizedTicker.includes(stable))) {
    return true;
  }

  // Known stablecoin policy IDs or partial policy IDs can be added here
  const stablecoinPolicyIds: string[] = [
    // Add known stablecoin policy IDs here
  ];

  // Check against policy IDs if needed
  if (stablecoinPolicyIds.some(policyId => unit.startsWith(policyId))) {
    return true;
  }

  return false;
}

async function loop(userTradingParams?: any) {
    try {
      console.log('\n=== [BOT] Fetching top tokens from TapTools ===');
      const topTokens = await taptoolsGetTopVolumeTokens(25);
      if (!topTokens.length) {
        console.log('[BOT] No tokens returned. Skipping iteration.');
        return;
      }

      // Notify bridge that bot analysis run is starting
      botAnalysisBridge.startBotRun(topTokens.length);
      console.log('[BOT] Top tokens:', topTokens.map(t=>t.ticker));

      // If we do portfolio checks:
      let addressInfo: Record<string, any> | null = null;
      if (CARDANO_ADDRESS) {
        addressInfo = await taptoolsGetAddressInfo(CARDANO_ADDRESS);
      }

      // Parse user's portfolio if we have address info
      let totalAdaValue = 0;
      const categoryValues: Record<Category, number> = {
        ada: 0,
        meme: 0,
        defi: 0,
        major: 0,
        new: 0
      };

      if (addressInfo) {
        // 1. Start with ADA balance
        const adaStr = addressInfo.lovelace || '0';
        const userAda = parseInt(adaStr, 10) / 1_000_000;
        categoryValues.ada = userAda;

        // 2. Get all token units we need prices for
        const allUnitsInWallet: string[] = [];
        if (addressInfo.assets) {
          for (const asset of addressInfo.assets) {
            allUnitsInWallet.push(asset.unit);
          }
        }

        // 3. Fetch all token prices in one batch call
        const tokenPrices = await taptoolsGetTokenPrices(allUnitsInWallet);

        // 4. Calculate value for each token and add to proper category
        if (addressInfo.assets) {
          for (const asset of addressInfo.assets) {
            const price = tokenPrices[asset.unit] || 0;
            if (price <= 0) continue;

            const quantity = parseInt(asset.quantity, 10);
            const valueInAda = quantity * price;

            // Add value to proper category
            const category = classifyToken(asset.unit);
            categoryValues[category] += valueInAda;
          }
        }

        // 5. Sum total portfolio value
        totalAdaValue = Object.values(categoryValues).reduce((a,b) => a + b, 0);

        // Log the breakdown
        console.log('[PORTFOLIO] Value Breakdown:');
        for (const [cat, value] of Object.entries(categoryValues)) {
          const percentage = totalAdaValue > 0 ? ((value / totalAdaValue) * 100).toFixed(2) : '0.00';
          console.log(`  ${cat}: ${value.toFixed(2)} ADA (${percentage}%)`);
        }
        console.log(`[PORTFOLIO] Total Value: ${totalAdaValue.toFixed(2)} ADA`);

        // Log individual token holdings for better visibility
        if (addressInfo?.assets && addressInfo.assets.length > 0) {
          console.log('[PORTFOLIO] Individual Token Holdings:');
          for (const asset of addressInfo.assets) {
            const price = tokenPrices[asset.unit] || 0;
            if (price > 0) {
              const quantity = parseInt(asset.quantity, 10);
              const valueInAda = quantity * price;
              const category = classifyToken(asset.unit);

              // Try to get ticker from the unit (simplified)
              const ticker = asset.unit.length > 56 ?
                Buffer.from(asset.unit.slice(56), 'hex').toString('utf8').replace(/[^\w]/g, '') :
                'UNKNOWN';

              console.log(`  ${ticker}: ${quantity.toLocaleString()} tokens = ${valueInAda.toFixed(2)} ADA (${category})`);
            }
          }
        }
      }

      // For each top token, fetch advanced data:
      for (const tokenInfo of topTokens) {
        const { ticker, unit, price, volume } = tokenInfo;

        // Skip stablecoins
        if (isStablecoin(ticker, unit)) {
          console.log(`[BOT] Skipping stablecoin: ${ticker}`);
          continue;
        }

        const tokenData: any = {
          ticker,
          unit,
          volume_24h: volume,
          price
        };

        // Add advanced data from TapTools
        tokenData.aggregated_price = (await taptoolsGetTokenPrices([unit]))[unit] ?? null;
        tokenData.price_change = await taptoolsGetTokenPriceChg(unit, '1h,4h,24h');
        tokenData.pools = await taptoolsGetTokenPools(unit, 1);
        // Get multi-timeframe data for confluence analysis
        tokenData.ohlcv = await taptoolsGetTokenOhlcv(unit, '1h', 168); // Keep existing hourly data
        tokenData.trading_stats = await taptoolsGetTokenTradingStats(unit, '24h');
        tokenData.mcap = await taptoolsGetTokenMcap(unit);

        // Enhanced: Multi-timeframe analysis
        tokenData.multiTimeframe = await getMultiTimeframeAnalysis(unit, ticker);

        // Enhanced: Relative strength vs ADA
        tokenData.relativeStrength = await getRelativeStrengthVsADA(unit, ticker);

        // Get technical indicators directly from TapTools API
        try {
          // Fetch RSI data from TapTools API
          const rsiData = await taptoolsGetTokenRSI(unit, '1d', 14, 30);

          if (rsiData && Array.isArray(rsiData) && rsiData.length > 0) {
            console.log(`[INDICATORS] RSI data for ${ticker}:`, JSON.stringify(rsiData.slice(0, 3)));

            // Get the most recent RSI value - the data is already the RSI values
            const currentRSI = parseFloat(rsiData[rsiData.length - 1]);

            // Determine RSI trend by looking at the last 3 values
            let trend: 'uptrend' | 'downtrend' | 'neutral' = 'neutral';
            if (rsiData.length >= 3) {
              const last3 = rsiData.slice(-3).map(item => parseFloat(item));
              if (last3[0] < last3[1] && last3[1] < last3[2]) {
                trend = 'uptrend';
              } else if (last3[0] > last3[1] && last3[1] > last3[2]) {
                trend = 'downtrend';
              }
            }

            // Determine interpretation
            let interpretation: 'overbought' | 'oversold' | 'neutral' = 'neutral';
            if (currentRSI >= 70) {
              interpretation = 'overbought';
            } else if (currentRSI <= 30) {
              interpretation = 'oversold';
            }

            // Add RSI data to token data
            tokenData.technical_indicators = {
              rsi: {
                value: currentRSI,
                trend: trend,
                divergence: 'none', // We don't have enough data to calculate divergence
                interpretation: interpretation,
                history: rsiData.slice(-14).map(item => parseFloat(item)) // Keep the last 14 values for reference
              }
            };

            console.log(`[INDICATORS] RSI for ${ticker}: ${currentRSI.toFixed(2)} (${interpretation})`);
          } else {
            console.log(`[INDICATORS] No RSI data available from TapTools for ${ticker}, falling back to calculation`);

            // Fall back to calculation if TapTools API doesn't return data
            if (tokenData.ohlcv && tokenData.ohlcv.length > 0) {
              // Extract price data from OHLCV
              let prices = [];
              const firstCandle = tokenData.ohlcv[0];

              if (firstCandle && typeof firstCandle === 'object') {
                if ('close' in firstCandle) {
                  prices = tokenData.ohlcv.map((candle: any) => candle.close);
                } else if ('c' in firstCandle) {
                  prices = tokenData.ohlcv.map((candle: any) => candle.c);
                }
              }

              if (prices.length > 0) {
                // Calculate RSI
                const rsiAnalysis = analyzeRSI(prices);

                // Add RSI data to token data
                tokenData.technical_indicators = {
                  rsi: {
                    value: rsiAnalysis.value,
                    trend: rsiAnalysis.trend,
                    divergence: rsiAnalysis.divergence,
                    interpretation: rsiAnalysis.interpretation,
                    source: 'calculated' // Indicate this was calculated, not from API
                  }
                };

                console.log(`[INDICATORS] Calculated RSI for ${ticker}: ${rsiAnalysis.value.toFixed(2)} (${rsiAnalysis.interpretation})`);
              }
            }
          }
        } catch (indicatorError) {
          console.error(`[INDICATORS] Error fetching/calculating indicators for ${ticker}:`, indicatorError);
        }

        // Get historical trades for this token to enhance analysis
        const tokenTrades = getTradesByToken(ticker);
        if (tokenTrades.length > 0) {
          // Add trade history to token data for the LLM
          tokenData.trade_history = {
            total_trades: tokenTrades.length,
            successful_trades: tokenTrades.filter(t => t.performanceTracking?.success).length,
            avg_profit_loss: tokenTrades.reduce((sum, t) =>
              sum + (t.performanceTracking?.profitLossPercentage || 0), 0) / tokenTrades.length || 0,
            last_trade: tokenTrades[tokenTrades.length - 1]
          };

          console.log(`[BOT] Including trade history for ${ticker}: ${tokenTrades.length} trades`);
        }

        // Enhance token data with Twitter sentiment and news
        const enhancedTokenData = await enhanceTokenDataWithSentiment(tokenData);

        // Enhanced: Social media links for Discord notifications
        try {
          enhancedTokenData.socialLinks = await getTokenSocialLinks(unit);
          console.log(`[SOCIAL] ✅ Fetched social links for ${ticker}`);
        } catch (error) {
          console.log(`[SOCIAL] ⚠️ Could not fetch social links for ${ticker}:`, error instanceof Error ? error.message : error);
          enhancedTokenData.socialLinks = null;
        }

        // Debug: Check if sentiment data was added
        if (enhancedTokenData.sentiment_data) {
          console.log(`[TWITTER] 📊 Sentiment for ${ticker}:`, {
            sentiment: enhancedTokenData.sentiment_data.sentiment_analysis?.overall_sentiment,
            confidence: enhancedTokenData.sentiment_data.sentiment_analysis?.confidence,
            catalysts: enhancedTokenData.sentiment_data.sentiment_analysis?.catalysts?.length || 0,
            insights: enhancedTokenData.sentiment_data.sentiment_analysis?.key_insights?.length || 0
          });
        } else {
          console.log(`[TWITTER] ⚠️ No sentiment data added for ${ticker}`);
        }

        // Prepare portfolio holdings for LLM context
        const portfolioHoldings = addressInfo ? {
          totalValue: totalAdaValue,
          ada: categoryValues.ada,
          tokens: addressInfo.assets?.filter((asset: any) => asset.unit !== 'lovelace') || []
        } : null;

        const decision = await getLlmTradingDecision(enhancedTokenData, portfolioHoldings);
        console.log(`[LLM] Decision for ${ticker}:`, decision);

        // Add analysis to bridge for frontend (regardless of trade decision)
        await botAnalysisBridge.addTokenAnalysis({
          ticker,
          unit,
          decision,
          tokenData: enhancedTokenData,
          timestamp: new Date().toISOString()
        });

        if (decision.trade === 'false' || decision.recommendation === 'hold') {
          console.log(`[BOT] Skipping ${ticker}, no trade recommended (trade: ${decision.trade}, recommendation: ${decision.recommendation}).`);
          // Send analysis to Discord for no-trade decisions
          await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
          continue;
        }

        // PROFESSIONAL TRADER FILTER: Minimum confidence threshold
        if (decision.confidence < 10) {
          console.log(`[BOT] PROFESSIONAL FILTER: Skipping ${ticker}, confidence only ${decision.confidence}/10 (minimum 10 required)`);
          // Send analysis to Discord showing why we skipped
          await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
          continue;
        }

        // FINAL COHERENCE CHECK: Scan for any negative language
        const finalCheck = performFinalCoherenceCheck(decision);
        if (!finalCheck.isCoherent) {
          console.log(`[BOT] FINAL COHERENCE CHECK FAILED: ${finalCheck.reason}`);
          // Override to hold and send to Discord
          decision.recommendation = 'hold';
          decision.trade = 'false';
          await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
          continue;
        }

        // PROFESSIONAL TRADER FILTER: Additional safety checks
        const safetyCheck = performProfessionalSafetyCheck(decision, enhancedTokenData);
        if (!safetyCheck.isSafe) {
          console.log(`[BOT] PROFESSIONAL FILTER: Skipping ${ticker}, ${safetyCheck.reason}`);
          // Send analysis to Discord showing why we skipped
          await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
          continue;
        }

        // Use dynamic position sizing based on multiple factors
        let baseRecommended = Math.round(Math.min(Math.max(decision.size || 50, 50), 500));
        let finalAmount = baseRecommended;

        if (addressInfo) {
          // Apply dynamic position sizing first
          const portfolioValue = addressInfo.totalValue;
          const riskCategory = getRiskCategory(tokenData);

          const dynamicAmount = calculateDynamicPositionSize(
            baseRecommended,
            portfolioValue,
            decision,
            tokenData,
            riskCategory,
            userTradingParams
          );

          // Then apply traditional portfolio constraints
          const cat = classifyToken(unit);
          const catValueNow = categoryValues[cat];
          const userAdaBal = categoryValues.ada;
          finalAmount = adjustTradeForPortfolio(
            decision.direction,
            cat,
            dynamicAmount, // Use dynamic amount instead of recommended
            totalAdaValue,
            catValueNow,
            userAdaBal,
            addressInfo,  // Pass the addressInfo for token balance checks
            unit,         // Pass the token unit for sells
            tokenData,    // Pass token data for risk assessment
            decision.confidence, // Pass confidence for position sizing
            ticker        // Pass ticker for historical trade lookup
          );
          // Safety check for NaN or invalid values
          if (isNaN(finalAmount) || finalAmount <= 0) {
            console.log(`[BOT] Invalid finalAmount: ${finalAmount}, using fallback calculation`);
            finalAmount = Math.round(Math.min(baseRecommended, portfolioValue * 0.10));
          }

          // Ensure final amount is rounded to whole ADA
          finalAmount = Math.round(finalAmount);

          if (finalAmount < 1) {
            console.log('[BOT] finalAmount < 1, skipping trade');
            // Send analysis to Discord even though trade can't be executed
            await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
            continue;
          }
        }

        // Check for correlation-based exit signals before executing trade
        const exitCheck = checkCorrelationBasedExits(tokenData, decision);
        if (exitCheck.shouldExit) {
          console.log(`[BOT] CORRELATION EXIT: Skipping ${ticker}, ${exitCheck.reason}`);
          // Override decision to HOLD and send analysis
          decision.recommendation = 'hold';
          decision.trade = 'false';
          await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
          continue;
        }

        // Check if decision.direction is defined before proceeding
        if (!decision.direction) {
          console.log(`[BOT] ERROR: Invalid direction for ${ticker}, skipping trade`);
          // Send analysis to Discord even though trade has invalid direction
          await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
          continue;
        }

        // OWNERSHIP CHECK: For sell orders, verify we actually own the token
        if (decision.direction === 'sell') {
          const ownedTokens = portfolioHoldings?.tokens || [];
          const ownedToken = ownedTokens.find((token: any) => token.unit === unit);

          if (!ownedToken || !ownedToken.quantity || ownedToken.quantity <= 0) {
            console.log(`[BOT] OWNERSHIP ERROR: Cannot sell ${ticker} - we don't own any tokens (unit: ${unit})`);
            console.log(`[BOT] Owned tokens:`, ownedTokens.map((t: any) => `${t.unit}: ${t.quantity}`));
            // Send analysis to Discord but skip the trade
            await discordBot.sendTokenAnalysis(ticker, unit, decision, enhancedTokenData);
            continue;
          }

          console.log(`[BOT] ✅ Ownership verified for ${ticker}: ${ownedToken.quantity} tokens owned`);
        }

        console.log(`[BOT] Executing ${decision.direction.toUpperCase()} of ${finalAmount} ADA for token ${ticker}`);

        // Send consolidated Discord notification with final trade decision
        await discordBot.sendEnhancedTransactionNotification(ticker, unit, decision.direction, finalAmount, decision, undefined, undefined, enhancedTokenData);

        await executeDexSwap(decision.direction, finalAmount, unit, ticker, decision);
      }
    } catch (err) {
      console.error('[BOT] Main Loop Error:', err);
    }
}

///////////////////////////////////////////
// 9) The Main Logic Loop
///////////////////////////////////////////
async function mainLoop(): Promise<void> {
  console.log('[INIT] Setting up Dexter & Providers...');
  try {
    await setupDexterProviders();
    console.log('[INIT] Dexter providers setup successful');
  } catch (setupError) {
    console.error('[INIT] Error setting up Dexter providers:', setupError);
    process.exit(1); // Exit if setup fails
  }

  // Test LLM connection
  console.log('[INIT] Testing LLM connection...');
  try {
    const connectionTest = await llmService.testConnection();
    if (connectionTest) {
      console.log('[INIT] LLM connection test successful');
    } else {
      console.warn('[INIT] LLM connection test failed, but continuing...');
    }
  } catch (llmError) {
    console.error('[INIT] LLM connection test error:', llmError);
    console.warn('[INIT] Continuing without LLM test...');
  }

  // Define interval constants
  const THREE_HOURS_MS = 3 * 60 * 60 * 1000;  // 3 hours in milliseconds

  // Set up error handling for the interval
  const runIteration = async () => {
    try {
      // Log the execution time for 3-hourly runs
      const executionTime = new Date().toLocaleString();
      console.log(`[BOT] Starting 3-hourly trading run at ${executionTime}`);

      // Load user configuration
      const userTradingParams = await getUserTradingParams(DEFAULT_USER_ID);
      console.log(`[CONFIG] Loaded trading parameters for user: ${DEFAULT_USER_ID}`);

      await loop(userTradingParams);

      // Schedule next run and send Discord notification
      const nextRunDate = new Date(Date.now() + THREE_HOURS_MS);
      const nextRunFormatted = nextRunDate.toLocaleString();

      console.log(`[BOT] Completed 3-hourly trading run that started at ${executionTime}`);
      console.log(`[BOT] Next run scheduled for: ${nextRunFormatted}`);

      // Notify bridge that bot analysis run is complete
      botAnalysisBridge.completeBotRun(nextRunDate.toISOString());

      // Send Discord notification about the next scheduled run
      await discordBot.sendDailyScheduleNotification(nextRunFormatted);
    } catch (iterationError) {
      console.error('[BOT] Error in main loop iteration:', iterationError);
      console.log('[BOT] Will continue with next scheduled iteration...');
    }
  };

  // Define the interval constants (moved from above to avoid duplication)
  const ONE_DAY_MS = 24 * 60 * 60 * 1000; // Keep for other uses

  // Run first iteration and then set up interval
  await runIteration();

  // Schedule 3-hourly iterations (3 hours = 3 * 60 * 60 * 1000 milliseconds)
  setInterval(runIteration, THREE_HOURS_MS); // run once every 3 hours

  console.log('[BOT] Main loop started, will run once every 3 hours');
  const nextRunDate = new Date(Date.now() + THREE_HOURS_MS);
  console.log(`[BOT] Next run scheduled for: ${nextRunDate.toLocaleString()}`);
}

///////////////////////////////////////////
// 10) Health Check Server & Start
///////////////////////////////////////////

// Health check server for Railway
function startHealthServer() {
  const app = express();
  const port = process.env.PORT || 3000;

  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'mister-trading-bot',
      version: '1.0.0'
    });
  });

  app.listen(port, () => {
    console.log(`[HEALTH] Health check server running on port ${port}`);
  });
}

// Start health server and main loop
startHealthServer();
mainLoop().catch(console.error);