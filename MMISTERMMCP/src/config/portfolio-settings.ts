// Copy this file to portfolio-settings.ts and customize the values
export type Category = 'ada'
  | 'meme'
  | 'defi'
  | 'major'
  | 'new';

/**
 * Target allocation ratios for different asset categories in the portfolio.
 * The sum of all ratios must equal 1 (100%).
 * 
 * @remarks
 * These ratios define the desired portfolio composition:
 * - ada: 35% allocation for ADA
 * - meme: 20% allocation for meme coins
 * - defi: 15% allocation for DeFi protocols
 * - major: 20% allocation for major projects
 * - new: 10% allocation for new positions
 */

export const TARGET_RATIOS: Record<Category, number> = {
  ada: 0.35,
  meme: 0.20,
  defi: 0.15,
  major: 0.20,
  new: 0.10
};

// Policy IDs for different token categories
export const TOKEN_POLICIES = {
  ada: [], // ADA is the native token
  meme: [
    '8cf0a1b811e2ec60739b9c033a2eafa9d23c82822c447e042bbcb79c',  // HOSKY
    '8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa',  // SNEK
    '8fef2d34078659493ce161a6c7fba4b56afefa8535296a5743678f5a',  // MILK
    '279c909f348e533da5808898f87f9a14bb2c3dfbbacccd631d927a3f'   // SNEK (additional policy)
  ],
  defi: [
    'f43a62fdc3965df486de8a0d32fe800963589c41b38946602a0dc535',  // SUNDAE
    '8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa',  // MIN
    '29d222ce763455e3d7a09a665ce554f00ac89d2e99a1a83d267170c6',  // LQ
    'f13ac4d66b3ee19a6aa0f2a22298737bd907cc95121662fc971b5275',  // STRIKE
    'b6a7467ea1deb012808ef4e87b5ff371e85f7142d7b356a40d9b42a0'   // BODEGA
  ],
  major: [
    'da8c30857834c6ae7203935b89278c532b3995245295456f993e1d24',  // WMT
    '8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa',  // AADA
    'b34b3ea80060ace9427bda98690a73d33840e27aaa8d6edb7f0c757a'   // AGIX
  ],
  new: []
};

export function classifyToken(unit: string): Category {
  if (unit === 'lovelace') return 'ada';

  if (TOKEN_POLICIES.meme.some(id => unit.includes(id))) {
    return 'meme';
  }
  if (TOKEN_POLICIES.defi.some(id => unit.includes(id))) {
    return 'defi';
  }
  if (TOKEN_POLICIES.major.some(id => unit.includes(id))) {
    return 'major';
  }
  return 'new';
}
