// Copy this file to analysis-guidelines.ts and customize the values
export const ANALYSIS_GUIDELINES = {
  priceAnalysis: [
    "Look for key support and resistance levels that have been tested multiple times - specify exact levels to 4 decimals",
    "Identify bullish and bearish chart patterns (triangles, head & shoulders, etc.) with precise breakout/breakdown levels",
    "Analyze price movement relative to moving averages (50EMA, 200EMA crossovers) with exact crossover prices",
    "Note any unusual price spikes or drops that could indicate market manipulation",
    "Evaluate if the current price is near historic highs or lows and calculate precise retracement levels",
    "Check for price consolidation patterns that might precede breakouts - identify exact breakout levels",
    "Identify potential divergences between price and technical indicators",
    "Assess the strength of recent price movements (strong vs. weak momentum)",
    "Calculate precise Fibonacci retracement levels (23.6%, 38.2%, 50%, 61.8%) for targets and stops",
    "Identify exact pivot points and psychological levels (round numbers) to 4 decimal places"
  ],
  volumeAnalysis: [
    "Check for volume confirmation of price movements (high volume on breakouts)",
    "Look for volume divergence from price (declining volume in rallies, increasing in drops)",
    "Compare current volume to historical average volume (unusual activity)",
    "Identify potential accumulation/distribution patterns based on volume",
    "Note changes in volume trends that might signal shifting market interest",
    "Analyze buy vs. sell volume ratio to determine market sentiment",
    "Check for volume spikes that might indicate capitulation or euphoria",
    "Evaluate if volume is increasing or decreasing during the current trend"
  ],
  technicalIndicators: [
    "Analyze RSI (Relative Strength Index) for overbought/oversold conditions",
    "Look for RSI divergences with price (bullish/bearish divergences)",
    "Check if RSI is trending up or down regardless of absolute value",
    "Note if RSI has recently crossed key levels (30, 50, 70)",
    "Consider RSI in multiple timeframes for confirmation"
  ],
  marketStructure: [
    "Assess overall market trend (bullish, bearish, or ranging)",
    "Identify higher highs/higher lows (bullish) or lower highs/lower lows (bearish)",
    "Evaluate if the token is outperforming or underperforming ADA and major indices",
    "Consider seasonality or cyclical patterns relevant to the token category",
    "Look for potential reversal signals or continuation patterns",
    "Identify key market phases (accumulation, markup, distribution, markdown)",
    "Assess liquidity conditions and their impact on price stability"
  ],
  fundamentalFactors: [
    "Consider recent news, developments, or announcements related to the token",
    "Note upcoming events that could impact price (launches, updates, governance votes)",
    "Assess the token's utility, adoption metrics, and growth indicators",
    "Review on-chain metrics if available (addresses growth, transaction volume)",
    "Consider broader market conditions and their influence on this specific token",
    "Evaluate the token's community engagement and social media sentiment",
    "Check for any regulatory developments that might impact the token"
  ],
  marketConditions: [
    "Assess the overall crypto market sentiment (bullish, bearish, or neutral)",
    "Consider correlation with Bitcoin and Ethereum price movements",
    "Evaluate sector-specific trends (DeFi, meme coins, etc.)",
    "Check for any macro events that might impact crypto markets",
    "Consider the impact of liquidity conditions on smaller cap tokens"
  ],
  historicalPerformance: [
    "Review past trade history and success rate for this token",
    "Analyze whether previous buy/sell signals were accurate",
    "Consider the average profit/loss from past trades with this token",
    "Identify market conditions where previous trades succeeded or failed",
    "Compare current market setup to previous successful trades",
    "Evaluate the token's historical volatility compared to current levels"
  ],
  riskAssessment: [
    "Evaluate the risk/reward ratio for the potential trade",
    "Consider the token's liquidity and potential slippage",
    "Assess volatility levels and their impact on position sizing",
    "Identify potential black swan events that could impact the token",
    "Consider correlation with other portfolio holdings"
  ],
  tradeExecution: [
    "Consider the optimal entry point based on current price action",
    "Evaluate appropriate position sizing based on volatility and risk",
    "Determine logical stop loss levels based on technical support/resistance",
    "Define reasonable target prices for short and long-term goals",
    "Outline a timeframe expectation for the trade to play out",
    "Consider scaling in/out strategies rather than all-in/all-out",
    "Plan for different market scenarios (bullish, bearish, sideways)"
  ]
};

export const DATA_FIELDS = {
  basic: ["price", "price_change_24h", "volume_24h", "market_cap"],
  technical: ["price_7d", "price_30d", "volume_7d", "volatility"],
  market: ["liquidity", "holders", "transactions_24h", "velocity"],
  comparative: ["ada_correlation", "market_rank", "sector_performance"],
  tradeHistory: ["total_trades", "successful_trades", "avg_profit_loss", "last_trade"]
};

export const ANALYSIS_STEPS = [
  "Review price and volume data across multiple timeframes",
  "Identify key support/resistance levels and chart patterns",
  "Assess market structure and trend direction",
  "Evaluate volume characteristics and divergences",
  "Consider fundamental factors and news catalysts",
  "Compare with broader market trends and correlations",
  "Review historical trade performance with this token",
  "Calculate risk/reward ratio based on key levels",
  "Determine confidence level in the analysis"
];

export const EXPECTED_RESPONSE_FORMAT = {
  summary: "Brief overview of key findings",
  technicalAnalysis: {
    priceAnalysis: "Analysis of price action and chart patterns",
    volumeAnalysis: "Analysis of volume and its relation to price",
    technicalIndicators: {
      rsi: "RSI analysis including current value, trend, and divergences",
      rsiValue: "Current RSI value (0-100)",
      rsiTrend: "Direction of RSI (uptrend, downtrend, or neutral)"
    },
    marketStructure: "Overall market structure assessment",
    supportResistance: "Key support and resistance levels"
  },
  fundamentalFactors: {
    news: "Recent news and developments - MUST include Twitter sentiment if available",
    utility: "Token utility and use case assessment",
    adoption: "Metrics about adoption and growth",
    community: "Community engagement and sentiment - MUST include Twitter insights if available",
    sentiment: "Twitter sentiment analysis and social media buzz - REQUIRED if sentiment_data exists"
  },
  historicalPerformance: "Analysis of past trades for this token (if available)",
  marketContext: "How the token relates to broader market conditions",
  riskAssessment: {
    riskRewardRatio: "Calculated risk/reward ratio for the trade",
    volatilityAssessment: "Assessment of current volatility",
    liquidityRisk: "Evaluation of liquidity risks"
  },
  recommendation: "Buy (if you have ADA), sell (if you own the token), or hold recommendation with reasoning",
  confidence: "1-10 scale indicating confidence in analysis (6+ required for trades)",
  targetPrice: "Precise price targets to 4 decimal places using technical analysis (e.g., '0.2847', '1.2345')",
  stopLoss: "Precise stop loss level to 4 decimal places based on support levels (e.g., '0.2534', '1.1892')",
  timeframe: "Expected timeframe for the trade to play out",
  direction: "Buy or sell direction for the trade",
  size: "Recommended position size in ADA (1-100 for testing, 50-500 for production)"
};
