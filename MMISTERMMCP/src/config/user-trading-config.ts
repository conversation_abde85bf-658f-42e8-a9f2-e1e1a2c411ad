/**
 * User Trading Configuration Types and Defaults
 * Defines the structure and default values for user-specific trading configurations
 */

export interface UserTradingConfig {
  userId: string;
  displayName: string;
  
  // Portfolio allocation settings
  portfolioAllocations: {
    ada: number;
    meme: number;
    defi: number;
    major: number;
    shard: number;
    new: number;
  };
  
  // Trading limits and thresholds
  tradingLimits: {
    minConfidence: number;
    minTokenAge: number;
    minLiquidity: number;
    maxImpact: number;
    maxTradesPerDay: number;
    maxTradeSize: number;
  };
  
  // Risk management settings
  riskManagement: {
    dynamicPositionSizing: boolean;
    stopLossEnabled: boolean;
    trailingStopEnabled: boolean;
    timeBasedExitEnabled: boolean;
    defaultRiskLevel: 'conservative' | 'moderate' | 'aggressive';
    maxDrawdown: number;
  };
  
  // Trade execution settings
  tradeExecution: {
    confidenceThreshold: number;
    enableProfessionalFilter: boolean;
    enableCorrelationExits: boolean;
    stopLoss: {
      enabled: boolean;
      defaultPercent: number;
      trailingEnabled: boolean;
    };
    takeProfit: {
      enabled: boolean;
      defaultPercent: number;
      partialEnabled: boolean;
    };
  };
  
  // Notification preferences
  notifications: {
    discord: {
      enabled: boolean;
    };
    email: {
      enabled: boolean;
    };
    webhook: {
      enabled: boolean;
    };
  };
  
  // Advanced settings
  advanced: {
    enableMultiTimeframeAnalysis: boolean;
    enableTwitterSentiment: boolean;
    enableRelativeStrength: boolean;
    rateLimiting: {
      maxTradesPerHour: number;
      maxTradesPerDay: number;
      cooldownMinutes: number;
    };
  };
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  lastUsed: Date;
}

/**
 * Default user configuration
 */
export const DEFAULT_USER_CONFIG: UserTradingConfig = {
  userId: 'default-trader',
  displayName: 'Default Trader',
  
  portfolioAllocations: {
    ada: 35,
    meme: 20,
    defi: 15,
    major: 20,
    shard: 5,
    new: 5
  },
  
  tradingLimits: {
    minConfidence: 10,
    minTokenAge: 7,
    minLiquidity: 5000,
    maxImpact: 5,
    maxTradesPerDay: 5,
    maxTradeSize: 100
  },
  
  riskManagement: {
    dynamicPositionSizing: true,
    stopLossEnabled: true,
    trailingStopEnabled: false,
    timeBasedExitEnabled: true,
    defaultRiskLevel: 'moderate',
    maxDrawdown: 0.15
  },
  
  tradeExecution: {
    confidenceThreshold: 10,
    enableProfessionalFilter: true,
    enableCorrelationExits: true,
    stopLoss: {
      enabled: true,
      defaultPercent: 0.1,
      trailingEnabled: false
    },
    takeProfit: {
      enabled: true,
      defaultPercent: 0.2,
      partialEnabled: false
    }
  },
  
  notifications: {
    discord: {
      enabled: true
    },
    email: {
      enabled: false
    },
    webhook: {
      enabled: false
    }
  },
  
  advanced: {
    enableMultiTimeframeAnalysis: true,
    enableTwitterSentiment: true,
    enableRelativeStrength: true,
    rateLimiting: {
      maxTradesPerHour: 10,
      maxTradesPerDay: 50,
      cooldownMinutes: 5
    }
  },
  
  createdAt: new Date(),
  updatedAt: new Date(),
  lastUsed: new Date()
};

/**
 * Preset configurations for different trading styles
 */
export const PRESET_CONFIGS = {
  conservative: {
    ...DEFAULT_USER_CONFIG,
    displayName: 'Conservative Trader',
    portfolioAllocations: {
      ada: 50,
      meme: 5,
      defi: 20,
      major: 20,
      shard: 3,
      new: 2
    },
    tradingLimits: {
      ...DEFAULT_USER_CONFIG.tradingLimits,
      minConfidence: 8,
      maxTradesPerDay: 3,
      maxTradeSize: 50
    },
    riskManagement: {
      ...DEFAULT_USER_CONFIG.riskManagement,
      defaultRiskLevel: 'conservative' as const,
      maxDrawdown: 0.1
    }
  },
  
  aggressive: {
    ...DEFAULT_USER_CONFIG,
    displayName: 'Aggressive Trader',
    portfolioAllocations: {
      ada: 20,
      meme: 30,
      defi: 15,
      major: 15,
      shard: 10,
      new: 10
    },
    tradingLimits: {
      ...DEFAULT_USER_CONFIG.tradingLimits,
      minConfidence: 5,
      maxTradesPerDay: 10,
      maxTradeSize: 200
    },
    riskManagement: {
      ...DEFAULT_USER_CONFIG.riskManagement,
      defaultRiskLevel: 'aggressive' as const,
      maxDrawdown: 0.25
    }
  }
};

/**
 * Validate user configuration
 */
export function validateUserConfig(config: Partial<UserTradingConfig>): boolean {
  try {
    // Check required fields
    if (!config.userId || !config.displayName) {
      return false;
    }
    
    // Validate portfolio allocations sum to 100
    if (config.portfolioAllocations) {
      const total = Object.values(config.portfolioAllocations).reduce((sum, val) => sum + val, 0);
      if (Math.abs(total - 100) > 1) { // Allow 1% tolerance
        console.warn('[CONFIG] Portfolio allocations do not sum to 100%:', total);
        return false;
      }
    }
    
    // Validate trading limits
    if (config.tradingLimits) {
      const limits = config.tradingLimits;
      if (limits.minConfidence < 1 || limits.minConfidence > 10) {
        return false;
      }
      if (limits.maxTradesPerDay < 1 || limits.maxTradesPerDay > 100) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('[CONFIG] Validation error:', error);
    return false;
  }
}

/**
 * Merge user config with defaults
 */
export function mergeWithDefaults(userConfig: Partial<UserTradingConfig>): UserTradingConfig {
  return {
    ...DEFAULT_USER_CONFIG,
    ...userConfig,
    portfolioAllocations: {
      ...DEFAULT_USER_CONFIG.portfolioAllocations,
      ...userConfig.portfolioAllocations
    },
    tradingLimits: {
      ...DEFAULT_USER_CONFIG.tradingLimits,
      ...userConfig.tradingLimits
    },
    riskManagement: {
      ...DEFAULT_USER_CONFIG.riskManagement,
      ...userConfig.riskManagement
    },
    tradeExecution: {
      ...DEFAULT_USER_CONFIG.tradeExecution,
      ...userConfig.tradeExecution,
      stopLoss: {
        ...DEFAULT_USER_CONFIG.tradeExecution.stopLoss,
        ...userConfig.tradeExecution?.stopLoss
      },
      takeProfit: {
        ...DEFAULT_USER_CONFIG.tradeExecution.takeProfit,
        ...userConfig.tradeExecution?.takeProfit
      }
    },
    notifications: {
      ...DEFAULT_USER_CONFIG.notifications,
      ...userConfig.notifications,
      discord: {
        ...DEFAULT_USER_CONFIG.notifications.discord,
        ...userConfig.notifications?.discord
      },
      email: {
        ...DEFAULT_USER_CONFIG.notifications.email,
        ...userConfig.notifications?.email
      },
      webhook: {
        ...DEFAULT_USER_CONFIG.notifications.webhook,
        ...userConfig.notifications?.webhook
      }
    },
    advanced: {
      ...DEFAULT_USER_CONFIG.advanced,
      ...userConfig.advanced,
      rateLimiting: {
        ...DEFAULT_USER_CONFIG.advanced.rateLimiting,
        ...userConfig.advanced?.rateLimiting
      }
    },
    updatedAt: new Date(),
    lastUsed: new Date()
  };
}
