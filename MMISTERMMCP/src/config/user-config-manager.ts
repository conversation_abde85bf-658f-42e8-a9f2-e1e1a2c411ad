/**
 * User Configuration Manager
 * Handles loading, saving, and managing user-specific trading configurations
 */

import { UserTradingConfig, DEFAULT_USER_CONFIG, PRESET_CONFIGS, validateUserConfig } from './user-trading-config.js';
import fs from 'fs/promises';
import path from 'path';

export class UserConfigManager {
  private configCache = new Map<string, UserTradingConfig>();
  private configDir: string;
  
  constructor(configDir: string = './user-configs') {
    this.configDir = configDir;
    this.ensureConfigDirectory();
  }
  
  /**
   * Ensure the configuration directory exists
   */
  private async ensureConfigDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.configDir, { recursive: true });
    } catch (error) {
      console.error('[CONFIG] Failed to create config directory:', error);
    }
  }
  
  /**
   * Get configuration for a user
   */
  async getUserConfig(userId: string): Promise<UserTradingConfig> {
    // Check cache first
    if (this.configCache.has(userId)) {
      const config = this.configCache.get(userId)!;
      config.lastUsed = new Date();
      return config;
    }
    
    // Try to load from file
    try {
      const configPath = path.join(this.configDir, `${userId}.json`);
      const configData = await fs.readFile(configPath, 'utf-8');
      const config: UserTradingConfig = JSON.parse(configData);
      
      // Convert date strings back to Date objects
      config.createdAt = new Date(config.createdAt);
      config.updatedAt = new Date(config.updatedAt);
      config.lastUsed = new Date();
      
      // Cache the config
      this.configCache.set(userId, config);
      
      console.log(`[CONFIG] Loaded user config for ${userId}`);
      return config;
    } catch (error) {
      // Config doesn't exist, create default
      console.log(`[CONFIG] Creating default config for new user ${userId}`);
      return this.createDefaultConfig(userId);
    }
  }
  
  /**
   * Save user configuration
   */
  async saveUserConfig(config: UserTradingConfig): Promise<void> {
    // Validate configuration
    const errors = validateUserConfig(config);
    if (errors.length > 0) {
      throw new Error(`Invalid configuration: ${errors.join(', ')}`);
    }
    
    // Update timestamp
    config.updatedAt = new Date();
    
    try {
      const configPath = path.join(this.configDir, `${config.userId}.json`);
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));
      
      // Update cache
      this.configCache.set(config.userId, config);
      
      console.log(`[CONFIG] Saved user config for ${config.userId}`);
    } catch (error) {
      console.error(`[CONFIG] Failed to save config for ${config.userId}:`, error);
      throw error;
    }
  }
  
  /**
   * Create default configuration for a new user
   */
  async createDefaultConfig(userId: string, preset?: 'conservative' | 'aggressive'): Promise<UserTradingConfig> {
    const baseConfig = preset ? PRESET_CONFIGS[preset] : DEFAULT_USER_CONFIG;
    
    const config: UserTradingConfig = {
      ...baseConfig,
      userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastUsed: new Date()
    };
    
    await this.saveUserConfig(config);
    return config;
  }
  
  /**
   * Update specific configuration sections
   */
  async updateUserConfig(
    userId: string, 
    updates: Partial<UserTradingConfig>
  ): Promise<UserTradingConfig> {
    const currentConfig = await this.getUserConfig(userId);
    
    // Deep merge the updates
    const updatedConfig: UserTradingConfig = {
      ...currentConfig,
      ...updates,
      userId, // Ensure userId doesn't change
      updatedAt: new Date()
    };
    
    await this.saveUserConfig(updatedConfig);
    return updatedConfig;
  }
  
  /**
   * Get all user configurations (for admin purposes)
   */
  async getAllUserConfigs(): Promise<UserTradingConfig[]> {
    try {
      const files = await fs.readdir(this.configDir);
      const configFiles = files.filter(file => file.endsWith('.json'));
      
      const configs: UserTradingConfig[] = [];
      for (const file of configFiles) {
        const userId = file.replace('.json', '');
        try {
          const config = await this.getUserConfig(userId);
          configs.push(config);
        } catch (error) {
          console.error(`[CONFIG] Failed to load config for ${userId}:`, error);
        }
      }
      
      return configs;
    } catch (error) {
      console.error('[CONFIG] Failed to get all user configs:', error);
      return [];
    }
  }
  
  /**
   * Delete user configuration
   */
  async deleteUserConfig(userId: string): Promise<void> {
    try {
      const configPath = path.join(this.configDir, `${userId}.json`);
      await fs.unlink(configPath);
      
      // Remove from cache
      this.configCache.delete(userId);
      
      console.log(`[CONFIG] Deleted user config for ${userId}`);
    } catch (error) {
      console.error(`[CONFIG] Failed to delete config for ${userId}:`, error);
      throw error;
    }
  }
  
  /**
   * Apply user configuration to trading parameters
   */
  applyConfigToTradingParams(config: UserTradingConfig) {
    return {
      // Dynamic position sizing configuration
      dynamicSizing: {
        volatilityBased: config.riskTolerance.customMultipliers || {
          lowVol: 0.15,
          medVol: 0.10,
          highVol: 0.05
        },
        confidenceBased: config.positionSizing.confidenceMultipliers,
        confluenceBased: config.positionSizing.confluenceMultipliers,
        correlationLimits: {
          maxSectorExposure: config.portfolioLimits.maxCorrelatedTokens,
          maxSimilarTokens: config.portfolioLimits.maxSingleToken
        }
      },
      
      // Portfolio limits
      portfolioLimits: config.portfolioLimits.categories,
      
      // Trade execution settings
      execution: {
        confidenceThreshold: config.tradeExecution.confidenceThreshold,
        enableProfessionalFilter: config.tradeExecution.enableProfessionalFilter,
        enableCorrelationExits: config.tradeExecution.enableCorrelationExits,
        minPositionAda: config.positionSizing.minPositionAda,
        maxPositionAda: config.positionSizing.maxPositionAda,
        maxPositionPercent: config.positionSizing.maxPositionPercent
      },
      
      // Feature toggles
      features: {
        multiTimeframe: config.advanced.enableMultiTimeframeAnalysis,
        twitterSentiment: config.advanced.enableTwitterSentiment,
        relativeStrength: config.advanced.enableRelativeStrength
      },
      
      // Rate limiting
      rateLimiting: config.advanced.rateLimiting,
      
      // Notifications
      notifications: config.notifications
    };
  }
  
  /**
   * Clear configuration cache
   */
  clearCache(): void {
    this.configCache.clear();
    console.log('[CONFIG] Configuration cache cleared');
  }
}

// Global instance
export const userConfigManager = new UserConfigManager();

/**
 * Helper function to get trading parameters for a user
 */
export async function getUserTradingParams(userId: string) {
  const config = await userConfigManager.getUserConfig(userId);
  return userConfigManager.applyConfigToTradingParams(config);
}
