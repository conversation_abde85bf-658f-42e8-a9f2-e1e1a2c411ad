/**
 * Risk Management Utilities
 * 
 * This file contains functions for dynamic position sizing and advanced stop loss strategies
 * to improve the trading bot's risk management capabilities.
 */

import { Trade } from './database';

/**
 * Token Risk Categories
 */
export enum TokenRiskCategory {
  ULTRA_HIGH = 'ultra_high',  // New tokens with < 3 days history and high volatility
  HIGH = 'high',              // Newer tokens (< 7 days) or high volatility tokens
  MEDIUM = 'medium',          // Established tokens with moderate volatility
  LOW = 'low'                 // Established tokens with low volatility
}

/**
 * Token Age Categories
 */
export enum TokenAgeCategory {
  NEW = 'new',                // < 3 days since first appearance in top volume
  RECENT = 'recent',          // 3-7 days since first appearance
  ESTABLISHED = 'established' // > 7 days since first appearance
}

/**
 * Calculate the volatility of a token based on price history
 * 
 * @param prices Array of historical prices
 * @returns Volatility as a percentage
 */
export function calculateVolatility(prices: number[]): number {
  if (!prices || prices.length < 2) {
    return 0;
  }

  // Calculate daily returns
  const returns: number[] = [];
  for (let i = 1; i < prices.length; i++) {
    const dailyReturn = (prices[i] - prices[i - 1]) / prices[i - 1];
    returns.push(dailyReturn);
  }

  // Calculate standard deviation of returns
  const mean = returns.reduce((sum, value) => sum + value, 0) / returns.length;
  const squaredDiffs = returns.map(value => Math.pow(value - mean, 2));
  const variance = squaredDiffs.reduce((sum, value) => sum + value, 0) / squaredDiffs.length;
  const stdDev = Math.sqrt(variance);

  // Annualized volatility (assuming daily prices)
  const annualizedVolatility = stdDev * Math.sqrt(365) * 100;

  return annualizedVolatility;
}

/**
 * Determine the risk category of a token
 * 
 * @param tokenData Token data including price history, volume, etc.
 * @param tokenTrades Historical trades for this token
 * @returns Risk category
 */
export function determineRiskCategory(
  tokenData: any,
  tokenTrades: Trade[]
): TokenRiskCategory {
  console.log(`[RISK] Analyzing token: ${tokenData.ticker || 'Unknown'}`);

  // Determine token age
  const ageCategory = determineTokenAge(tokenData, tokenTrades);
  console.log(`[RISK] Age category: ${ageCategory}`);

  // Calculate volatility if price data is available
  let volatility = 0;
  if (tokenData.ohlcv && tokenData.ohlcv.length > 0) {
    const prices = tokenData.ohlcv.map((candle: any) =>
      candle.close || candle.c || (Array.isArray(candle) ? candle[4] : 0)
    );
    volatility = calculateVolatility(prices);
    console.log(`[RISK] Calculated volatility: ${volatility.toFixed(2)}%`);
  }

  // Check liquidity and market depth
  const liquidityScore = assessLiquidity(tokenData);
  console.log(`[RISK] Liquidity score: ${liquidityScore}/10`);

  // Check for rugpull characteristics
  const rugpullRisk = assessRugpullRisk(tokenData);
  console.log(`[RISK] Rugpull risk: ${rugpullRisk}/10`);

  // Check market maturity (number of candles/data points)
  const maturityScore = assessMarketMaturity(tokenData);
  console.log(`[RISK] Market maturity: ${maturityScore}/10`);

  // Check if this is a high volume token
  const isHighVolume = isHighVolumeToken(tokenData);
  console.log(`[RISK] High volume token: ${isHighVolume}`);

  // Comprehensive risk assessment
  let riskScore = 0;

  // Age factor (0-4 points)
  if (ageCategory === TokenAgeCategory.NEW) {
    riskScore += 4;
  } else if (ageCategory === TokenAgeCategory.RECENT) {
    riskScore += 2;
  } else {
    riskScore += 0; // Established tokens get no age penalty
  }

  // Volatility factor (0-3 points)
  if (volatility > 150) {
    riskScore += 3;
  } else if (volatility > 100) {
    riskScore += 2;
  } else if (volatility > 50) {
    riskScore += 1;
  }

  // Liquidity factor (0-2 points)
  if (liquidityScore < 3) {
    riskScore += 2;
  } else if (liquidityScore < 6) {
    riskScore += 1;
  }

  // Rugpull risk factor (0-3 points)
  if (rugpullRisk > 7) {
    riskScore += 3;
  } else if (rugpullRisk > 5) {
    riskScore += 2;
  } else if (rugpullRisk > 3) {
    riskScore += 1;
  }

  // Market maturity factor (0-2 points)
  if (maturityScore < 3) {
    riskScore += 2;
  } else if (maturityScore < 6) {
    riskScore += 1;
  }

  console.log(`[RISK] Total risk score: ${riskScore}/14`);

  // Determine final risk category
  if (riskScore >= 10) {
    return TokenRiskCategory.ULTRA_HIGH;
  } else if (riskScore >= 7) {
    return TokenRiskCategory.HIGH;
  } else if (riskScore >= 4) {
    return TokenRiskCategory.MEDIUM;
  } else {
    return TokenRiskCategory.LOW;
  }
}

/**
 * Determine the age category of a token
 * 
 * @param tokenData Token data
 * @param tokenTrades Historical trades for this token
 * @returns Age category
 */
export function determineTokenAge(
  tokenData: any,
  tokenTrades: Trade[]
): TokenAgeCategory {
  // Check if we have first seen date in token data
  if (tokenData.first_seen_date) {
    const firstSeen = new Date(tokenData.first_seen_date);
    const daysSinceFirstSeen = (Date.now() - firstSeen.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysSinceFirstSeen < 3) {
      return TokenAgeCategory.NEW;
    } else if (daysSinceFirstSeen < 7) {
      return TokenAgeCategory.RECENT;
    } else {
      return TokenAgeCategory.ESTABLISHED;
    }
  }
  
  // If no first seen date, check trade history
  if (tokenTrades && tokenTrades.length > 0) {
    // Sort trades by timestamp
    const sortedTrades = [...tokenTrades].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    const firstTradeDate = new Date(sortedTrades[0].timestamp);
    const daysSinceFirstTrade = (Date.now() - firstTradeDate.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysSinceFirstTrade < 3) {
      return TokenAgeCategory.NEW;
    } else if (daysSinceFirstTrade < 7) {
      return TokenAgeCategory.RECENT;
    } else {
      return TokenAgeCategory.ESTABLISHED;
    }
  }
  
  // If we have no data, assume it's new
  return TokenAgeCategory.NEW;
}

/**
 * Check if a token is a high volume token
 * 
 * @param tokenData Token data
 * @returns True if high volume
 */
export function isHighVolumeToken(tokenData: any): boolean {
  // Check if volume data is available
  if (tokenData.trading_stats && tokenData.trading_stats.volume) {
    // Consider high volume if > 10,000 ADA in 24h
    return tokenData.trading_stats.volume > 10000;
  }
  
  // If no volume data, check if it's in the top 20 tokens
  if (tokenData.rank && tokenData.rank <= 20) {
    return true;
  }
  
  return false;
}

/**
 * Calculate dynamic position size based on risk category and other factors
 * 
 * @param baseSize Base position size recommended by AI
 * @param riskCategory Risk category of the token
 * @param confidence AI confidence score (0-10)
 * @param availableAda Available ADA for trading
 * @param maxPositionSize Maximum position size allowed
 * @returns Adjusted position size
 */
export function calculateDynamicPositionSize(
  baseSize: number,
  riskCategory: TokenRiskCategory,
  confidence: number,
  availableAda: number,
  maxPositionSize: number = 500
): number {
  // Risk multiplier based on category
  let riskMultiplier = 1.0;
  switch (riskCategory) {
    case TokenRiskCategory.ULTRA_HIGH:
      riskMultiplier = 0.3; // Only 30% of base size for ultra high risk
      break;
    case TokenRiskCategory.HIGH:
      riskMultiplier = 0.5; // 50% of base size for high risk
      break;
    case TokenRiskCategory.MEDIUM:
      riskMultiplier = 0.8; // 80% of base size for medium risk
      break;
    case TokenRiskCategory.LOW:
      riskMultiplier = 1.0; // 100% of base size for low risk
      break;
  }
  
  // Confidence multiplier (scales from 0.5 to 1.5 based on confidence)
  const confidenceMultiplier = 0.5 + (confidence / 10);
  
  // Calculate adjusted size
  let adjustedSize = baseSize * riskMultiplier * confidenceMultiplier;
  
  // Cap at maximum position size
  adjustedSize = Math.min(adjustedSize, maxPositionSize);
  
  // Cap at available ADA (minus a small buffer for fees)
  const maxAvailable = Math.max(0, availableAda - 2);
  adjustedSize = Math.min(adjustedSize, maxAvailable);
  
  // Ensure minimum viable trade size
  if (adjustedSize < 5) {
    // If adjusted size is too small, either don't trade or use minimum size
    return adjustedSize < 2 ? 0 : 5;
  }
  
  // Round to nearest ADA
  return Math.round(adjustedSize);
}

/**
 * Calculate dynamic stop loss based on token characteristics
 * 
 * @param currentPrice Current token price
 * @param riskCategory Risk category of the token
 * @param supportLevels Array of support levels (optional)
 * @param volatility Token volatility (optional)
 * @returns Stop loss price
 */
export function calculateDynamicStopLoss(
  currentPrice: number,
  riskCategory: TokenRiskCategory,
  supportLevels: number[] = [],
  volatility: number = 0
): number {
  // Default percentage drop based on risk category
  let percentageDrop = 0.05; // 5% for low risk
  
  switch (riskCategory) {
    case TokenRiskCategory.ULTRA_HIGH:
      percentageDrop = 0.15; // 15% for ultra high risk
      break;
    case TokenRiskCategory.HIGH:
      percentageDrop = 0.10; // 10% for high risk
      break;
    case TokenRiskCategory.MEDIUM:
      percentageDrop = 0.07; // 7% for medium risk
      break;
    case TokenRiskCategory.LOW:
      percentageDrop = 0.05; // 5% for low risk
      break;
  }
  
  // Adjust based on volatility if available
  if (volatility > 0) {
    // Use daily volatility to adjust stop loss (higher volatility = wider stop)
    const dailyVolatility = volatility / Math.sqrt(365);
    percentageDrop = Math.max(percentageDrop, dailyVolatility * 2);
  }
  
  // Calculate stop loss based on percentage
  let stopLoss = currentPrice * (1 - percentageDrop);
  
  // If support levels are provided, use the closest one below current price
  if (supportLevels.length > 0) {
    // Filter support levels below current price
    const validSupportLevels = supportLevels
      .filter(level => level < currentPrice)
      .sort((a, b) => b - a); // Sort descending
    
    if (validSupportLevels.length > 0) {
      // Use the highest support level below current price
      const supportBasedStop = validSupportLevels[0];
      
      // Use the higher of percentage-based or support-based stop
      stopLoss = Math.max(stopLoss, supportBasedStop);
    }
  }
  
  return stopLoss;
}

/**
 * Calculate trailing stop loss parameters
 * 
 * @param currentPrice Current token price
 * @param entryPrice Entry price of the trade
 * @param riskCategory Risk category of the token
 * @returns Trailing stop parameters
 */
export function calculateTrailingStop(
  currentPrice: number,
  entryPrice: number,
  riskCategory: TokenRiskCategory
): { 
  activationPercentage: number; 
  trailPercentage: number;
  isActive: boolean;
} {
  // Default trailing stop parameters
  let activationPercentage = 0.05; // 5% profit to activate
  let trailPercentage = 0.03; // 3% trail
  
  // Adjust based on risk category
  switch (riskCategory) {
    case TokenRiskCategory.ULTRA_HIGH:
      activationPercentage = 0.10; // 10% profit to activate
      trailPercentage = 0.05; // 5% trail
      break;
    case TokenRiskCategory.HIGH:
      activationPercentage = 0.07; // 7% profit to activate
      trailPercentage = 0.04; // 4% trail
      break;
    case TokenRiskCategory.MEDIUM:
      activationPercentage = 0.05; // 5% profit to activate
      trailPercentage = 0.03; // 3% trail
      break;
    case TokenRiskCategory.LOW:
      activationPercentage = 0.03; // 3% profit to activate
      trailPercentage = 0.02; // 2% trail
      break;
  }
  
  // Check if trailing stop is active
  const percentageGain = (currentPrice - entryPrice) / entryPrice;
  const isActive = percentageGain >= activationPercentage;
  
  return {
    activationPercentage,
    trailPercentage,
    isActive
  };
}

/**
 * Calculate time-based exit strategy
 * 
 * @param tokenAge Age category of the token
 * @param entryTime Entry time of the trade
 * @returns Time-based exit parameters
 */
export function calculateTimeBasedExit(
  tokenAge: TokenAgeCategory,
  entryTime: Date
): {
  maxHoldTime: number; // in hours
  timeRemaining: number; // in hours
  shouldExit: boolean;
} {
  let maxHoldTime = 168; // 7 days for established tokens
  
  // Adjust based on token age
  switch (tokenAge) {
    case TokenAgeCategory.NEW:
      maxHoldTime = 24; // 24 hours for new tokens
      break;
    case TokenAgeCategory.RECENT:
      maxHoldTime = 72; // 3 days for recent tokens
      break;
    case TokenAgeCategory.ESTABLISHED:
      maxHoldTime = 168; // 7 days for established tokens
      break;
  }
  
  // Calculate time elapsed since entry
  const hoursElapsed = (Date.now() - entryTime.getTime()) / (1000 * 60 * 60);
  
  // Calculate time remaining
  const timeRemaining = Math.max(0, maxHoldTime - hoursElapsed);
  
  // Determine if we should exit based on time
  const shouldExit = timeRemaining <= 0;
  
  return {
    maxHoldTime,
    timeRemaining,
    shouldExit
  };
}

/**
 * Assess liquidity based on volume and DEX presence
 */
function assessLiquidity(tokenData: any): number {
  let score = 0;

  // Check 24h volume
  const volume24h = tokenData.volume_24h || 0;
  if (volume24h > 100000) score += 4;
  else if (volume24h > 50000) score += 3;
  else if (volume24h > 10000) score += 2;
  else if (volume24h > 1000) score += 1;

  // Check number of DEXes
  const dexCount = tokenData.dexes ? tokenData.dexes.length : 0;
  if (dexCount >= 3) score += 3;
  else if (dexCount >= 2) score += 2;
  else if (dexCount >= 1) score += 1;

  // Check total value locked
  const tvl = tokenData.total_value_locked || 0;
  if (tvl > 1000000) score += 3;
  else if (tvl > 500000) score += 2;
  else if (tvl > 100000) score += 1;

  return Math.min(score, 10);
}

/**
 * Assess rugpull risk based on various factors
 */
function assessRugpullRisk(tokenData: any): number {
  let riskScore = 0;

  // Check if token has very few data points (potential pump and dump)
  const dataPoints = tokenData.ohlcv ? tokenData.ohlcv.length : 0;
  if (dataPoints < 5) riskScore += 4;
  else if (dataPoints < 10) riskScore += 2;
  else if (dataPoints < 20) riskScore += 1;

  // Check for extreme price volatility in short time
  if (tokenData.ohlcv && tokenData.ohlcv.length > 1) {
    const prices = tokenData.ohlcv.map((candle: any) =>
      candle.close || candle.c || (Array.isArray(candle) ? candle[4] : 0)
    );
    const maxPrice = Math.max(...prices);
    const minPrice = Math.min(...prices);
    const priceRange = ((maxPrice - minPrice) / minPrice) * 100;

    if (priceRange > 500) riskScore += 3; // 500%+ swing
    else if (priceRange > 200) riskScore += 2; // 200%+ swing
    else if (priceRange > 100) riskScore += 1; // 100%+ swing
  }

  // Check volume concentration (if most volume in very few candles)
  if (tokenData.ohlcv && tokenData.ohlcv.length > 2) {
    const volumes = tokenData.ohlcv.map((candle: any) =>
      candle.volume || candle.v || (Array.isArray(candle) ? candle[5] : 0)
    );
    const totalVolume = volumes.reduce((sum: number, vol: number) => sum + vol, 0);
    const maxVolume = Math.max(...volumes);

    // If one candle has >50% of total volume, it's suspicious
    if (maxVolume > totalVolume * 0.5) riskScore += 2;
    else if (maxVolume > totalVolume * 0.3) riskScore += 1;
  }

  // Check for very low liquidity
  const volume24h = tokenData.volume_24h || 0;
  if (volume24h < 1000) riskScore += 1;

  return Math.min(riskScore, 10);
}

/**
 * Assess market maturity based on data availability and consistency
 */
function assessMarketMaturity(tokenData: any): number {
  let score = 0;

  // Check number of price data points
  const dataPoints = tokenData.ohlcv ? tokenData.ohlcv.length : 0;
  if (dataPoints >= 100) score += 4;
  else if (dataPoints >= 50) score += 3;
  else if (dataPoints >= 20) score += 2;
  else if (dataPoints >= 10) score += 1;

  // Check consistency of trading (no large gaps)
  if (tokenData.ohlcv && tokenData.ohlcv.length > 5) {
    const volumes = tokenData.ohlcv.map((candle: any) =>
      candle.volume || candle.v || (Array.isArray(candle) ? candle[5] : 0)
    );
    const nonZeroVolumes = volumes.filter((v: number) => v > 0).length;
    const consistencyRatio = nonZeroVolumes / volumes.length;

    if (consistencyRatio > 0.8) score += 3;
    else if (consistencyRatio > 0.6) score += 2;
    else if (consistencyRatio > 0.4) score += 1;
  }

  // Check for multiple DEX presence (indicates established trading)
  const dexCount = tokenData.dexes ? tokenData.dexes.length : 0;
  if (dexCount >= 3) score += 3;
  else if (dexCount >= 2) score += 2;
  else if (dexCount >= 1) score += 1;

  return Math.min(score, 10);
}
