/**
 * API endpoints for user configuration management
 * Provides REST API for frontend integration
 */

import express, { Request, Response } from 'express';
import { userConfigManager } from '../config/user-config-manager.js';
import { UserTradingConfig, PRESET_CONFIGS, validateUserConfig } from '../config/user-trading-config.js';

const router = express.Router();

/**
 * GET /api/config/:userId
 * Get user configuration
 */
router.get('/config/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const config = await userConfigManager.getUserConfig(userId);
    
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('[API] Error getting user config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user configuration'
    });
  }
});

/**
 * POST /api/config/:userId
 * Create or update user configuration
 */
router.post('/config/:userId', (async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const updates = req.body;

    // Validate the configuration
    const errors = validateUserConfig(updates);
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid configuration',
        details: errors
      });
    }

    const config = await userConfigManager.updateUserConfig(userId, updates);

    res.json({
      success: true,
      data: config,
      message: 'Configuration updated successfully'
    });
  } catch (error) {
    console.error('[API] Error updating user config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user configuration'
    });
  }
}) as express.RequestHandler);

/**
 * POST /api/config/:userId/preset
 * Apply a preset configuration
 */
router.post('/config/:userId/preset', (async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { preset } = req.body;

    if (!preset || !PRESET_CONFIGS[preset as keyof typeof PRESET_CONFIGS]) {
      return res.status(400).json({
        success: false,
        error: 'Invalid preset. Available presets: conservative, aggressive'
      });
    }

    const config = await userConfigManager.createDefaultConfig(userId, preset);

    res.json({
      success: true,
      data: config,
      message: `${preset} preset applied successfully`
    });
  } catch (error) {
    console.error('[API] Error applying preset:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to apply preset configuration'
    });
  }
}) as express.RequestHandler);

/**
 * GET /api/config/:userId/trading-params
 * Get processed trading parameters for the bot
 */
router.get('/config/:userId/trading-params', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const config = await userConfigManager.getUserConfig(userId);
    const tradingParams = userConfigManager.applyConfigToTradingParams(config);
    
    res.json({
      success: true,
      data: tradingParams
    });
  } catch (error) {
    console.error('[API] Error getting trading params:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get trading parameters'
    });
  }
});

/**
 * DELETE /api/config/:userId
 * Delete user configuration
 */
router.delete('/config/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    await userConfigManager.deleteUserConfig(userId);

    res.json({
      success: true,
      message: 'Configuration deleted successfully'
    });
  } catch (error) {
    console.error('[API] Error deleting user config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete user configuration'
    });
  }
});

/**
 * GET /api/config/presets
 * Get available preset configurations
 */
router.get('/config/presets', async (req: Request, res: Response) => {
  try {
    res.json({
      success: true,
      data: {
        presets: Object.keys(PRESET_CONFIGS),
        configurations: PRESET_CONFIGS
      }
    });
  } catch (error) {
    console.error('[API] Error getting presets:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get preset configurations'
    });
  }
});

/**
 * POST /api/config/validate
 * Validate configuration without saving
 */
router.post('/config/validate', async (req: Request, res: Response) => {
  try {
    const config = req.body;
    const errors = validateUserConfig(config);

    res.json({
      success: errors.length === 0,
      valid: errors.length === 0,
      errors: errors
    });
  } catch (error) {
    console.error('[API] Error validating config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate configuration'
    });
  }
});

/**
 * GET /api/config/admin/all
 * Get all user configurations (admin only)
 */
router.get('/config/admin/all', async (req: Request, res: Response) => {
  try {
    // TODO: Add admin authentication middleware
    const configs = await userConfigManager.getAllUserConfigs();

    res.json({
      success: true,
      data: configs,
      count: configs.length
    });
  } catch (error) {
    console.error('[API] Error getting all configs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get all configurations'
    });
  }
});

/**
 * POST /api/config/admin/clear-cache
 * Clear configuration cache (admin only)
 */
router.post('/config/admin/clear-cache', async (req: Request, res: Response) => {
  try {
    // TODO: Add admin authentication middleware
    userConfigManager.clearCache();

    res.json({
      success: true,
      message: 'Configuration cache cleared successfully'
    });
  } catch (error) {
    console.error('[API] Error clearing cache:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear configuration cache'
    });
  }
});

export default router;
