/**
 * Express server for user configuration API
 * Provides REST endpoints for frontend integration
 */

import express from 'express';
import cors from 'cors';
import userConfigRouter from './user-config-api.js';
import managedWalletRouter from './managed-wallet-api.js';

const app = express();
const PORT = process.env.API_PORT || 4114;

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use((req, res, next) => {
  console.log(`[API] ${req.method} ${req.path} - ${new Date().toISOString()}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'MISTER Trading Bot API'
  });
});

// API routes
app.use('/api', userConfigRouter);
app.use('/api', managedWalletRouter);

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('[API] Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

/**
 * Start the API server
 */
export function startApiServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      const server = app.listen(PORT, () => {
        console.log(`[API] MISTER Trading Bot API server running on port ${PORT}`);
        console.log(`[API] Health check: http://localhost:${PORT}/health`);
        console.log(`[API] API endpoints: http://localhost:${PORT}/api`);
        resolve();
      });

      server.on('error', (error) => {
        console.error('[API] Server error:', error);
        reject(error);
      });

      // Graceful shutdown
      process.on('SIGTERM', () => {
        console.log('[API] Received SIGTERM, shutting down gracefully');
        server.close(() => {
          console.log('[API] Server closed');
          process.exit(0);
        });
      });

      process.on('SIGINT', () => {
        console.log('[API] Received SIGINT, shutting down gracefully');
        server.close(() => {
          console.log('[API] Server closed');
          process.exit(0);
        });
      });

    } catch (error) {
      console.error('[API] Failed to start server:', error);
      reject(error);
    }
  });
}

// Start server if this file is run directly
if (require.main === module) {
  startApiServer().catch(error => {
    console.error('[API] Failed to start API server:', error);
    process.exit(1);
  });
}

export default app;
