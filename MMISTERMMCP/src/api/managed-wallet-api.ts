/**
 * Managed Wallet API Endpoints
 * REST API for frontend to manage wallets and execute trades
 * SEPARATE FROM STRIKE FINANCE - This is for frontend managed wallet trading
 */

import express, { Request, Response } from 'express';
import { managedWalletManager } from '../managed-wallets/wallet-manager.js';
import { managedWalletTradingService } from '../managed-wallets/trading-service.js';

const router = express.Router();

/**
 * POST /api/wallets/create
 * Create a new managed wallet for a user
 */
router.post('/wallets/create', (async (req: Request, res: Response) => {
  try {
    const { userId, displayName } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId is required'
      });
    }
    
    const result = await managedWalletManager.createManagedWallet(userId, displayName);
    
    res.json({
      success: true,
      data: {
        wallet: result.wallet,
        mnemonic: result.mnemonic // Only returned during creation
      },
      message: 'Managed wallet created successfully'
    });
  } catch (error) {
    console.error('[API] Error creating managed wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create managed wallet'
    });
  }
}) as express.RequestHandler);

/**
 * GET /api/wallets/:userId
 * Get all wallets for a user
 */
router.get('/wallets/:userId', (async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const wallets = await managedWalletManager.getUserWallets(userId);
    
    res.json({
      success: true,
      data: wallets,
      count: wallets.length
    });
  } catch (error) {
    console.error('[API] Error getting user wallets:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user wallets'
    });
  }
}) as express.RequestHandler);

/**
 * GET /api/wallets/wallet/:walletId
 * Get specific wallet details
 */
router.get('/wallets/wallet/:walletId', (async (req: Request, res: Response) => {
  try {
    const { walletId } = req.params;
    const wallet = await managedWalletManager.getWallet(walletId);
    
    if (!wallet) {
      return res.status(404).json({
        success: false,
        error: 'Wallet not found'
      });
    }
    
    res.json({
      success: true,
      data: wallet
    });
  } catch (error) {
    console.error('[API] Error getting wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get wallet'
    });
  }
}) as express.RequestHandler);

/**
 * PUT /api/wallets/wallet/:walletId
 * Update wallet configuration
 */
router.put('/wallets/wallet/:walletId', (async (req: Request, res: Response) => {
  try {
    const { walletId } = req.params;
    const updates = req.body;
    
    const wallet = await managedWalletManager.updateWalletConfig(walletId, updates);
    
    if (!wallet) {
      return res.status(404).json({
        success: false,
        error: 'Wallet not found'
      });
    }
    
    res.json({
      success: true,
      data: wallet,
      message: 'Wallet updated successfully'
    });
  } catch (error) {
    console.error('[API] Error updating wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update wallet'
    });
  }
}) as express.RequestHandler);

/**
 * DELETE /api/wallets/wallet/:walletId
 * Delete a managed wallet
 */
router.delete('/wallets/wallet/:walletId', (async (req: Request, res: Response) => {
  try {
    const { walletId } = req.params;
    const success = await managedWalletManager.deleteWallet(walletId);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Wallet not found or failed to delete'
      });
    }
    
    res.json({
      success: true,
      message: 'Wallet deleted successfully'
    });
  } catch (error) {
    console.error('[API] Error deleting wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete wallet'
    });
  }
}) as express.RequestHandler);

/**
 * POST /api/trading/start
 * Start automated trading for a wallet
 */
router.post('/trading/start', (async (req: Request, res: Response) => {
  try {
    const { userId, walletId, settings } = req.body;
    
    if (!userId || !walletId) {
      return res.status(400).json({
        success: false,
        error: 'userId and walletId are required'
      });
    }
    
    const session = await managedWalletTradingService.startTradingSession(userId, walletId, settings);
    
    res.json({
      success: true,
      data: session,
      message: 'Trading session started successfully'
    });
  } catch (error) {
    console.error('[API] Error starting trading session:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to start trading session'
    });
  }
}) as express.RequestHandler);

/**
 * POST /api/trading/stop
 * Stop automated trading for a wallet
 */
router.post('/trading/stop', (async (req: Request, res: Response) => {
  try {
    const { walletId } = req.body;
    
    if (!walletId) {
      return res.status(400).json({
        success: false,
        error: 'walletId is required'
      });
    }
    
    const success = await managedWalletTradingService.stopTradingSession(walletId);
    
    res.json({
      success,
      message: success ? 'Trading session stopped successfully' : 'No active trading session found'
    });
  } catch (error) {
    console.error('[API] Error stopping trading session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to stop trading session'
    });
  }
}) as express.RequestHandler);

/**
 * GET /api/trading/status/:walletId
 * Get trading session status for a wallet
 */
router.get('/trading/status/:walletId', (async (req: Request, res: Response) => {
  try {
    const { walletId } = req.params;
    
    const session = managedWalletTradingService.getTradingSession(walletId);
    const stats = managedWalletTradingService.getSessionStats(walletId);
    
    res.json({
      success: true,
      data: {
        session,
        stats,
        isActive: !!session?.isActive
      }
    });
  } catch (error) {
    console.error('[API] Error getting trading status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get trading status'
    });
  }
}) as express.RequestHandler);

/**
 * GET /api/trading/sessions/:userId
 * Get all active trading sessions for a user
 */
router.get('/trading/sessions/:userId', (async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const sessions = managedWalletTradingService.getUserTradingSessions(userId);
    
    res.json({
      success: true,
      data: sessions,
      count: sessions.length
    });
  } catch (error) {
    console.error('[API] Error getting user trading sessions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get trading sessions'
    });
  }
}) as express.RequestHandler);

/**
 * POST /api/trading/manual-trade
 * Execute a manual trade for a managed wallet
 */
router.post('/trading/manual-trade', (async (req: Request, res: Response) => {
  try {
    const { walletId, ticker, direction, amount } = req.body;
    
    if (!walletId || !ticker || !direction || !amount) {
      return res.status(400).json({
        success: false,
        error: 'walletId, ticker, direction, and amount are required'
      });
    }
    
    if (!['buy', 'sell'].includes(direction)) {
      return res.status(400).json({
        success: false,
        error: 'direction must be "buy" or "sell"'
      });
    }
    
    const result = await managedWalletTradingService.executeManualTrade(
      walletId,
      ticker,
      direction,
      Math.round(amount) // Ensure whole numbers
    );
    
    res.json({
      success: result.success,
      data: result,
      message: result.success ? 'Trade executed successfully' : 'Trade failed'
    });
  } catch (error) {
    console.error('[API] Error executing manual trade:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute manual trade'
    });
  }
}) as express.RequestHandler);

/**
 * PUT /api/trading/settings/:walletId
 * Update trading settings for a wallet
 */
router.put('/trading/settings/:walletId', (async (req: Request, res: Response) => {
  try {
    const { walletId } = req.params;
    const settings = req.body;
    
    const session = await managedWalletTradingService.updateSessionSettings(walletId, settings);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'No active trading session found'
      });
    }
    
    res.json({
      success: true,
      data: session,
      message: 'Trading settings updated successfully'
    });
  } catch (error) {
    console.error('[API] Error updating trading settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update trading settings'
    });
  }
}) as express.RequestHandler);

export default router;
