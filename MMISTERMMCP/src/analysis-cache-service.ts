import fs from 'fs';
import path from 'path';

export interface CachedAnalysis {
  ticker: string;
  timestamp: string;
  currentPrice: number;
  priceChange24h: number;
  
  technicalAnalysis: {
    rsi: {
      '15m': number;
      '1h': number;
      '4h': number;
      signal: 'oversold' | 'neutral' | 'overbought';
    };
    macd: {
      signal: 'bullish' | 'bearish' | 'neutral';
      histogram: number;
    };
    bollinger: {
      position: 'upper' | 'middle' | 'lower';
      squeeze: boolean;
    };
    support: number;
    resistance: number;
  };
  
  sentiment: {
    twitter: {
      score: number;
      volume: number;
      trending: boolean;
    };
    overall: 'bullish' | 'bearish' | 'neutral';
  };
  
  decision: {
    action: 'BUY' | 'SELL' | 'HOLD';
    confidence: number;
    reasoning: string[];
    targetPrice?: number;
    stopLoss?: number;
    positionSize: number;
  };
  
  risk: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    liquidityScore: number;
  };
}

export interface AnalysisCache {
  lastUpdated: string;
  nextUpdate: string;
  updateInterval: number; // minutes
  currentAnalysis: CachedAnalysis | null;
  analysisHistory: CachedAnalysis[];
  metadata: {
    totalRuns: number;
    successfulRuns: number;
    lastError?: string;
    apiCallsUsed: number;
    rateLimitStatus: 'ok' | 'warning' | 'limited';
  };
}

export class AnalysisCacheService {
  private cacheFilePath: string;
  private cache!: AnalysisCache;
  private updateInterval: number = 60; // Default: 60 minutes
  private isRunning: boolean = false;
  private timer: NodeJS.Timeout | null = null;

  constructor(cacheDir: string = './cache') {
    this.cacheFilePath = path.join(cacheDir, 'analysis-cache.json');
    this.ensureCacheDirectory();
    this.loadCache();
  }

  private ensureCacheDirectory(): void {
    const cacheDir = path.dirname(this.cacheFilePath);
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }
  }

  private loadCache(): void {
    try {
      if (fs.existsSync(this.cacheFilePath)) {
        const cacheData = fs.readFileSync(this.cacheFilePath, 'utf8');
        this.cache = JSON.parse(cacheData);
        console.log('📋 Analysis cache loaded successfully');
      } else {
        this.initializeCache();
      }
    } catch (error) {
      console.error('❌ Error loading cache, initializing new cache:', error);
      this.initializeCache();
    }
  }

  private initializeCache(): void {
    this.cache = {
      lastUpdated: '',
      nextUpdate: '',
      updateInterval: this.updateInterval,
      currentAnalysis: null,
      analysisHistory: [],
      metadata: {
        totalRuns: 0,
        successfulRuns: 0,
        apiCallsUsed: 0,
        rateLimitStatus: 'ok'
      }
    };
    this.saveCache();
    console.log('🆕 New analysis cache initialized');
  }

  private saveCache(): void {
    try {
      fs.writeFileSync(this.cacheFilePath, JSON.stringify(this.cache, null, 2));
    } catch (error) {
      console.error('❌ Error saving cache:', error);
    }
  }

  // Get current cached analysis (for users)
  public getCurrentAnalysis(): CachedAnalysis | null {
    return this.cache.currentAnalysis;
  }

  // Get analysis history (for users)
  public getAnalysisHistory(): CachedAnalysis[] {
    return this.cache.analysisHistory;
  }

  // Get cache metadata (for admin)
  public getCacheStatus(): AnalysisCache {
    return this.cache;
  }

  // Update analysis data (called by scheduler)
  public async updateAnalysis(newAnalysis: CachedAnalysis): Promise<void> {
    try {
      const now = new Date();
      
      // Update current analysis
      this.cache.currentAnalysis = newAnalysis;
      
      // Add to history (keep last 50)
      this.cache.analysisHistory.unshift(newAnalysis);
      this.cache.analysisHistory = this.cache.analysisHistory.slice(0, 50);
      
      // Update metadata
      this.cache.lastUpdated = now.toISOString();
      this.cache.nextUpdate = new Date(now.getTime() + this.updateInterval * 60 * 1000).toISOString();
      this.cache.metadata.totalRuns++;
      this.cache.metadata.successfulRuns++;
      this.cache.metadata.apiCallsUsed++;
      
      this.saveCache();
      
      console.log(`✅ Analysis cache updated: ${newAnalysis.ticker} - ${newAnalysis.decision.action}`);
    } catch (error) {
      console.error('❌ Error updating analysis cache:', error);
      this.cache.metadata.lastError = error instanceof Error ? error.message : String(error);
      this.saveCache();
    }
  }

  // Set update interval (admin only)
  public setUpdateInterval(minutes: number): void {
    this.updateInterval = minutes;
    this.cache.updateInterval = minutes;
    this.saveCache();
    
    // Restart timer with new interval
    if (this.isRunning) {
      this.stopScheduler();
      this.startScheduler();
    }
    
    console.log(`⏰ Analysis update interval set to ${minutes} minutes`);
  }

  // Start the analysis scheduler
  public startScheduler(): void {
    if (this.isRunning) {
      console.log('⚠️ Analysis scheduler already running');
      return;
    }

    this.isRunning = true;
    const intervalMs = this.updateInterval * 60 * 1000;
    
    this.timer = setInterval(async () => {
      console.log('🔄 Running scheduled analysis...');
      await this.runAnalysis();
    }, intervalMs);

    console.log(`🚀 Analysis scheduler started (${this.updateInterval} minute intervals)`);
  }

  // Stop the analysis scheduler
  public stopScheduler(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.isRunning = false;
    console.log('⏹️ Analysis scheduler stopped');
  }

  // Manual analysis trigger (admin only)
  public async triggerAnalysis(): Promise<CachedAnalysis | null> {
    console.log('🎯 Manual analysis triggered');
    return await this.runAnalysis();
  }

  // Run the actual analysis (calls external APIs)
  private async runAnalysis(): Promise<CachedAnalysis | null> {
    try {
      // This would call the actual analysis logic
      // For now, we'll generate realistic mock data
      const tokens = ['SNEK', 'WMTX', 'HOSKY', 'BOOK', 'AGIX'];
      const randomToken = tokens[Math.floor(Math.random() * tokens.length)];
      
      const analysis: CachedAnalysis = {
        ticker: randomToken,
        timestamp: new Date().toISOString(),
        currentPrice: Math.random() * 0.01,
        priceChange24h: (Math.random() - 0.5) * 20,
        
        technicalAnalysis: {
          rsi: {
            '15m': 30 + Math.random() * 40,
            '1h': 30 + Math.random() * 40,
            '4h': 30 + Math.random() * 40,
            signal: ['oversold', 'neutral', 'overbought'][Math.floor(Math.random() * 3)] as any
          },
          macd: {
            signal: ['bullish', 'bearish', 'neutral'][Math.floor(Math.random() * 3)] as any,
            histogram: (Math.random() - 0.5) * 0.01
          },
          bollinger: {
            position: ['upper', 'middle', 'lower'][Math.floor(Math.random() * 3)] as any,
            squeeze: Math.random() > 0.5
          },
          support: Math.random() * 0.008,
          resistance: Math.random() * 0.012 + 0.008
        },
        
        sentiment: {
          twitter: {
            score: Math.random(),
            volume: Math.floor(Math.random() * 500),
            trending: Math.random() > 0.7
          },
          overall: ['bullish', 'bearish', 'neutral'][Math.floor(Math.random() * 3)] as any
        },
        
        decision: {
          action: ['BUY', 'SELL', 'HOLD'][Math.floor(Math.random() * 3)] as any,
          confidence: Math.floor(Math.random() * 6) + 5,
          reasoning: [
            'Technical indicators showing strong momentum',
            'Positive sentiment across social media',
            'Good liquidity and volume',
            'Price action respecting key levels',
            'Multi-timeframe confluence detected'
          ].slice(0, Math.floor(Math.random() * 3) + 2),
          targetPrice: Math.random() * 0.015,
          stopLoss: Math.random() * 0.005,
          positionSize: Math.floor(Math.random() * 100) + 25
        },
        
        risk: {
          level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
          factors: [
            'Market volatility present',
            'Some resistance at current levels',
            'Lower volume in recent sessions'
          ].slice(0, Math.floor(Math.random() * 2) + 1),
          liquidityScore: Math.floor(Math.random() * 40) + 60
        }
      };

      await this.updateAnalysis(analysis);
      return analysis;
      
    } catch (error) {
      console.error('❌ Error running analysis:', error);
      this.cache.metadata.lastError = error instanceof Error ? error.message : String(error);
      this.cache.metadata.totalRuns++;
      this.saveCache();
      return null;
    }
  }

  // Check if cache is fresh (for rate limiting)
  public isCacheFresh(maxAgeMinutes: number = 5): boolean {
    if (!this.cache.lastUpdated) return false;
    
    const lastUpdate = new Date(this.cache.lastUpdated);
    const now = new Date();
    const ageMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60);
    
    return ageMinutes < maxAgeMinutes;
  }
}

// Singleton instance
export const analysisCacheService = new AnalysisCacheService();
