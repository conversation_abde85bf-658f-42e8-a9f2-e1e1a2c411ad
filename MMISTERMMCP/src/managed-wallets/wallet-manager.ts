/**
 * Managed Wallet System
 * Handles creation, storage, and management of user wallets for automated trading
 * SEPARATE FROM STRIKE FINANCE - This is for frontend managed wallet trading
 */

import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';
import { generateMnemonic, mnemonicToEntropy, entropyToMnemonic } from 'bip39';

export interface ManagedWallet {
  userId: string;
  walletId: string;
  displayName: string;
  address: string;
  stakeAddress?: string;
  encryptedSeed: string;
  createdAt: Date;
  lastUsed?: Date;
  isActive: boolean;
  balance?: {
    ada: number;
    tokens: Array<{
      unit: string;
      amount: number;
      ticker?: string;
    }>;
    lastUpdated: Date;
  };
  tradingConfig?: {
    autoTradingEnabled: boolean;
    maxDailyTrades: number;
    maxPositionSize: number;
    riskLevel: 'conservative' | 'moderate' | 'aggressive';
  };
}

export interface WalletCreationResult {
  wallet: ManagedWallet;
  mnemonic: string; // Only returned once during creation
}

export class ManagedWalletManager {
  private walletsDir: string;
  private encryptionKey: string;
  
  constructor(walletsDir: string = './managed-wallets', encryptionKey?: string) {
    this.walletsDir = walletsDir;
    this.encryptionKey = encryptionKey || process.env.WALLET_ENCRYPTION_KEY || this.generateEncryptionKey();
    this.ensureWalletsDirectory();
  }
  
  /**
   * Generate a secure encryption key
   */
  private generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }
  
  /**
   * Ensure the wallets directory exists
   */
  private async ensureWalletsDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.walletsDir, { recursive: true });
    } catch (error) {
      console.error('[WALLET_MANAGER] Failed to create wallets directory:', error);
    }
  }
  
  /**
   * Encrypt sensitive data
   */
  private encrypt(text: string): string {
    const algorithm = 'aes-256-gcm';
    const key = Buffer.from(this.encryptionKey, 'hex');
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  /**
   * Decrypt sensitive data
   */
  private decrypt(encryptedData: string): string {
    const algorithm = 'aes-256-gcm';
    const key = Buffer.from(this.encryptionKey, 'hex');

    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
  
  /**
   * Create a new managed wallet for a user
   */
  async createManagedWallet(
    userId: string, 
    displayName: string = 'Trading Wallet'
  ): Promise<WalletCreationResult> {
    try {
      // Generate new mnemonic
      const mnemonic = generateMnemonic(256); // 24 words for extra security
      
      // TODO: Generate Cardano address from mnemonic
      // For now, using placeholder - will implement with Cardano libraries
      const walletId = crypto.randomUUID();
      const address = `addr1_placeholder_${walletId.substring(0, 8)}`;
      const stakeAddress = `stake1_placeholder_${walletId.substring(0, 8)}`;
      
      // Encrypt the mnemonic
      const encryptedSeed = this.encrypt(mnemonic);
      
      const wallet: ManagedWallet = {
        userId,
        walletId,
        displayName,
        address,
        stakeAddress,
        encryptedSeed,
        createdAt: new Date(),
        isActive: true,
        tradingConfig: {
          autoTradingEnabled: false,
          maxDailyTrades: 10,
          maxPositionSize: 100, // 100 ADA max position
          riskLevel: 'moderate'
        }
      };
      
      // Save wallet to file
      await this.saveWallet(wallet);
      
      console.log(`[WALLET_MANAGER] Created managed wallet ${walletId} for user ${userId}`);
      
      return {
        wallet,
        mnemonic // Only returned during creation
      };
    } catch (error) {
      console.error('[WALLET_MANAGER] Failed to create managed wallet:', error);
      throw error;
    }
  }
  
  /**
   * Get all wallets for a user
   */
  async getUserWallets(userId: string): Promise<ManagedWallet[]> {
    try {
      const files = await fs.readdir(this.walletsDir);
      const walletFiles = files.filter(file => file.endsWith('.json'));
      
      const userWallets: ManagedWallet[] = [];
      
      for (const file of walletFiles) {
        try {
          const walletPath = path.join(this.walletsDir, file);
          const walletData = await fs.readFile(walletPath, 'utf-8');
          const wallet: ManagedWallet = JSON.parse(walletData);
          
          // Convert date strings back to Date objects
          wallet.createdAt = new Date(wallet.createdAt);
          if (wallet.lastUsed) wallet.lastUsed = new Date(wallet.lastUsed);
          if (wallet.balance?.lastUpdated) wallet.balance.lastUpdated = new Date(wallet.balance.lastUpdated);
          
          if (wallet.userId === userId) {
            userWallets.push(wallet);
          }
        } catch (error) {
          console.error(`[WALLET_MANAGER] Failed to load wallet ${file}:`, error);
        }
      }
      
      return userWallets.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    } catch (error) {
      console.error('[WALLET_MANAGER] Failed to get user wallets:', error);
      return [];
    }
  }
  
  /**
   * Get a specific wallet by ID
   */
  async getWallet(walletId: string): Promise<ManagedWallet | null> {
    try {
      const walletPath = path.join(this.walletsDir, `${walletId}.json`);
      const walletData = await fs.readFile(walletPath, 'utf-8');
      const wallet: ManagedWallet = JSON.parse(walletData);
      
      // Convert date strings back to Date objects
      wallet.createdAt = new Date(wallet.createdAt);
      if (wallet.lastUsed) wallet.lastUsed = new Date(wallet.lastUsed);
      if (wallet.balance?.lastUpdated) wallet.balance.lastUpdated = new Date(wallet.balance.lastUpdated);
      
      return wallet;
    } catch (error) {
      console.error(`[WALLET_MANAGER] Failed to get wallet ${walletId}:`, error);
      return null;
    }
  }
  
  /**
   * Get decrypted mnemonic for a wallet (use with extreme caution)
   */
  async getWalletMnemonic(walletId: string): Promise<string | null> {
    try {
      const wallet = await this.getWallet(walletId);
      if (!wallet) return null;
      
      return this.decrypt(wallet.encryptedSeed);
    } catch (error) {
      console.error(`[WALLET_MANAGER] Failed to decrypt mnemonic for ${walletId}:`, error);
      return null;
    }
  }
  
  /**
   * Update wallet configuration
   */
  async updateWalletConfig(
    walletId: string, 
    updates: Partial<ManagedWallet>
  ): Promise<ManagedWallet | null> {
    try {
      const wallet = await this.getWallet(walletId);
      if (!wallet) return null;
      
      // Merge updates
      const updatedWallet: ManagedWallet = {
        ...wallet,
        ...updates,
        walletId, // Ensure ID doesn't change
        lastUsed: new Date()
      };
      
      await this.saveWallet(updatedWallet);
      return updatedWallet;
    } catch (error) {
      console.error(`[WALLET_MANAGER] Failed to update wallet ${walletId}:`, error);
      return null;
    }
  }
  
  /**
   * Save wallet to file
   */
  private async saveWallet(wallet: ManagedWallet): Promise<void> {
    try {
      const walletPath = path.join(this.walletsDir, `${wallet.walletId}.json`);
      await fs.writeFile(walletPath, JSON.stringify(wallet, null, 2));
    } catch (error) {
      console.error(`[WALLET_MANAGER] Failed to save wallet ${wallet.walletId}:`, error);
      throw error;
    }
  }
  
  /**
   * Delete a managed wallet
   */
  async deleteWallet(walletId: string): Promise<boolean> {
    try {
      const walletPath = path.join(this.walletsDir, `${walletId}.json`);
      await fs.unlink(walletPath);
      console.log(`[WALLET_MANAGER] Deleted wallet ${walletId}`);
      return true;
    } catch (error) {
      console.error(`[WALLET_MANAGER] Failed to delete wallet ${walletId}:`, error);
      return false;
    }
  }
  
  /**
   * Check if a wallet has sufficient balance for trading
   */
  async checkWalletBalance(walletId: string, minAda: number = 10): Promise<boolean> {
    try {
      const wallet = await this.getWallet(walletId);
      if (!wallet || !wallet.balance) return false;
      
      return wallet.balance.ada >= minAda;
    } catch (error) {
      console.error(`[WALLET_MANAGER] Failed to check balance for ${walletId}:`, error);
      return false;
    }
  }
}

// Global instance
export const managedWalletManager = new ManagedWalletManager();
