/**
 * Managed Wallet Trading Service
 * Provides trading functionality for managed wallets using the MISTER trading bot logic
 * SEPARATE FROM STRIKE FINANCE - This is for frontend managed wallet trading
 */

import { managedWalletManager, ManagedWallet } from './wallet-manager.js';
import { getUserTradingParams } from '../config/user-config-manager.js';

export interface TradingSession {
  sessionId: string;
  userId: string;
  walletId: string;
  isActive: boolean;
  startedAt: Date;
  lastTradeAt?: Date;
  tradesExecuted: number;
  totalVolume: number;
  pnl: number;
  settings: {
    maxDailyTrades: number;
    maxPositionSize: number;
    riskLevel: 'conservative' | 'moderate' | 'aggressive';
    autoTradingEnabled: boolean;
  };
}

export interface TradeResult {
  success: boolean;
  tradeId?: string;
  ticker: string;
  direction: 'buy' | 'sell';
  amount: number;
  price?: number;
  txHash?: string;
  error?: string;
  timestamp: Date;
}

export class ManagedWalletTradingService {
  private activeSessions = new Map<string, TradingSession>();
  private discordBot?: any;

  constructor(discordBot?: any) {
    this.discordBot = discordBot;
  }
  
  /**
   * Start a trading session for a managed wallet
   */
  async startTradingSession(
    userId: string, 
    walletId: string,
    customSettings?: Partial<TradingSession['settings']>
  ): Promise<TradingSession> {
    try {
      // Get the managed wallet
      const wallet = await managedWalletManager.getWallet(walletId);
      if (!wallet) {
        throw new Error(`Wallet ${walletId} not found`);
      }
      
      if (wallet.userId !== userId) {
        throw new Error(`Wallet ${walletId} does not belong to user ${userId}`);
      }
      
      // Check if wallet has sufficient balance
      const hasBalance = await managedWalletManager.checkWalletBalance(walletId, 10);
      if (!hasBalance) {
        throw new Error(`Wallet ${walletId} has insufficient balance for trading`);
      }
      
      // Stop any existing session for this wallet
      await this.stopTradingSession(walletId);
      
      // Create new session
      const sessionId = `session_${walletId}_${Date.now()}`;
      const session: TradingSession = {
        sessionId,
        userId,
        walletId,
        isActive: true,
        startedAt: new Date(),
        tradesExecuted: 0,
        totalVolume: 0,
        pnl: 0,
        settings: {
          maxDailyTrades: customSettings?.maxDailyTrades || wallet.tradingConfig?.maxDailyTrades || 10,
          maxPositionSize: customSettings?.maxPositionSize || wallet.tradingConfig?.maxPositionSize || 100,
          riskLevel: customSettings?.riskLevel || wallet.tradingConfig?.riskLevel || 'moderate',
          autoTradingEnabled: customSettings?.autoTradingEnabled ?? wallet.tradingConfig?.autoTradingEnabled ?? true
        }
      };
      
      this.activeSessions.set(walletId, session);
      
      console.log(`[TRADING_SERVICE] Started trading session ${sessionId} for wallet ${walletId}`);
      
      // Start the trading loop for this wallet
      this.startTradingLoop(session);
      
      return session;
    } catch (error) {
      console.error('[TRADING_SERVICE] Failed to start trading session:', error);
      throw error;
    }
  }
  
  /**
   * Stop a trading session
   */
  async stopTradingSession(walletId: string): Promise<boolean> {
    try {
      const session = this.activeSessions.get(walletId);
      if (session) {
        session.isActive = false;
        this.activeSessions.delete(walletId);
        console.log(`[TRADING_SERVICE] Stopped trading session for wallet ${walletId}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('[TRADING_SERVICE] Failed to stop trading session:', error);
      return false;
    }
  }
  
  /**
   * Get active trading session for a wallet
   */
  getTradingSession(walletId: string): TradingSession | null {
    return this.activeSessions.get(walletId) || null;
  }
  
  /**
   * Get all active sessions for a user
   */
  getUserTradingSessions(userId: string): TradingSession[] {
    return Array.from(this.activeSessions.values())
      .filter(session => session.userId === userId);
  }
  
  /**
   * Execute a manual trade for a managed wallet
   */
  async executeManualTrade(
    walletId: string,
    ticker: string,
    direction: 'buy' | 'sell',
    amount: number
  ): Promise<TradeResult> {
    try {
      const wallet = await managedWalletManager.getWallet(walletId);
      if (!wallet) {
        throw new Error(`Wallet ${walletId} not found`);
      }
      
      // Get wallet mnemonic for transaction signing
      const mnemonic = await managedWalletManager.getWalletMnemonic(walletId);
      if (!mnemonic) {
        throw new Error(`Failed to decrypt wallet ${walletId}`);
      }
      
      // TODO: Implement actual trade execution using the mnemonic
      // This will use the same logic as the current MISTER bot but with the managed wallet
      
      const tradeResult: TradeResult = {
        success: true,
        tradeId: `trade_${Date.now()}`,
        ticker,
        direction,
        amount: Math.round(amount), // Ensure whole numbers
        price: 0, // Will be filled by actual trade execution
        txHash: `tx_placeholder_${Date.now()}`,
        timestamp: new Date()
      };
      
      // Update session if active
      const session = this.activeSessions.get(walletId);
      if (session) {
        session.tradesExecuted++;
        session.totalVolume += amount;
        session.lastTradeAt = new Date();
      }
      
      // Send Discord notification if configured
      if (this.discordBot) {
        // TODO: Send notification about managed wallet trade
      }
      
      console.log(`[TRADING_SERVICE] Executed ${direction} ${amount} ADA of ${ticker} for wallet ${walletId}`);
      
      return tradeResult;
    } catch (error) {
      console.error('[TRADING_SERVICE] Failed to execute manual trade:', error);
      return {
        success: false,
        ticker,
        direction,
        amount,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }
  
  /**
   * Start the automated trading loop for a session
   */
  private async startTradingLoop(session: TradingSession): Promise<void> {
    if (!session.settings.autoTradingEnabled) {
      console.log(`[TRADING_SERVICE] Auto-trading disabled for session ${session.sessionId}`);
      return;
    }
    
    console.log(`[TRADING_SERVICE] Starting trading loop for session ${session.sessionId}`);
    
    // Run trading loop every 30 minutes
    const tradingInterval = setInterval(async () => {
      if (!session.isActive) {
        clearInterval(tradingInterval);
        return;
      }
      
      try {
        await this.runTradingIteration(session);
      } catch (error) {
        console.error(`[TRADING_SERVICE] Error in trading loop for ${session.sessionId}:`, error);
      }
    }, 30 * 60 * 1000); // 30 minutes
    
    // Run first iteration immediately
    setTimeout(() => this.runTradingIteration(session), 5000); // 5 second delay
  }
  
  /**
   * Run a single trading iteration for a session
   */
  private async runTradingIteration(session: TradingSession): Promise<void> {
    try {
      console.log(`[TRADING_SERVICE] Running trading iteration for session ${session.sessionId}`);
      
      // Check daily trade limits
      const today = new Date().toDateString();
      if (session.tradesExecuted >= session.settings.maxDailyTrades) {
        console.log(`[TRADING_SERVICE] Daily trade limit reached for session ${session.sessionId}`);
        return;
      }
      
      // Get user trading parameters
      const userTradingParams = await getUserTradingParams(session.userId);
      
      // Get wallet for balance checking
      const wallet = await managedWalletManager.getWallet(session.walletId);
      if (!wallet) {
        console.error(`[TRADING_SERVICE] Wallet ${session.walletId} not found`);
        return;
      }
      
      // TODO: Implement the actual trading logic here
      // This will use the same logic as the current MISTER bot:
      // 1. Fetch top tokens from TapTools
      // 2. Analyze each token with multi-timeframe analysis
      // 3. Get LLM trading decision
      // 4. Apply user configuration and risk management
      // 5. Execute trades using the managed wallet's mnemonic
      
      console.log(`[TRADING_SERVICE] Trading iteration completed for session ${session.sessionId}`);
    } catch (error) {
      console.error(`[TRADING_SERVICE] Error in trading iteration for ${session.sessionId}:`, error);
    }
  }
  
  /**
   * Update trading session settings
   */
  async updateSessionSettings(
    walletId: string,
    settings: Partial<TradingSession['settings']>
  ): Promise<TradingSession | null> {
    try {
      const session = this.activeSessions.get(walletId);
      if (!session) return null;
      
      // Update settings
      session.settings = {
        ...session.settings,
        ...settings
      };
      
      console.log(`[TRADING_SERVICE] Updated settings for session ${session.sessionId}`);
      return session;
    } catch (error) {
      console.error('[TRADING_SERVICE] Failed to update session settings:', error);
      return null;
    }
  }
  
  /**
   * Get trading statistics for a session
   */
  getSessionStats(walletId: string): any {
    const session = this.activeSessions.get(walletId);
    if (!session) return null;
    
    const runtime = Date.now() - session.startedAt.getTime();
    const runtimeHours = runtime / (1000 * 60 * 60);
    
    return {
      sessionId: session.sessionId,
      isActive: session.isActive,
      runtime: runtimeHours,
      tradesExecuted: session.tradesExecuted,
      totalVolume: session.totalVolume,
      pnl: session.pnl,
      avgTradesPerHour: runtimeHours > 0 ? session.tradesExecuted / runtimeHours : 0,
      lastTradeAt: session.lastTradeAt
    };
  }
}

// Global instance
export const managedWalletTradingService = new ManagedWalletTradingService();
