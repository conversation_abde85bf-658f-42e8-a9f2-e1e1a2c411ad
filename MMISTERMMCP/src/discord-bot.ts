import { Client, GatewayIntentBits, TextChannel, EmbedBuilder, Colors } from 'discord.js';
import * as dotenv from 'dotenv';
import { getTokenHitRate, getTradesByToken } from './database';
import { LlmDecision } from './types';

dotenv.config();

const DISCORD_TOKEN = process.env.DISCORD_TOKEN;
const DISCORD_CHANNEL_ID = process.env.DISCORD_CHANNEL_ID;

if (!DISCORD_TOKEN) {
  console.error('Missing DISCORD_TOKEN in .env file');
  process.exit(1);
}

if (!DISCORD_CHANNEL_ID) {
  console.error('Missing DISCORD_CHANNEL_ID in .env file');
  process.exit(1);
}

/**
 * Discord bot for sending Talos analysis to a Discord channel
 */
class DiscordBot {
  private client: Client;
  private ready: boolean = false;
  private channelId: string;

  constructor() {
    // Store the channel ID after validation
    this.channelId = DISCORD_CHANNEL_ID as string;

    console.log(`[<PERSON><PERSON><PERSON><PERSON>] Initializing Discord bot with channel ID: ${this.channelId}`);

    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages
      ]
    });

    this.client.on('ready', () => {
      console.log(`[DISCORD] Logged in as ${this.client.user?.tag}!`);
      this.ready = true;
    });

    // Add more error handling and retry logic
    this.client.on('error', (error) => {
      console.error('[DISCORD] Client error:', error);
    });

    this.client.on('disconnect', () => {
      console.log('[DISCORD] Disconnected, attempting to reconnect...');
      setTimeout(() => {
        this.client.login(DISCORD_TOKEN).catch(err => {
          console.error('[DISCORD] Failed to reconnect:', err);
        });
      }, 5000);
    });

    // Login with retry logic
    const attemptLogin = (retries = 3) => {
      this.client.login(DISCORD_TOKEN).catch(err => {
        console.error(`[DISCORD] Failed to login (attempt ${4-retries}/3):`, err);
        if (retries > 0) {
          console.log(`[DISCORD] Retrying in 5 seconds...`);
          setTimeout(() => attemptLogin(retries - 1), 5000);
        } else {
          console.error('[DISCORD] Failed to login after multiple attempts');
        }
      });
    };

    attemptLogin();
  }

  /**
   * Get confidence explanation from either legacy or new structure
   */
  private getConfidenceExplanation(decision: LlmDecision): string {
    // Try to get from legacy structure first
    if (decision.reasoning?.confidence_explanation) {
      return decision.reasoning.confidence_explanation;
    }

    // Otherwise, construct explanation from the new structure
    if (decision.summary) {
      return decision.summary;
    }

    return 'No confidence explanation provided';
  }

  /**
   * Get price analysis from either legacy or new structure
   */
  private getPriceAnalysis(decision: LlmDecision): string {
    // Try to get from legacy structure first
    if (decision.reasoning?.price_analysis) {
      return decision.reasoning.price_analysis;
    }

    // Otherwise, construct from the new structure - checking multiple possible field names
    if (decision.technicalAnalysis?.priceAction) {
      return decision.technicalAnalysis.priceAction;
    }

    // Check for priceAnalysis in technicalAnalysis
    if (decision.technicalAnalysis?.priceAnalysis) {
      return decision.technicalAnalysis.priceAnalysis;
    }

    // Check for priceTrend in technicalAnalysis (used in some responses)
    if (decision.technicalAnalysis?.priceTrend) {
      return decision.technicalAnalysis.priceTrend;
    }

    // If we have technicalAnalysis but none of the specific price fields,
    // try to construct something meaningful from other available data
    if (decision.technicalAnalysis) {
      const parts = [];

      // Include chart patterns if available
      if (decision.technicalAnalysis.chartPatterns) {
        parts.push(decision.technicalAnalysis.chartPatterns);
      }

      // Include support/resistance if available
      if (decision.technicalAnalysis.supportResistance) {
        parts.push(decision.technicalAnalysis.supportResistance);
      }

      // If we found any relevant information, return it
      if (parts.length > 0) {
        return parts.join(' ');
      }
    }

    // Fallback to summary if we have it
    if (decision.summary) {
      return `Price info from summary: ${decision.summary}`;
    }

    return 'No price analysis provided';
  }

  /**
   * Get volume analysis from either legacy or new structure
   */
  private getVolumeAnalysis(decision: LlmDecision): string {
    // Try to get from legacy structure first
    if (decision.reasoning?.volume_analysis) {
      return decision.reasoning.volume_analysis;
    }

    // Otherwise, construct from the new structure
    if (decision.technicalAnalysis?.volumeAnalysis) {
      return decision.technicalAnalysis.volumeAnalysis;
    }

    return 'No volume analysis provided';
  }

  /**
   * Get technical indicators analysis
   */
  private getTechnicalIndicators(decision: LlmDecision, additionalData: any = {}): string {
    let indicators = '';

    // Check for RSI in the decision
    if (decision.technicalAnalysis?.technicalIndicators?.rsi) {
      indicators += decision.technicalAnalysis.technicalIndicators.rsi + '\n\n';
    }

    // Check for RSI in additional data
    if (additionalData?.technical_indicators?.rsi) {
      try {
        const rsi = additionalData.technical_indicators.rsi;
        if (rsi.value !== undefined && !isNaN(rsi.value)) {
          indicators += `RSI: ${parseFloat(rsi.value).toFixed(1)} (${rsi.interpretation || 'neutral'})\n`;
          indicators += `Trend: ${rsi.trend || 'neutral'}, Divergence: ${rsi.divergence || 'none'}\n\n`;
        } else {
          indicators += `RSI data available but value is invalid\n\n`;
        }
      } catch (error) {
        console.error('[DISCORD] Error formatting RSI data:', error);
        indicators += `RSI data available but could not be formatted\n\n`;
      }
    }

    // If no indicators found, return default message
    if (!indicators) {
      return 'No technical indicators available';
    }

    return indicators;
  }

  /**
   * Get risk assessment
   */
  private getRiskAssessment(decision: LlmDecision): string {
    // Check for risk assessment in the new structure
    if (decision.riskAssessment) {
      let riskText = '';

      if (decision.riskAssessment.riskRewardRatio) {
        const rrText = decision.riskAssessment.riskRewardRatio.length > 200
          ? decision.riskAssessment.riskRewardRatio.substring(0, 200) + '...'
          : decision.riskAssessment.riskRewardRatio;
        riskText += `Risk/Reward: ${rrText}\n`;
      }

      if (decision.riskAssessment.volatilityAssessment) {
        const volText = decision.riskAssessment.volatilityAssessment.length > 200
          ? decision.riskAssessment.volatilityAssessment.substring(0, 200) + '...'
          : decision.riskAssessment.volatilityAssessment;
        riskText += `Volatility: ${volText}\n`;
      }

      if (decision.riskAssessment.liquidityRisk) {
        const liqText = decision.riskAssessment.liquidityRisk.length > 200
          ? decision.riskAssessment.liquidityRisk.substring(0, 200) + '...'
          : decision.riskAssessment.liquidityRisk;
        riskText += `Liquidity Risk: ${liqText}\n`;
      }

      if (riskText) {
        // Final check: ensure total length doesn't exceed Discord limit (more aggressive)
        return riskText.length > 800 ? riskText.substring(0, 797) + '...' : riskText;
      }
    }

    // Check for risk assessment in the legacy structure
    if (decision.reasoning?.risk_assessment) {
      return decision.reasoning.risk_assessment;
    }

    return 'No risk assessment provided';
  }

  /**
   * Generate clickable links for token
   */
  private generateTokenLinks(ticker: string, unit: string, additionalData: any = {}): string {
    const links: string[] = [];

    // TapTools trading link (always available)
    const tapToolsUrl = `https://www.taptools.io/charts/token?unit=${unit}`;
    links.push(`🔗 [Trade on TapTools](${tapToolsUrl})`);

    // Policy ID display (formatted beautifully)
    if (unit && unit.length > 56) {
      const policyId = unit.substring(0, 56);
      const assetName = unit.substring(56);
      links.push(`📋 **Policy ID:** \`${policyId}\``);
      if (assetName) {
        links.push(`🏷️ **Asset Name:** \`${assetName}\``);
      }
    }

    // Social media links from TapTools data
    if (additionalData.socialLinks) {
      if (additionalData.socialLinks.twitter) {
        const twitterUsername = this.extractTwitterUsername(additionalData.socialLinks.twitter);
        if (twitterUsername) {
          links.push(`🐦 [Follow @${twitterUsername}](https://twitter.com/${twitterUsername})`);
        }
      }

      if (additionalData.socialLinks.discord) {
        links.push(`💬 [Join Discord](${additionalData.socialLinks.discord})`);
      }

      if (additionalData.socialLinks.telegram) {
        links.push(`📱 [Join Telegram](${additionalData.socialLinks.telegram})`);
      }

      if (additionalData.socialLinks.website) {
        links.push(`🌐 [Official Website](${additionalData.socialLinks.website})`);
      }
    }

    // DEX links based on pools data
    if (additionalData.pools && additionalData.pools.length > 0) {
      const pool = additionalData.pools[0]; // Use the first/main pool
      if (pool.dex) {
        const dexName = pool.dex.toLowerCase();
        if (dexName.includes('minswap')) {
          links.push(`🔄 [Trade on Minswap](https://app.minswap.org/swap?currencySymbolA=&tokenNameA=&currencySymbolB=${ticker}&tokenNameB=${unit})`);
        } else if (dexName.includes('sundae')) {
          links.push(`🔄 [Trade on SundaeSwap](https://exchange.sundaeswap.finance/#/swap?swap_from=ada&swap_to=${unit})`);
        } else if (dexName.includes('muesli')) {
          links.push(`🔄 [Trade on MuesliSwap](https://ada.muesliswap.com/swap?base=.&quote=${unit})`);
        }
      }
    }

    // Cardano blockchain explorer links
    if (unit && unit.length > 56) {
      const policyId = unit.substring(0, 56);
      links.push(`🔍 [View on CardanoScan](https://cardanoscan.io/token/${policyId})`);
      links.push(`📊 [View on Pool.pm](https://pool.pm/${policyId})`);
    }

    return links.join('\n');
  }

  /**
   * Extract Twitter username from various URL formats
   */
  private extractTwitterUsername(twitterUrl: string): string | null {
    if (!twitterUrl) return null;

    const patterns = [
      /twitter\.com\/([^\/\?]+)/i,
      /x\.com\/([^\/\?]+)/i,
      /@([a-zA-Z0-9_]+)/
    ];

    for (const pattern of patterns) {
      const match = twitterUrl.match(pattern);
      if (match && match[1]) {
        return match[1].replace('@', '');
      }
    }

    return null;
  }

  /**
   * Send token analysis to Discord channel
   */
  async sendTokenAnalysis(ticker: string, unit: string, decision: LlmDecision, additionalData: any = {}) {
    // If client is not ready, wait for up to 10 seconds for it to become ready
    if (!this.ready) {
      console.log('[DISCORD] Client not ready, waiting up to 10 seconds...');
      let waitTime = 0;
      while (!this.ready && waitTime < 10000) {
        await new Promise(resolve => setTimeout(resolve, 500));
        waitTime += 500;
      }

      if (!this.ready) {
        console.log('[DISCORD] Client still not ready after waiting, skipping message');
        return;
      }

      console.log('[DISCORD] Client is now ready after waiting');
    }

    try {
      console.log(`[DISCORD] Attempting to fetch channel with ID: ${this.channelId}`);
      const channel = await this.client.channels.fetch(this.channelId) as TextChannel;
      if (!channel) {
        console.error('[DISCORD] Channel not found');
        return;
      }
      console.log(`[DISCORD] Successfully fetched channel: ${channel.name}`);


      // Get historical performance data
      const hitRate = getTokenHitRate(ticker);
      const tokenTrades = getTradesByToken(ticker);
      const tradeCount = tokenTrades.length;

      // Format date
      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      });

      // Determine color based on recommendation
      const embedColor =
        decision.recommendation === 'buy' ? Colors.Green :
        decision.recommendation === 'sell' ? Colors.Red :
        Colors.Yellow;

      // Create rich embed
      const embed = new EmbedBuilder()
        .setColor(embedColor)
        .setTitle(`${ticker} Analysis - ${decision.recommendation?.toUpperCase()}`)
        .setDescription(this.getConfidenceExplanation(decision))
        .addFields(
          { name: 'Recommendation', value: decision.recommendation?.toUpperCase() || 'HOLD', inline: true },
          { name: 'Confidence', value: `${decision.confidence}/10 ${this.getConfidenceEmoji(decision.confidence)}`, inline: true },
          { name: 'Time Frame', value: decision.timeframe || 'Short-term', inline: true }
        )
        .addFields(
          { name: 'Price Analysis', value: this.getPriceAnalysis(decision), inline: false },
          { name: 'Volume Analysis', value: this.getVolumeAnalysis(decision), inline: false },
          { name: 'Technical Indicators', value: this.getTechnicalIndicators(decision, additionalData), inline: false },
          { name: 'Risk Assessment', value: this.getRiskAssessment(decision), inline: false }
        );

      // Add Twitter sentiment and news if available (for ALL recommendations: BUY, HOLD, SELL)
      if (decision.fundamentalFactors) {
        if (decision.fundamentalFactors.news && decision.fundamentalFactors.news.length > 50) {
          // Truncate news if too long for Discord
          const newsText = decision.fundamentalFactors.news.length > 1000
            ? decision.fundamentalFactors.news.substring(0, 1000) + '...'
            : decision.fundamentalFactors.news;
          embed.addFields({ name: 'News & Developments', value: newsText, inline: false });
        }

        if (decision.fundamentalFactors.sentiment && decision.fundamentalFactors.sentiment.length > 20) {
          // Add Twitter sentiment section
          const sentimentText = decision.fundamentalFactors.sentiment.length > 1000
            ? decision.fundamentalFactors.sentiment.substring(0, 1000) + '...'
            : decision.fundamentalFactors.sentiment;
          embed.addFields({ name: '🐦 Twitter Sentiment', value: sentimentText, inline: false });
        }
      }

      // Add Enhanced Multi-Timeframe Analysis if available (for ALL recommendations)
      if (additionalData?.multiTimeframe && additionalData.multiTimeframe.length > 0) {
        const mtfData = additionalData.multiTimeframe;
        const confluence = mtfData[0]?.confluence || 0;

        // Create detailed timeframe breakdown
        const timeframeDetails = mtfData.map((tf: any) => {
          const trend = tf.trend || 'neutral';
          const strength = tf.strength ? `(${tf.strength.toFixed(1)}%)` : '';
          const rsi = tf.technicalIndicators?.rsi ? `RSI: ${tf.technicalIndicators.rsi.toFixed(1)}` : '';
          const volume = tf.technicalIndicators?.volumeTrend ? `Vol: ${tf.technicalIndicators.volumeTrend}` : '';

          return `**${tf.timeframe}:** ${trend.toUpperCase()} ${strength}\n${rsi} | ${volume}`;
        }).join('\n\n');

        // Add market structure summary
        const marketStructure = mtfData[0]?.marketStructure;
        const structureText = marketStructure ?
          `\n**Market Structure:** ${marketStructure.trendDirection} (${marketStructure.trendStrength.toFixed(1)}% strength)\n**Support:** ${marketStructure.supportLevel.toFixed(6)} | **Resistance:** ${marketStructure.resistanceLevel.toFixed(6)}\n**Breakout Potential:** ${marketStructure.breakoutPotential.toUpperCase()}` : '';

        const mtfText = `**Confluence Score:** ${(confluence * 100).toFixed(1)}%\n\n${timeframeDetails}${structureText}`;
        embed.addFields({ name: '📊 Multi-Timeframe Analysis', value: mtfText, inline: false });
      }

      // Add Relative Strength vs ADA if available (for ALL recommendations)
      if (additionalData?.relativeStrength) {
        const rs = additionalData.relativeStrength;
        const rsText = `**Token Performance:** ${rs.tokenPerformance24h.toFixed(2)}%\n**ADA Performance:** ${rs.adaPerformance24h.toFixed(2)}%\n**Relative Strength:** ${rs.relativeStrength.toFixed(2)}% (${rs.strengthCategory})\n**Status:** ${rs.outperforming ? '📈 Outperforming' : '📉 Underperforming'} ADA`;
        embed.addFields({ name: '⚖️ Relative Strength vs ADA', value: rsText, inline: false });
      }

      // Add target price if available
      if (decision.targetPrice) {
        const targetPrice = typeof decision.targetPrice === 'string'
          ? decision.targetPrice
          : `Short: ${decision.targetPrice.shortTerm}, Long: ${decision.targetPrice.longTerm}`;

        embed.addFields({ name: 'Target Price', value: targetPrice, inline: true });
      }

      // Add stop loss if available
      if (decision.stopLoss) {
        embed.addFields({ name: 'Stop Loss', value: decision.stopLoss.toString(), inline: true });
      }

      // Add historical performance data if available
      if (tradeCount > 0) {
        embed.addFields({
          name: 'Historical Performance',
          value: `Success Rate: ${hitRate.toFixed(1)}%\nTotal Trades: ${tradeCount}`,
          inline: false
        });
      }

      // Add clickable links section
      const tokenLinks = this.generateTokenLinks(ticker, unit, additionalData);
      if (tokenLinks) {
        embed.addFields({
          name: '🔗 Quick Actions & Links',
          value: tokenLinks,
          inline: false
        });
      }

      embed.setFooter({ text: 'MISTER trading agent • ' + currentDate });

      await channel.send({ embeds: [embed] });
      console.log(`[DISCORD] Sent enhanced analysis with links for ${ticker} to Discord`);
    } catch (error) {
      console.error('[DISCORD] Error sending message:', error);
    }
  }

  /**
   * Send an enhanced transaction notification with analysis context
   */
  async sendEnhancedTransactionNotification(
    ticker: string,
    unit: string,
    direction: 'buy' | 'sell',
    amount: number,
    decision: LlmDecision,
    txHash?: string,
    riskManagement?: any,
    additionalData?: any
  ) {
    // If client is not ready, wait for up to 10 seconds for it to become ready
    if (!this.ready) {
      console.log('[DISCORD] Client not ready, waiting up to 10 seconds...');
      let waitTime = 0;
      while (!this.ready && waitTime < 10000) {
        await new Promise(resolve => setTimeout(resolve, 500));
        waitTime += 500;
      }

      if (!this.ready) {
        console.log('[DISCORD] Client still not ready after waiting, skipping message');
        return;
      }

      console.log('[DISCORD] Client is now ready after waiting');
    }

    try {
      console.log(`[DISCORD] Attempting to fetch channel with ID: ${this.channelId}`);
      const channel = await this.client.channels.fetch(this.channelId) as TextChannel;
      if (!channel) {
        console.error('[DISCORD] Channel not found');
        return;
      }
      console.log(`[DISCORD] Successfully fetched channel: ${channel.name}`);

      // Get historical performance data
      const hitRate = getTokenHitRate(ticker);
      const tokenTrades = getTradesByToken(ticker);
      const tradeCount = tokenTrades.length;

      // Format date
      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      });

      // Determine color based on direction
      const embedColor = direction === 'buy' ? Colors.Green : Colors.Red;

      // Get risk category emoji and text if available
      let riskCategoryEmoji = '⚠️';
      let riskCategoryText = 'Medium Risk';

      if (riskManagement && riskManagement.riskCategory) {
        switch (riskManagement.riskCategory) {
          case 'ultra_high':
            riskCategoryEmoji = '🔥';
            riskCategoryText = 'Ultra High Risk';
            break;
          case 'high':
            riskCategoryEmoji = '⚠️';
            riskCategoryText = 'High Risk';
            break;
          case 'medium':
            riskCategoryEmoji = '🟡';
            riskCategoryText = 'Medium Risk';
            break;
          case 'low':
            riskCategoryEmoji = '🟢';
            riskCategoryText = 'Low Risk';
            break;
        }
      }

      // Create rich embed
      const embed = new EmbedBuilder()
        .setColor(embedColor)
        .setTitle(`${direction.toUpperCase()} ${Math.round(amount)} ADA of ${ticker} ${riskCategoryEmoji}`)
        .setDescription(this.getConfidenceExplanation(decision))
        .addFields(
          { name: 'Transaction', value: `${direction.toUpperCase()} ${Math.round(amount)} ADA of ${ticker}`, inline: false },
          { name: 'Confidence', value: `${decision.confidence}/10 ${this.getConfidenceEmoji(decision.confidence)}`, inline: true },
          { name: 'Time Frame', value: decision.timeframe || 'Short-term', inline: true },
          { name: 'Risk Level', value: riskCategoryText, inline: true }
        )
        .addFields(
          { name: 'Price Analysis', value: this.getPriceAnalysis(decision), inline: false },
          { name: 'Volume Analysis', value: this.getVolumeAnalysis(decision), inline: false },
          { name: 'Technical Indicators', value: this.getTechnicalIndicators(decision), inline: false },
          { name: 'Risk Assessment', value: this.getRiskAssessment(decision), inline: false }
        );

      // Add Enhanced Multi-Timeframe Analysis if available
      if (additionalData?.multiTimeframe && additionalData.multiTimeframe.length > 0) {
        const mtfData = additionalData.multiTimeframe;
        const confluence = mtfData[0]?.confluence || 0;

        // Create detailed timeframe breakdown
        const timeframeDetails = mtfData.map((tf: any) => {
          const trend = tf.trend || 'neutral';
          const strength = tf.strength ? `(${tf.strength.toFixed(1)}%)` : '';
          const rsi = tf.technicalIndicators?.rsi ? `RSI: ${tf.technicalIndicators.rsi.toFixed(1)}` : '';
          const volume = tf.technicalIndicators?.volumeTrend ? `Vol: ${tf.technicalIndicators.volumeTrend}` : '';

          return `**${tf.timeframe}:** ${trend.toUpperCase()} ${strength}\n${rsi} | ${volume}`;
        }).join('\n\n');

        // Add market structure summary
        const marketStructure = mtfData[0]?.marketStructure;
        const structureText = marketStructure ?
          `\n**Market Structure:** ${marketStructure.trendDirection} (${marketStructure.trendStrength.toFixed(1)}% strength)\n**Support:** ${marketStructure.supportLevel.toFixed(6)} | **Resistance:** ${marketStructure.resistanceLevel.toFixed(6)}\n**Breakout Potential:** ${marketStructure.breakoutPotential.toUpperCase()}` : '';

        const mtfText = `**Confluence Score:** ${(confluence * 100).toFixed(1)}%\n\n${timeframeDetails}${structureText}`;
        embed.addFields({ name: '📊 Multi-Timeframe Analysis', value: mtfText, inline: false });
      }

      // Add Relative Strength vs ADA if available
      if (additionalData?.relativeStrength) {
        const rs = additionalData.relativeStrength;
        const rsText = `**Token Performance:** ${rs.tokenPerformance24h.toFixed(2)}%\n**ADA Performance:** ${rs.adaPerformance24h.toFixed(2)}%\n**Relative Strength:** ${rs.relativeStrength.toFixed(2)}% (${rs.strengthCategory})\n**Status:** ${rs.outperforming ? '📈 Outperforming' : '📉 Underperforming'} ADA`;
        embed.addFields({ name: '⚖️ Relative Strength vs ADA', value: rsText, inline: false });
      }

      // Add Twitter sentiment and news if available
      if (decision.fundamentalFactors) {
        if (decision.fundamentalFactors.news && decision.fundamentalFactors.news.length > 50) {
          // Truncate news if too long for Discord
          const newsText = decision.fundamentalFactors.news.length > 1000
            ? decision.fundamentalFactors.news.substring(0, 1000) + '...'
            : decision.fundamentalFactors.news;
          embed.addFields({ name: 'News & Developments', value: newsText, inline: false });
        }

        if (decision.fundamentalFactors.sentiment && decision.fundamentalFactors.sentiment.length > 20) {
          // Add Twitter sentiment section
          const sentimentText = decision.fundamentalFactors.sentiment.length > 1000
            ? decision.fundamentalFactors.sentiment.substring(0, 1000) + '...'
            : decision.fundamentalFactors.sentiment;
          embed.addFields({ name: '🐦 Twitter Sentiment', value: sentimentText, inline: false });
        }
      }

      // Add target price if available
      if (decision.targetPrice) {
        const targetPrice = typeof decision.targetPrice === 'string'
          ? decision.targetPrice
          : `Short: ${decision.targetPrice.shortTerm}, Long: ${decision.targetPrice.longTerm}`;

        embed.addFields({ name: 'Target Price', value: targetPrice, inline: true });
      } else if (riskManagement && riskManagement.targetPrice) {
        embed.addFields({ name: 'Target Price', value: riskManagement.targetPrice.toString(), inline: true });
      }

      // Add stop loss if available
      if (decision.stopLoss) {
        embed.addFields({ name: 'Stop Loss', value: decision.stopLoss.toString(), inline: true });
      } else if (riskManagement && riskManagement.stopLoss) {
        embed.addFields({ name: 'Stop Loss', value: riskManagement.stopLoss.toString(), inline: true });
      }

      // Add risk/reward ratio if available
      if (riskManagement && riskManagement.riskRewardRatio) {
        embed.addFields({ name: 'Risk/Reward Ratio', value: riskManagement.riskRewardRatio.toFixed(2), inline: true });
      }

      // Add max hold time if available
      if (riskManagement && riskManagement.timeBasedExit && riskManagement.timeBasedExit.maxHoldTime) {
        const maxHoldTime = riskManagement.timeBasedExit.maxHoldTime;
        const exitTimeStr = riskManagement.timeBasedExit.exitTime ?
          new Date(riskManagement.timeBasedExit.exitTime).toLocaleString() :
          'Not specified';

        embed.addFields({
          name: 'Recommended Max Hold',
          value: `${maxHoldTime} hours\nExit by: ${exitTimeStr}`,
          inline: true
        });
      }

      // Add historical performance data if available
      if (tradeCount > 0) {
        embed.addFields({
          name: 'Historical Performance',
          value: `Success Rate: ${hitRate.toFixed(1)}%\nTotal Trades: ${tradeCount}`,
          inline: false
        });
      }

      // Add transaction hash if available
      if (txHash) {
        embed.addFields({
          name: '🔗 Transaction Details',
          value: `**Hash:** \`${txHash}\`\n🔍 [View on CardanoScan](https://cardanoscan.io/transaction/${txHash})`,
          inline: false
        });
      }

      // Add clickable links section for transactions too
      const tokenLinks = this.generateTokenLinks(ticker, unit, additionalData);
      if (tokenLinks) {
        embed.addFields({
          name: '🔗 Quick Actions & Links',
          value: tokenLinks,
          inline: false
        });
      }

      embed.setFooter({ text: 'MISTER trading agent • ' + currentDate });

      await channel.send({ embeds: [embed] });
      console.log(`[DISCORD] Sent enhanced transaction notification with links for ${ticker} to Discord`);
    } catch (error) {
      console.error('[DISCORD] Error sending message:', error);
    }
  }

  /**
   * Send transaction notification to Discord
   */
  async sendTransactionNotification(ticker: string, direction: 'buy' | 'sell', amount: number, txHash?: string) {
    // If client is not ready, wait for up to 10 seconds for it to become ready
    if (!this.ready) {
      console.log('[DISCORD] Client not ready, waiting up to 10 seconds...');
      let waitTime = 0;
      while (!this.ready && waitTime < 10000) {
        await new Promise(resolve => setTimeout(resolve, 500));
        waitTime += 500;
      }

      if (!this.ready) {
        console.log('[DISCORD] Client still not ready after waiting, skipping message');
        return;
      }

      console.log('[DISCORD] Client is now ready after waiting');
    }

    try {
      console.log(`[DISCORD] Attempting to fetch channel with ID: ${this.channelId}`);
      const channel = await this.client.channels.fetch(this.channelId) as TextChannel;
      if (!channel) {
        console.error('[DISCORD] Channel not found');
        return;
      }
      console.log(`[DISCORD] Successfully fetched channel: ${channel.name}`);

      // Get historical performance data
      const hitRate = getTokenHitRate(ticker);
      const tokenTrades = getTradesByToken(ticker);
      const tradeCount = tokenTrades.length;

      // Format date
      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      });

      // Determine color based on direction
      const embedColor = direction === 'buy' ? Colors.Green : Colors.Red;

      // Create rich embed
      const embed = new EmbedBuilder()
        .setColor(embedColor)
        .setTitle(`${direction.toUpperCase()} ${Math.round(amount)} ADA of ${ticker}`)
        .setDescription(`Transaction executed at ${currentDate}`)
        .addFields(
          { name: 'Transaction', value: `${direction.toUpperCase()} ${Math.round(amount)} ADA of ${ticker}`, inline: false }
        );

      // Add historical performance data if available
      if (tradeCount > 0) {
        embed.addFields({
          name: 'Historical Performance',
          value: `Success Rate: ${hitRate.toFixed(1)}%\nTotal Trades: ${tradeCount}`,
          inline: false
        });
      }

      // Add transaction hash if available
      if (txHash) {
        embed.addFields({
          name: '🔗 Transaction Details',
          value: `**Hash:** \`${txHash}\`\n🔍 [View on CardanoScan](https://cardanoscan.io/transaction/${txHash})`,
          inline: false
        });
      }

      embed.setFooter({ text: 'MISTER trading agent • ' + currentDate });

      await channel.send({ embeds: [embed] });
      console.log(`[DISCORD] Sent transaction notification for ${ticker} to Discord`);
    } catch (error) {
      console.error('[DISCORD] Error sending message:', error);
    }
  }

  /**
   * Send a notification about the daily trading schedule
   */
  async sendDailyScheduleNotification(nextRunTime: string) {
    // If client is not ready, wait for up to 10 seconds for it to become ready
    if (!this.ready) {
      console.log('[DISCORD] Client not ready, waiting up to 10 seconds...');
      let waitTime = 0;
      while (!this.ready && waitTime < 10000) {
        await new Promise(resolve => setTimeout(resolve, 500));
        waitTime += 500;
      }

      if (!this.ready) {
        console.log('[DISCORD] Client still not ready after waiting, skipping message');
        return;
      }

      console.log('[DISCORD] Client is now ready after waiting');
    }

    try {
      console.log(`[DISCORD] Attempting to fetch channel with ID: ${this.channelId}`);
      const channel = await this.client.channels.fetch(this.channelId) as TextChannel;
      if (!channel) {
        console.error('[DISCORD] Channel not found');
        return;
      }
      console.log(`[DISCORD] Successfully fetched channel: ${channel.name}`);

      // Format date
      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      });

      // Create rich embed
      const embed = new EmbedBuilder()
        .setColor(Colors.Blue)
        .setTitle('Daily Trading Schedule Update')
        .setDescription('The trading bot has been configured to run once per day')
        .addFields(
          { name: 'Current Operation', value: 'The bot is now analyzing market data and making trading decisions', inline: false },
          { name: 'Next Scheduled Run', value: nextRunTime, inline: false }
        )
        .setFooter({ text: 'MISTER trading agent • ' + currentDate });

      await channel.send({ embeds: [embed] });
      console.log('[DISCORD] Sent daily schedule notification to Discord');
    } catch (error) {
      console.error('[DISCORD] Error sending message:', error);
    }
  }

  /**
   * Get emoji based on confidence level
   */
  private getConfidenceEmoji(confidence: number): string {
    if (confidence >= 9) return '🔥'; // Fire - very high confidence
    if (confidence >= 7) return '✅'; // Checkmark - high confidence
    if (confidence >= 5) return '⚠️'; // Warning - medium confidence
    if (confidence >= 3) return '❓'; // Question - low confidence
    return '❌'; // X - very low confidence
  }
}

// Create a singleton instance
const discordBot = new DiscordBot();
export default discordBot;
