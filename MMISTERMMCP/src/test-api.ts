/**
 * Simple test API server to verify managed wallet functionality
 */

import express from 'express';
import cors from 'cors';
import { AnalysisCacheService } from './analysis-cache-service.js';
import { botAnalysisBridge } from './bot-analysis-bridge.js';

const app = express();
const PORT = 4114;

// Initialize analysis cache service
const analysisCache = new AnalysisCacheService();

// Middleware
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'MISTER Managed Wallet API'
  });
});

// Test managed wallet endpoints
app.post('/api/wallets/create', (req, res) => {
  const { userId, displayName } = req.body;
  
  res.json({
    success: true,
    data: {
      wallet: {
        userId,
        walletId: `wallet_${Date.now()}`,
        displayName: displayName || 'Test Wallet',
        address: `addr1_test_${Date.now()}`,
        isActive: true,
        createdAt: new Date()
      },
      mnemonic: 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about'
    },
    message: 'Test wallet created successfully'
  });
});

app.get('/api/wallets/:userId', (req, res) => {
  const { userId } = req.params;
  
  res.json({
    success: true,
    data: [
      {
        userId,
        walletId: 'wallet_test_123',
        displayName: 'Test Trading Wallet',
        address: 'addr1_test_123',
        isActive: true,
        createdAt: new Date(),
        balance: {
          ada: 100,
          tokens: [],
          lastUpdated: new Date()
        }
      }
    ],
    count: 1
  });
});

app.post('/api/trading/start', (req, res) => {
  const { userId, walletId } = req.body;
  
  res.json({
    success: true,
    data: {
      sessionId: `session_${walletId}_${Date.now()}`,
      userId,
      walletId,
      isActive: true,
      startedAt: new Date(),
      tradesExecuted: 0,
      settings: {
        autoTradingEnabled: true,
        maxDailyTrades: 10,
        maxPositionSize: 100,
        riskLevel: 'moderate'
      }
    },
    message: 'Trading session started successfully'
  });
});

app.post('/api/trading/manual-trade', (req, res) => {
  const { walletId, ticker, direction, amount } = req.body;

  res.json({
    success: true,
    data: {
      tradeId: `trade_${Date.now()}`,
      ticker,
      direction,
      amount: Math.round(amount),
      price: Math.random() * 10,
      txHash: `tx_test_${Date.now()}`,
      timestamp: new Date()
    },
    message: 'Test trade executed successfully'
  });
});

// Analysis endpoints - REAL DATA FROM BOT
app.get('/api/analysis/current', (req, res) => {
  try {
    // Get the latest analysis from the bot bridge (real-time)
    const latestAnalysis = botAnalysisBridge.getLatestAnalysis();

    if (!latestAnalysis) {
      // Fallback to cache if no real-time data
      const cachedAnalysis = analysisCache.getCurrentAnalysis();

      if (!cachedAnalysis) {
        return res.json({
          success: false,
          error: 'No analysis data available. Bot may still be running initial analysis.',
          message: 'Please wait for the trading bot to complete its first analysis run.'
        });
      }

      return res.json({
        success: true,
        data: cachedAnalysis,
        metadata: {
          lastUpdated: cachedAnalysis.timestamp,
          nextUpdate: new Date(new Date(cachedAnalysis.timestamp).getTime() + 60 * 60 * 1000).toISOString(),
          fromCache: true,
          source: 'cache'
        }
      });
    }

    res.json({
      success: true,
      data: latestAnalysis,
      metadata: {
        lastUpdated: latestAnalysis.timestamp,
        nextUpdate: new Date(new Date(latestAnalysis.timestamp).getTime() + 60 * 60 * 1000).toISOString(),
        fromCache: false,
        source: 'live_bot'
      }
    });
  } catch (error) {
    console.error('Error fetching current analysis:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analysis data'
    });
  }
});

app.get('/api/analysis/history', (req, res) => {
  try {
    // Get current run analyses (real-time) + cached history
    const currentAnalyses = botAnalysisBridge.getCurrentAnalyses();
    const cachedHistory = analysisCache.getAnalysisHistory();

    // Combine and deduplicate by ticker and timestamp
    const allAnalyses = [...currentAnalyses, ...cachedHistory];
    const uniqueAnalyses = allAnalyses.filter((analysis, index, self) =>
      index === self.findIndex(a => a.ticker === analysis.ticker && a.timestamp === analysis.timestamp)
    );

    // Sort by timestamp (newest first)
    uniqueAnalyses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    res.json({
      success: true,
      data: uniqueAnalyses.slice(0, 50), // Limit to 50 most recent
      metadata: {
        totalRecords: uniqueAnalyses.length,
        currentRunAnalyses: currentAnalyses.length,
        cachedAnalyses: cachedHistory.length,
        fromCache: false
      }
    });
  } catch (error) {
    console.error('Error fetching analysis history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analysis history'
    });
  }
});

// Bot status endpoint - REAL-TIME BOT STATUS
app.get('/api/bot/status', (req, res) => {
  try {
    const botStatus = botAnalysisBridge.getBotStatus();

    res.json({
      success: true,
      data: botStatus,
      metadata: {
        timestamp: new Date().toISOString(),
        source: 'live_bot'
      }
    });
  } catch (error) {
    console.error('Error fetching bot status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch bot status'
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 MISTER Managed Wallet API Test Server running on port ${PORT}`);
  console.log(`📋 Test endpoints:`);
  console.log(`   Health: GET http://localhost:${PORT}/health`);
  console.log(`   Create Wallet: POST http://localhost:${PORT}/api/wallets/create`);
  console.log(`   Get Wallets: GET http://localhost:${PORT}/api/wallets/:userId`);
  console.log(`   Start Trading: POST http://localhost:${PORT}/api/trading/start`);
  console.log(`   Manual Trade: POST http://localhost:${PORT}/api/trading/manual-trade`);
  console.log(`📊 Analysis endpoints:`);
  console.log(`   Current Analysis: GET http://localhost:${PORT}/api/analysis/current`);
  console.log(`   Analysis History: GET http://localhost:${PORT}/api/analysis/history`);
});

export default app;
