/**
 * Bridge service to connect the portfolio-swaps bot with the analysis cache
 * This allows the frontend to see real-time bot analysis instead of mock data
 */

import { AnalysisCacheService, CachedAnalysis } from './analysis-cache-service.js';
import { LlmDecision } from './types.js';

export interface BotAnalysisData {
  ticker: string;
  unit: string;
  decision: LlmDecision;
  tokenData: any;
  timestamp: string;
}

export interface BotStatus {
  isRunning: boolean;
  currentToken?: string;
  tokensAnalyzed: number;
  totalTokens: number;
  startTime?: string;
  nextRunTime?: string;
  lastError?: string;
  currentAnalyses: CachedAnalysis[];
}

export class BotAnalysisBridge {
  private analysisCache: AnalysisCacheService;
  private botStatus: BotStatus;
  private currentAnalyses: Map<string, CachedAnalysis> = new Map();

  constructor() {
    this.analysisCache = new AnalysisCacheService();
    this.botStatus = {
      isRunning: false,
      tokensAnalyzed: 0,
      totalTokens: 0,
      currentAnalyses: []
    };
  }

  /**
   * Called when the bot starts a new analysis run
   */
  public startBotRun(totalTokens: number): void {
    console.log(`[BRIDGE] Bot analysis run started - ${totalTokens} tokens to analyze`);
    this.botStatus = {
      isRunning: true,
      tokensAnalyzed: 0,
      totalTokens,
      startTime: new Date().toISOString(),
      currentAnalyses: []
    };
    this.currentAnalyses.clear();
  }

  /**
   * Called when the bot completes analysis of a single token
   */
  public async addTokenAnalysis(data: BotAnalysisData): Promise<void> {
    try {
      console.log(`[BRIDGE] Adding analysis for ${data.ticker}`);
      
      // Convert bot decision to cached analysis format
      const cachedAnalysis: CachedAnalysis = this.convertToCachedAnalysis(data);
      
      // Store in current run
      this.currentAnalyses.set(data.ticker, cachedAnalysis);
      this.botStatus.tokensAnalyzed++;
      this.botStatus.currentToken = data.ticker;
      this.botStatus.currentAnalyses = Array.from(this.currentAnalyses.values());

      // Update the analysis cache with the latest analysis
      await this.analysisCache.updateAnalysis(cachedAnalysis);
      
      console.log(`[BRIDGE] ✅ Analysis cached for ${data.ticker} (${this.botStatus.tokensAnalyzed}/${this.botStatus.totalTokens})`);
    } catch (error) {
      console.error(`[BRIDGE] ❌ Error caching analysis for ${data.ticker}:`, error);
      this.botStatus.lastError = error instanceof Error ? error.message : String(error);
    }
  }

  /**
   * Called when the bot completes the entire analysis run
   */
  public completeBotRun(nextRunTime?: string): void {
    console.log(`[BRIDGE] Bot analysis run completed - ${this.botStatus.tokensAnalyzed} tokens analyzed`);
    this.botStatus.isRunning = false;
    this.botStatus.nextRunTime = nextRunTime;
    this.botStatus.currentToken = undefined;
  }

  /**
   * Get current bot status for the frontend
   */
  public getBotStatus(): BotStatus {
    return { ...this.botStatus };
  }

  /**
   * Get all current analyses from this run
   */
  public getCurrentAnalyses(): CachedAnalysis[] {
    return Array.from(this.currentAnalyses.values());
  }

  /**
   * Get the most recent analysis (for the main display)
   */
  public getLatestAnalysis(): CachedAnalysis | null {
    const analyses = Array.from(this.currentAnalyses.values());
    if (analyses.length === 0) return null;
    
    // Return the most recent analysis
    return analyses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];
  }

  /**
   * Convert bot decision format to cached analysis format
   */
  private convertToCachedAnalysis(data: BotAnalysisData): CachedAnalysis {
    const { ticker, decision, tokenData, timestamp } = data;

    // Extract price data
    const currentPrice = tokenData.aggregated_price?.price || 0;
    const priceChange24h = tokenData.price_change?.['24h'] || 0;

    // Extract technical analysis
    const rsi15m = tokenData.multiTimeframe?.['15m']?.rsi || 50;
    const rsi1h = tokenData.multiTimeframe?.['1h']?.rsi || 50;
    const rsi4h = tokenData.multiTimeframe?.['4h']?.rsi || 50;

    // Determine RSI signal
    const avgRsi = (rsi15m + rsi1h + rsi4h) / 3;
    let rsiSignal: 'oversold' | 'neutral' | 'overbought' = 'neutral';
    if (avgRsi < 30) rsiSignal = 'oversold';
    else if (avgRsi > 70) rsiSignal = 'overbought';

    // Extract sentiment data
    const twitterSentiment = tokenData.twitterSentiment || {};
    const sentimentScore = twitterSentiment.confidence || 0.5;
    const tweetVolume = twitterSentiment.insights || 0;
    const trending = sentimentScore > 0.7;

    // Map sentiment to overall sentiment
    let overallSentiment: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    if (twitterSentiment.sentiment === 'very_bullish' || twitterSentiment.sentiment === 'bullish') {
      overallSentiment = 'bullish';
    } else if (twitterSentiment.sentiment === 'bearish' || twitterSentiment.sentiment === 'very_bearish') {
      overallSentiment = 'bearish';
    }

    // Extract decision data
    const action = decision.recommendation?.toUpperCase() as 'BUY' | 'SELL' | 'HOLD' || 'HOLD';
    const confidence = decision.confidence || 5;
    const reasoning = Array.isArray(decision.reasoning) ? decision.reasoning : [decision.summary || 'Analysis completed'];
    const targetPrice = parseFloat(decision.targetPrice || '0');
    const stopLoss = parseFloat(decision.stopLoss || '0');
    const positionSize = decision.size || 0;

    // Extract risk data
    let riskLevel: 'low' | 'medium' | 'high' = 'medium';
    if (confidence >= 8) riskLevel = 'low';
    else if (confidence <= 5) riskLevel = 'high';

    const liquidityScore = tokenData.trading_stats?.liquidity_score || 50;

    return {
      ticker,
      timestamp,
      currentPrice,
      priceChange24h,
      technicalAnalysis: {
        rsi: {
          '15m': rsi15m,
          '1h': rsi1h,
          '4h': rsi4h,
          signal: rsiSignal
        },
        macd: {
          signal: decision.direction === 'buy' ? 'bullish' : decision.direction === 'sell' ? 'bearish' : 'neutral',
          histogram: 0.001 // Placeholder
        },
        bollinger: {
          position: 'middle', // Placeholder
          squeeze: false
        },
        support: tokenData.support || currentPrice * 0.9,
        resistance: tokenData.resistance || currentPrice * 1.1
      },
      sentiment: {
        twitter: {
          score: sentimentScore,
          volume: tweetVolume,
          trending
        },
        overall: overallSentiment
      },
      decision: {
        action,
        confidence,
        reasoning,
        targetPrice: targetPrice > 0 ? targetPrice : undefined,
        stopLoss: stopLoss > 0 ? stopLoss : undefined,
        positionSize
      },
      risk: {
        level: riskLevel,
        factors: decision.riskFactors || ['Market volatility present'],
        liquidityScore
      }
    };
  }
}

// Export singleton instance
export const botAnalysisBridge = new BotAnalysisBridge();
