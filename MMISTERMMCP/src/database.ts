import fs from 'fs';
import path from 'path';

// Trade interface
export interface Trade {
  id: string;
  timestamp: string;
  token: string;
  ticker: string;
  unit: string;
  direction: 'buy' | 'sell';
  amount: number;
  price: number;
  total: number;
  status: 'completed' | 'pending' | 'failed';
  txHash?: string;
  confidence: number;
  llmDecision: any;
  errorMessage?: string;
  riskManagement?: {
    riskCategory: string;
    stopLoss: number;
    trailingStop?: {
      active: boolean;
      activationPrice: number;
      trailPercentage: number;
      currentStopPrice: number;
    };
    timeBasedExit?: {
      maxHoldTime: number; // in hours
      exitTime: string; // ISO date string
    };
    targetPrice?: number;
    riskRewardRatio?: number;
  };
  performanceTracking?: {
    entryPrice: number;
    exitPrice?: number;
    exitTimestamp?: string;
    profitLoss?: number;
    profitLossPercentage?: number;
    holdDuration?: number; // in hours
    success?: boolean;
    exitReason?: 'stop_loss' | 'trailing_stop' | 'time_based' | 'target_reached' | 'manual' | 'unknown';
  }
}

// Performance metrics interface
export interface PerformanceMetrics {
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  pendingTrades: number;
  winRate: number;
  averageProfitLoss: number;
  totalProfitLoss: number;
  bestTrade: {
    token: string;
    profitLossPercentage: number;
    timestamp: string;
  };
  worstTrade: {
    token: string;
    profitLossPercentage: number;
    timestamp: string;
  };
  tokenPerformance: Record<string, {
    trades: number;
    successfulTrades: number;
    winRate: number;
    averageProfitLoss: number;
  }>;
  timeframePerformance: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

// Path to trade database file
const TRADES_DB_PATH = path.join(process.cwd(), 'data', 'trades.json');
const PERFORMANCE_DB_PATH = path.join(process.cwd(), 'data', 'performance.json');

// Ensure the data directory exists
const ensureDataDirExists = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Initialize database files if they don't exist
const initializeDatabase = () => {
  ensureDataDirExists();

  if (!fs.existsSync(TRADES_DB_PATH)) {
    fs.writeFileSync(TRADES_DB_PATH, JSON.stringify([], null, 2));
  }

  if (!fs.existsSync(PERFORMANCE_DB_PATH)) {
    const initialPerformance: PerformanceMetrics = {
      totalTrades: 0,
      successfulTrades: 0,
      failedTrades: 0,
      pendingTrades: 0,
      winRate: 0,
      averageProfitLoss: 0,
      totalProfitLoss: 0,
      bestTrade: {
        token: '',
        profitLossPercentage: 0,
        timestamp: '',
      },
      worstTrade: {
        token: '',
        profitLossPercentage: 0,
        timestamp: '',
      },
      tokenPerformance: {},
      timeframePerformance: {
        daily: 0,
        weekly: 0,
        monthly: 0,
      },
    };
    fs.writeFileSync(PERFORMANCE_DB_PATH, JSON.stringify(initialPerformance, null, 2));
  }
};

// Save a new trade to the database
export const saveTrade = (trade: Trade): Trade => {
  try {
    initializeDatabase();

    // Read current trades
    const tradesData = fs.readFileSync(TRADES_DB_PATH, 'utf8');
    const trades: Trade[] = JSON.parse(tradesData);

    // Add the new trade
    trades.push(trade);

    // Write updated trades back to file
    fs.writeFileSync(TRADES_DB_PATH, JSON.stringify(trades, null, 2));

    // Update performance metrics
    updatePerformanceMetrics();

    return trade;
  } catch (error) {
    console.error('[DATABASE] Error saving trade:', error);
    throw error;
  }
};

// Get all trades
export const getAllTrades = (): Trade[] => {
  try {
    initializeDatabase();

    const tradesData = fs.readFileSync(TRADES_DB_PATH, 'utf8');
    return JSON.parse(tradesData);
  } catch (error) {
    console.error('[DATABASE] Error getting trades:', error);
    return [];
  }
};

// Get trades for a specific token
export const getTradesByToken = (token: string): Trade[] => {
  try {
    const trades = getAllTrades();
    return trades.filter(trade => trade.ticker === token || trade.token === token);
  } catch (error) {
    console.error(`[DATABASE] Error getting trades for token ${token}:`, error);
    return [];
  }
};

// Update an existing trade
export const updateTrade = (tradeId: string, updatedFields: Partial<Trade>): Trade | null => {
  try {
    const trades = getAllTrades();
    const tradeIndex = trades.findIndex(trade => trade.id === tradeId);

    if (tradeIndex === -1) {
      console.error(`[DATABASE] Trade with ID ${tradeId} not found`);
      return null;
    }

    // Update the trade
    trades[tradeIndex] = { ...trades[tradeIndex], ...updatedFields };

    // Write updated trades back to file
    fs.writeFileSync(TRADES_DB_PATH, JSON.stringify(trades, null, 2));

    // Update performance metrics
    updatePerformanceMetrics();

    return trades[tradeIndex];
  } catch (error) {
    console.error(`[DATABASE] Error updating trade ${tradeId}:`, error);
    return null;
  }
};

// Update exit information for a trade (when we sell a token we previously bought)
export const updateTradeExit = (token: string, exitPrice: number): Trade | null => {
  try {
    const trades = getAllTrades();

    // Find the most recent buy trade for this token that doesn't have an exit
    const buyTradeIndex = [...trades].reverse().findIndex(
      trade => (trade.ticker === token || trade.token === token) &&
              trade.direction === 'buy' &&
              !trade.performanceTracking?.exitPrice
    );

    if (buyTradeIndex === -1) {
      console.error(`[DATABASE] No open buy trades found for token ${token}`);
      return null;
    }

    // Calculate the actual index in the original array
    const actualIndex = trades.length - 1 - buyTradeIndex;
    const trade = trades[actualIndex];

    // Calculate performance metrics
    const entryPrice = trade.price;
    const profitLoss = (exitPrice - entryPrice) * trade.amount;
    const profitLossPercentage = ((exitPrice - entryPrice) / entryPrice) * 100;

    const entryDate = new Date(trade.timestamp);
    const exitDate = new Date();
    const holdDuration = (exitDate.getTime() - entryDate.getTime()) / (1000 * 60 * 60); // in hours

    // Update the trade
    const updatedTrade: Trade = {
      ...trade,
      performanceTracking: {
        entryPrice,
        exitPrice,
        exitTimestamp: new Date().toISOString(),
        profitLoss,
        profitLossPercentage,
        holdDuration,
        success: profitLoss > 0
      }
    };

    trades[actualIndex] = updatedTrade;

    // Write updated trades back to file
    fs.writeFileSync(TRADES_DB_PATH, JSON.stringify(trades, null, 2));

    // Update performance metrics
    updatePerformanceMetrics();

    return updatedTrade;
  } catch (error) {
    console.error(`[DATABASE] Error updating exit for token ${token}:`, error);
    return null;
  }
};

// Calculate and update performance metrics
export const updatePerformanceMetrics = (): PerformanceMetrics => {
  try {
    const trades = getAllTrades();

    const completedTrades = trades.filter(trade =>
      trade.status === 'completed' && trade.performanceTracking?.exitPrice
    );

    const metrics: PerformanceMetrics = {
      totalTrades: trades.length,
      successfulTrades: completedTrades.filter(trade => trade.performanceTracking?.success).length,
      failedTrades: completedTrades.filter(trade => !trade.performanceTracking?.success).length,
      pendingTrades: trades.filter(trade => trade.status === 'pending' || !trade.performanceTracking?.exitPrice).length,
      winRate: 0,
      averageProfitLoss: 0,
      totalProfitLoss: 0,
      bestTrade: {
        token: '',
        profitLossPercentage: 0,
        timestamp: '',
      },
      worstTrade: {
        token: '',
        profitLossPercentage: 0,
        timestamp: '',
      },
      tokenPerformance: {},
      timeframePerformance: {
        daily: 0,
        weekly: 0,
        monthly: 0,
      },
    };

    // Calculate win rate
    if (completedTrades.length > 0) {
      metrics.winRate = (metrics.successfulTrades / completedTrades.length) * 100;
    }

    // Calculate profit/loss
    let totalProfitLoss = 0;

    completedTrades.forEach(trade => {
      if (trade.performanceTracking?.profitLoss) {
        totalProfitLoss += trade.performanceTracking.profitLoss;
      }
    });

    metrics.totalProfitLoss = totalProfitLoss;
    metrics.averageProfitLoss = completedTrades.length > 0 ? totalProfitLoss / completedTrades.length : 0;

    // Find best and worst trades
    if (completedTrades.length > 0) {
      const sortedTrades = [...completedTrades].sort((a, b) =>
        (b.performanceTracking?.profitLossPercentage || 0) - (a.performanceTracking?.profitLossPercentage || 0)
      );

      const bestTrade = sortedTrades[0];
      const worstTrade = sortedTrades[sortedTrades.length - 1];

      metrics.bestTrade = {
        token: bestTrade.ticker || bestTrade.token,
        profitLossPercentage: bestTrade.performanceTracking?.profitLossPercentage || 0,
        timestamp: bestTrade.timestamp,
      };

      metrics.worstTrade = {
        token: worstTrade.ticker || worstTrade.token,
        profitLossPercentage: worstTrade.performanceTracking?.profitLossPercentage || 0,
        timestamp: worstTrade.timestamp,
      };
    }

    // Calculate token performance
    const tokenPerformance: Record<string, {
      trades: number;
      successfulTrades: number;
      winRate: number;
      averageProfitLoss: number;
    }> = {};

    completedTrades.forEach(trade => {
      const token = trade.ticker || trade.token;

      if (!tokenPerformance[token]) {
        tokenPerformance[token] = {
          trades: 0,
          successfulTrades: 0,
          winRate: 0,
          averageProfitLoss: 0,
        };
      }

      tokenPerformance[token].trades += 1;

      if (trade.performanceTracking?.success) {
        tokenPerformance[token].successfulTrades += 1;
      }

      // Accumulate profit/loss for averaging later
      if (trade.performanceTracking?.profitLoss) {
        tokenPerformance[token].averageProfitLoss += trade.performanceTracking.profitLoss;
      }
    });

    // Calculate win rate and average profit/loss for each token
    Object.keys(tokenPerformance).forEach(token => {
      const tokenStats = tokenPerformance[token];

      tokenStats.winRate = tokenStats.trades > 0 ? (tokenStats.successfulTrades / tokenStats.trades) * 100 : 0;
      tokenStats.averageProfitLoss = tokenStats.trades > 0 ? tokenStats.averageProfitLoss / tokenStats.trades : 0;
    });

    metrics.tokenPerformance = tokenPerformance;

    // Calculate timeframe performance
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const dailyTrades = completedTrades.filter(trade => new Date(trade.timestamp) >= oneDayAgo);
    const weeklyTrades = completedTrades.filter(trade => new Date(trade.timestamp) >= oneWeekAgo);
    const monthlyTrades = completedTrades.filter(trade => new Date(trade.timestamp) >= oneMonthAgo);

    metrics.timeframePerformance.daily = calculateProfitLoss(dailyTrades);
    metrics.timeframePerformance.weekly = calculateProfitLoss(weeklyTrades);
    metrics.timeframePerformance.monthly = calculateProfitLoss(monthlyTrades);

    // Save updated metrics
    fs.writeFileSync(PERFORMANCE_DB_PATH, JSON.stringify(metrics, null, 2));

    return metrics;
  } catch (error) {
    console.error('[DATABASE] Error updating performance metrics:', error);
    return getPerformanceMetrics();
  }
};

// Helper function to calculate profit/loss for a set of trades
const calculateProfitLoss = (trades: Trade[]): number => {
  return trades.reduce((total, trade) => total + (trade.performanceTracking?.profitLoss || 0), 0);
};

// Get performance metrics
export const getPerformanceMetrics = (): PerformanceMetrics => {
  try {
    initializeDatabase();

    const metricsData = fs.readFileSync(PERFORMANCE_DB_PATH, 'utf8');
    return JSON.parse(metricsData);
  } catch (error) {
    console.error('[DATABASE] Error getting performance metrics:', error);

    // Return default metrics
    return {
      totalTrades: 0,
      successfulTrades: 0,
      failedTrades: 0,
      pendingTrades: 0,
      winRate: 0,
      averageProfitLoss: 0,
      totalProfitLoss: 0,
      bestTrade: {
        token: '',
        profitLossPercentage: 0,
        timestamp: '',
      },
      worstTrade: {
        token: '',
        profitLossPercentage: 0,
        timestamp: '',
      },
      tokenPerformance: {},
      timeframePerformance: {
        daily: 0,
        weekly: 0,
        monthly: 0,
      },
    };
  }
};

// Get the hit rate for a specific token
export const getTokenHitRate = (token: string): number => {
  try {
    const metrics = getPerformanceMetrics();
    const tokenStats = metrics.tokenPerformance[token];

    if (!tokenStats || tokenStats.trades === 0) {
      return 0;
    }

    return tokenStats.winRate;
  } catch (error) {
    console.error(`[DATABASE] Error getting hit rate for token ${token}:`, error);
    return 0;
  }
};

// Initialize database on module load
initializeDatabase();