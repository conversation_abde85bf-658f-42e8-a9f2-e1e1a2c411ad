/**
 * LLM Service - Multi-provider support for trading analysis
 * Supports OpenAI, OpenRouter, and Custom LLM endpoints
 */

import OpenAI from 'openai';
import { LlmDecision } from './types';

export interface LLMConfig {
  provider: 'openai' | 'openrouter' | 'custom';
  model: string;
  apiKey: string;
  baseURL?: string;
  temperature?: number;
  maxTokens?: number;
}

export class LLMService {
  private client: OpenAI;
  private config: LLMConfig;
  private lastTokenData: any = null;

  constructor(config: LLMConfig) {
    this.config = config;
    
    // Initialize OpenAI client with appropriate configuration
    switch (config.provider) {
      case 'openai':
        this.client = new OpenAI({
          apiKey: config.apiKey,
        });
        break;
        
      case 'openrouter':
        this.client = new OpenAI({
          apiKey: config.apiKey,
          baseURL: 'https://openrouter.ai/api/v1',
          defaultHeaders: {
            'HTTP-Referer': 'https://mister-trading-agent.com',
            'X-Title': 'MISTER Trading Agent',
          },
        });
        break;
        
      case 'custom':
        this.client = new OpenAI({
          apiKey: config.apiKey,
          baseURL: config.baseURL || 'http://localhost:8000/v1',
        });
        break;
        
      default:
        throw new Error(`Unsupported LLM provider: ${config.provider}`);
    }
  }

  async getTradingDecision(
    systemPrompt: string,
    userPrompt: string,
    tokenData?: any
  ): Promise<LlmDecision> {
    // Store token data for validation
    this.lastTokenData = tokenData;
    try {
      console.log(`[LLM] Using ${this.config.provider} with model ${this.config.model}`);
      
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: this.config.temperature || 0.1,
        max_tokens: this.config.maxTokens || 10000,
      }, {
        // Add timeout for larger models like Gemini 2.5 Pro
        timeout: 120000, // 2 minutes
      });

      const content = response.choices[0].message?.content?.trim() || '';

      try {
        // Clean up the response - remove markdown code blocks if present
        let cleanContent = content;

        // Remove ```json and ``` markers
        if (cleanContent.startsWith('```json')) {
          cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        // Handle truncated JSON responses (common with Gemini)
        if (!cleanContent.trim().endsWith('}')) {
          console.log('[LLM] Response appears truncated, attempting to fix...');

          // Try to find and complete the JSON structure
          let fixedContent = cleanContent;

          // Count open and close braces to determine how many we need to close
          const openBraces = (fixedContent.match(/\{/g) || []).length;
          const closeBraces = (fixedContent.match(/\}/g) || []).length;
          const missingBraces = openBraces - closeBraces;

          // If we're missing closing braces, try to add them
          if (missingBraces > 0) {
            // Remove any incomplete line at the end
            const lines = fixedContent.split('\n');
            let lastCompleteLineIndex = lines.length - 1;

            // Find the last line that ends with a complete JSON element
            for (let i = lines.length - 1; i >= 0; i--) {
              const line = lines[i].trim();
              if (line.endsWith(',') || line.endsWith('{') || line.endsWith('}') || line.endsWith('"')) {
                lastCompleteLineIndex = i;
                break;
              }
            }

            // Reconstruct with complete lines only
            fixedContent = lines.slice(0, lastCompleteLineIndex + 1).join('\n');

            // Add missing closing braces
            for (let i = 0; i < missingBraces; i++) {
              fixedContent += '\n}';
            }
          }

          cleanContent = fixedContent;
        }

        const parsed = JSON.parse(cleanContent) as LlmDecision;
        return this.validateAndFixDecision(parsed);
      } catch (parseError) {
        console.error('[LLM] Error parsing JSON response:', parseError);
        console.log('[LLM] Raw response:', content);

        // Try to extract JSON from the response using regex as fallback
        try {
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const extractedJson = jsonMatch[0];
            const parsed = JSON.parse(extractedJson) as LlmDecision;
            console.log('[LLM] Successfully extracted JSON using regex fallback');
            return this.validateAndFixDecision(parsed);
          }
        } catch (regexError) {
          console.error('[LLM] Regex fallback also failed:', regexError);
        }

        throw parseError;
      }
    } catch (error) {
      console.error(`[LLM] Error with ${this.config.provider}:`, error);
      return this.getDefaultDecision();
    }
  }

  private validateAndFixDecision(parsed: LlmDecision): LlmDecision {
    // Validate and fix recommendation field
    if (!parsed.recommendation || !['buy', 'sell', 'hold'].includes(parsed.recommendation)) {
      console.log(`[LLM] Invalid recommendation value: ${parsed.recommendation}, defaulting to 'hold'`);
      parsed.recommendation = 'hold';
    }

    // Map recommendation to direction if direction is not valid
    if (!parsed.direction || !['buy', 'sell'].includes(parsed.direction)) {
      if (parsed.recommendation === 'hold') {
        parsed.direction = 'buy';
        parsed.trade = 'false';
        console.log(`[LLM] Recommendation is 'hold', setting direction to 'buy' and trade to 'false'`);
      } else {
        parsed.direction = parsed.recommendation as 'buy' | 'sell';
        console.log(`[LLM] Setting direction to match recommendation: ${parsed.direction}`);
      }
    }

    // Validate confidence
    if (typeof parsed.confidence !== 'number' || isNaN(parsed.confidence)) {
      console.log('[LLM] Invalid confidence value, defaulting to 0');
      parsed.confidence = 0;
    }

    // Validate size
    if (typeof parsed.size !== 'number' || isNaN(parsed.size)) {
      console.log('[LLM] Invalid size value, defaulting to 50');
      parsed.size = 50;
    }

    // PROFESSIONAL TRADER COHERENCE CHECK
    // If analysis indicates severe problems, override the recommendation
    const coherenceCheck = this.performCoherenceCheck(parsed);
    if (!coherenceCheck.isCoherent) {
      console.log(`[LLM] COHERENCE FAILURE: ${coherenceCheck.reason}`);
      console.log(`[LLM] Overriding recommendation from '${parsed.recommendation}' to 'hold'`);
      parsed.recommendation = 'hold';
      parsed.trade = 'false';
      parsed.confidence = Math.min(parsed.confidence, 3); // Cap confidence at 3

      // Add coherence warning to reasoning
      if (parsed.reasoning) {
        parsed.reasoning.confidence_explanation = `COHERENCE WARNING: ${coherenceCheck.reason}. Original confidence reduced to ${parsed.confidence}.`;
      }
    }

    // Check if sentiment data was provided but not used
    if (this.lastTokenData?.sentiment_data && parsed.fundamentalFactors) {
      const newsText = parsed.fundamentalFactors.news?.toLowerCase() || '';
      const sentimentText = parsed.fundamentalFactors.sentiment?.toLowerCase() || '';

      if (!newsText.includes('twitter') && !newsText.includes('sentiment') &&
          !sentimentText.includes('bullish') && !sentimentText.includes('bearish')) {
        console.log(`[LLM] WARNING: Sentiment data available but not used in analysis`);

        // Add sentiment info to news if missing
        const sentimentData = this.lastTokenData.sentiment_data.sentiment_analysis;
        if (sentimentData && parsed.fundamentalFactors.news) {
          parsed.fundamentalFactors.news += ` Twitter sentiment: ${sentimentData.overall_sentiment} (${Math.round(sentimentData.confidence * 100)}% confidence) with ${sentimentData.catalysts?.length || 0} recent catalysts.`;
        }
      }
    }

    return parsed;
  }

  /**
   * Professional trader coherence check - prevents contradictory decisions
   */
  private performCoherenceCheck(decision: LlmDecision): { isCoherent: boolean; reason: string } {
    const summary = decision.summary?.toLowerCase() || '';
    const reasoning = JSON.stringify(decision.reasoning || {}).toLowerCase();
    const allText = summary + ' ' + reasoning;

    // Check for severe negative indicators that should prevent buying
    const severeNegatives = [
      'pump-and-dump',
      'pump and dump',
      'falling knife',
      'severe price collapse',
      'price collapse',
      'crashed',
      'crash',
      'bubble pop',
      'extremely poor liquidity',
      'zero-volume candles',
      'near-zero volume',
      'alarmingly low',
      'major red flag',
      'exceptionally high-risk',
      'extremely low liquidity',
      'severe liquidity',
      'losing all its remaining value',
      'substantial risk',
      'significant slippage',
      'difficult to enter or exit',
      'overwhelmingly bearish',
      'decisively bearish',
      'complete failure',
      'distribution phase',
      'markdown phase',
      'post-hype',
      'retraced nearly the entire',
      'collapsed over',
      'severe downtrend',
      'failed launch',
      'high-risk token',
      'extreme volatility',
      'poor liquidity',
      'total loss',
      'total loss of capital',
      'extremely poor',
      'no sustained interest',
      'non-existent liquidity',
      'maximum volatility',
      'maximum risk',
      'purely speculative',
      'not supported by',
      'could easily collapse'
    ];

    // Check for low confidence with buy recommendation
    if (decision.recommendation === 'buy' && decision.confidence <= 4) {
      return {
        isCoherent: false,
        reason: `Low confidence (${decision.confidence}/10) contradicts buy recommendation`
      };
    }

    // Check for severe negative language with buy recommendation
    if (decision.recommendation === 'buy') {
      for (const negative of severeNegatives) {
        if (allText.includes(negative)) {
          return {
            isCoherent: false,
            reason: `Analysis mentions "${negative}" but recommends buying - contradictory`
          };
        }
      }
    }

    // Check for volume/liquidity warnings
    const liquidityWarnings = [
      'zero volume',
      'no volume',
      'extremely low volume',
      'poor liquidity',
      'low liquidity',
      'liquidity risk',
      'slippage'
    ];

    if (decision.recommendation === 'buy') {
      for (const warning of liquidityWarnings) {
        if (allText.includes(warning)) {
          return {
            isCoherent: false,
            reason: `Analysis warns about liquidity/volume issues but recommends buying`
          };
        }
      }
    }

    // Check for extreme volatility warnings
    const volatilityWarnings = [
      'extreme volatility',
      'extremely volatile',
      'high volatility',
      'significant downside risk',
      'substantial downside'
    ];

    if (decision.recommendation === 'buy' && decision.confidence <= 5) {
      for (const warning of volatilityWarnings) {
        if (allText.includes(warning)) {
          return {
            isCoherent: false,
            reason: `Analysis warns about extreme volatility with low confidence but recommends buying`
          };
        }
      }
    }

    return { isCoherent: true, reason: 'Analysis is coherent with recommendation' };
  }

  private getDefaultDecision(): LlmDecision {
    return {
      trade: 'false',
      direction: 'buy',
      confidence: 0,
      size: 0,
      recommendation: 'hold',
      reasoning: {
        price_analysis: 'LLM error - using default decision',
        volume_analysis: '',
        risk_assessment: '',
        confidence_explanation: '',
        size_explanation: ''
      }
    };
  }

  // Method to test the LLM connection
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: [
          { role: 'user', content: 'Respond with "OK" if you can understand this message.' }
        ],
        max_tokens: 10,
        temperature: 0
      });

      const content = response.choices[0].message?.content?.trim() || '';
      console.log(`[LLM] Connection test response: ${content}`);
      return content.toLowerCase().includes('ok');
    } catch (error) {
      console.error(`[LLM] Connection test failed:`, error);
      return false;
    }
  }

  // Get current configuration info
  getConfig(): LLMConfig {
    return { ...this.config };
  }
}

// Factory function to create LLM service from environment variables
export function createLLMService(): LLMService {
  const provider = (process.env.LLM_PROVIDER || 'openai') as 'openai' | 'openrouter' | 'custom';
  const model = process.env.LLM_MODEL || 'gpt-4o-mini';
  
  let apiKey: string;
  let baseURL: string | undefined;

  switch (provider) {
    case 'openai':
      apiKey = process.env.OPENAI_API_KEY || '';
      if (!apiKey) {
        throw new Error('OPENAI_API_KEY is required when using OpenAI provider');
      }
      break;
      
    case 'openrouter':
      apiKey = process.env.OPENROUTER_API_KEY || '';
      if (!apiKey) {
        throw new Error('OPENROUTER_API_KEY is required when using OpenRouter provider');
      }
      break;
      
    case 'custom':
      apiKey = process.env.CUSTOM_LLM_API_KEY || '';
      baseURL = process.env.CUSTOM_LLM_ENDPOINT || 'http://localhost:8000/v1';
      if (!apiKey) {
        throw new Error('CUSTOM_LLM_API_KEY is required when using custom provider');
      }
      break;
      
    default:
      throw new Error(`Unsupported LLM provider: ${provider}`);
  }

  const config: LLMConfig = {
    provider,
    model,
    apiKey,
    baseURL,
    temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.1'),
    maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '10000'),
  };

  return new LLMService(config);
}
