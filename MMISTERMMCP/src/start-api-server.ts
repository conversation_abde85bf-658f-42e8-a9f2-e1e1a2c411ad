/**
 * Start the MISTER Trading Bot API Server
 * Provides REST endpoints for frontend integration
 */

import { startApiServer } from './api/server.js';

console.log('🚀 Starting MISTER Trading Bot API Server...');

startApiServer()
  .then(() => {
    console.log('✅ API Server started successfully!');
    console.log('📋 Available endpoints:');
    console.log('   Health: GET /health');
    console.log('   User Config: GET/POST /api/config/:userId');
    console.log('   Managed Wallets: POST /api/wallets/create');
    console.log('   Trading: POST /api/trading/start');
    console.log('   Manual Trade: POST /api/trading/manual-trade');
  })
  .catch((error) => {
    console.error('❌ Failed to start API server:', error);
    process.exit(1);
  });
