export interface LlmDecision {
  trade: 'true'|'false';
  direction: 'buy'|'sell';
  recommendation?: 'buy'|'sell'|'hold';
  confidence: number;
  size: number;
  summary?: string;
  technicalAnalysis?: {
    priceAnalysis?: string;
    volumeAnalysis?: string;
    marketStructure?: string;
    supportResistance?: string;
    technicalIndicators?: {
      rsi?: string;
      rsiValue?: number;
      rsiTrend?: 'uptrend' | 'downtrend' | 'neutral';
      [k: string]: any;
    };
    [k: string]: any;
  };
  fundamentalFactors?: {
    news?: string;
    utility?: string;
    adoption?: string;
    community?: string;
    [k: string]: any;
  };
  riskAssessment?: {
    riskRewardRatio?: string | number;
    volatilityAssessment?: string;
    liquidityRisk?: string;
    [k: string]: any;
  };
  marketContext?: string;
  targetPrice?: string | { shortTerm: number, longTerm: number };
  stopLoss?: string | number;
  timeframe?: string;
  // Legacy structure fields
  reasoning?: {
    price_analysis?: string;
    volume_analysis?: string;
    risk_assessment?: string;
    confidence_explanation?: string;
    size_explanation?: string;
    [k: string]: any;
  };
}

export interface TradeRecord {
  id: string;
  timestamp: Date;
  ticker: string;
  unit: string;
  direction: 'buy' | 'sell';
  amount: number;
  price: number;
  txHash?: string;
  status: 'pending' | 'completed' | 'failed';
  error?: string;
}

export interface AnalysisRecord {
  id: string;
  timestamp: Date;
  ticker: string;
  unit: string;
  price: number;
  volume24h: number;
  decision: LlmDecision;
  resultingTrade?: TradeRecord;
}