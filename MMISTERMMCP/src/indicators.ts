/**
 * Technical indicators for token analysis
 *
 * This file contains functions for calculating various technical indicators
 * that can be used to enhance the trading bot's analysis capabilities.
 */

/**
 * Calculate Relative Strength Index (RSI)
 *
 * RSI = 100 - (100 / (1 + RS))
 * RS = Average Gain / Average Loss
 *
 * @param prices Array of prices (oldest to newest)
 * @param period Period for RSI calculation (default: 14)
 * @returns RSI value (0-100)
 */
export function calculateRSI(prices: number[], period: number = 14): number {
  if (prices.length < period + 1) {
    return 50; // Not enough data, return neutral value
  }

  // Check if all prices are the same
  const allSamePrice = prices.every(price => price === prices[0]);
  if (allSamePrice) {
    console.log('[INDICATORS] All prices are identical, returning neutral RSI value');
    return 50; // No price movement, return neutral value
  }

  // Calculate price changes
  const changes: number[] = [];
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1]);
  }

  // Check if there's enough price movement
  const significantChanges = changes.filter(change => Math.abs(change) > 0.0000001);
  if (significantChanges.length < 3) {
    console.log('[INDICATORS] Not enough significant price changes, returning neutral RSI value');
    return 50; // Not enough significant price movement, return neutral value
  }

  // Calculate gains and losses
  const gains: number[] = changes.map(change => change > 0 ? change : 0);
  const losses: number[] = changes.map(change => change < 0 ? Math.abs(change) : 0);

  // Calculate average gain and loss for the first period
  let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;

  // Calculate smoothed average gain and loss for the remaining periods
  for (let i = period; i < changes.length; i++) {
    avgGain = ((avgGain * (period - 1)) + gains[i]) / period;
    avgLoss = ((avgLoss * (period - 1)) + losses[i]) / period;
  }

  // Calculate RS and RSI
  if (avgLoss === 0) {
    // If there are no losses but there are gains, the asset is strongly bullish
    if (avgGain > 0) {
      console.log('[INDICATORS] No losses with gains, returning overbought RSI value');
      return 90; // Very bullish but not quite 100 to avoid extreme values
    }
    // If there are no losses and no gains, the price hasn't moved
    return 50; // Neutral
  }

  const rs = avgGain / avgLoss;
  const rsi = 100 - (100 / (1 + rs));

  return rsi;
}

/**
 * Determine RSI trend
 *
 * @param rsiValues Array of RSI values (oldest to newest)
 * @returns 'uptrend', 'downtrend', or 'neutral'
 */
export function getRSITrend(rsiValues: number[]): 'uptrend' | 'downtrend' | 'neutral' {
  if (rsiValues.length < 3) {
    return 'neutral'; // Not enough data
  }

  // Get the last 3 RSI values
  const last3 = rsiValues.slice(-3);

  // Check if RSI is consistently increasing or decreasing
  if (last3[0] < last3[1] && last3[1] < last3[2]) {
    return 'uptrend';
  } else if (last3[0] > last3[1] && last3[1] > last3[2]) {
    return 'downtrend';
  } else {
    return 'neutral';
  }
}

/**
 * Check for RSI divergence with price
 *
 * @param prices Array of prices (oldest to newest)
 * @param rsiValues Array of RSI values (oldest to newest)
 * @returns 'bullish', 'bearish', or 'none'
 */
export function checkRSIDivergence(prices: number[], rsiValues: number[]): 'bullish' | 'bearish' | 'none' {
  if (prices.length < 5 || rsiValues.length < 5) {
    return 'none'; // Not enough data
  }

  // Get the last 5 prices and RSI values
  const last5Prices = prices.slice(-5);
  const last5RSI = rsiValues.slice(-5);

  // Check for lower lows in price but higher lows in RSI (bullish divergence)
  if (last5Prices[0] > last5Prices[4] && last5RSI[0] < last5RSI[4]) {
    return 'bullish';
  }

  // Check for higher highs in price but lower highs in RSI (bearish divergence)
  if (last5Prices[0] < last5Prices[4] && last5RSI[0] > last5RSI[4]) {
    return 'bearish';
  }

  return 'none';
}

/**
 * Generate RSI analysis for a token
 *
 * @param prices Array of prices (oldest to newest)
 * @returns RSI analysis object
 */
export function analyzeRSI(prices: number[]): {
  value: number;
  trend: 'uptrend' | 'downtrend' | 'neutral';
  divergence: 'bullish' | 'bearish' | 'none';
  interpretation: 'overbought' | 'oversold' | 'neutral';
} {
  // Calculate RSI values for the last 15 periods
  const rsiValues: number[] = [];
  for (let i = 0; i < 3; i++) {
    const startIdx = Math.max(0, prices.length - 14 - i);
    const endIdx = prices.length - i;
    const periodPrices = prices.slice(startIdx, endIdx);
    rsiValues.unshift(calculateRSI(periodPrices));
  }

  const currentRSI = rsiValues[rsiValues.length - 1];
  const trend = getRSITrend(rsiValues);
  const divergence = checkRSIDivergence(prices, rsiValues);

  // Determine interpretation
  let interpretation: 'overbought' | 'oversold' | 'neutral' = 'neutral';
  if (currentRSI >= 70) {
    interpretation = 'overbought';
  } else if (currentRSI <= 30) {
    interpretation = 'oversold';
  }

  return {
    value: currentRSI,
    trend,
    divergence,
    interpretation
  };
}
