# Dependencies
node_modules/
/vendor/
/.pnp
.pnp.js

# Build outputs
/dist/
/build/
/out/
frontend/.next/
.next/

/coverage/

# Environment files (keeping .env in repo for private repo)
.env.local
.env.*.local

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs.txt

# Testing
/coverage/

# Temporary files
*.tmp
*.temp
.cache/

# System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Bot runtime files
bot-output.log
*.lock
