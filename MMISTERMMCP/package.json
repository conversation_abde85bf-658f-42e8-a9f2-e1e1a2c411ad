{"name": "mister-cardano-trading-agent", "version": "1.0.0", "main": "index.js", "scripts": {"build": "echo 'Build completed - using tsx for runtime compilation'", "start": "npx tsx src/portfolio-swaps.ts", "mcp": "cd mcp-server && node simple-server.js", "setup": "chmod +x setup.sh && ./setup.sh", "start-logs": "npx tsx src/portfolio-swaps.ts", "start-old": "node --experimental-specifier-resolution=node --loader ts-node/esm src/portfolio-swaps.ts", "dev": "cd frontend && npm run dev"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "description": "", "devDependencies": {"@types/node": "^22.10.6", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.7.3"}, "dependencies": {"@indigo-labs/dexter": "^5.4.10", "@indigo-labs/iris-sdk": "^1.0.3", "@types/bip39": "^2.4.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "axios": "^1.7.9", "bip39": "^3.1.0", "concurrently": "^8.2.2", "cors": "^2.8.5", "discord.js": "^14.18.0", "dotenv": "^16.4.7", "express": "^5.1.0", "openai": "^4.104.0", "uuid": "^11.1.0"}}