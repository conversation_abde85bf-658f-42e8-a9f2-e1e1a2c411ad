/**
 * Test script to preview enhanced Discord notifications with clickable links
 */

// Mock token data with social links
const mockTokenData = {
  ticker: 'SNEK',
  unit: '279c909f348e533da5808898f87f9a14bb2c3dfbbacccd631d927a3f534e454b',
  socialLinks: {
    twitter: 'https://twitter.com/snek',
    discord: 'https://discord.gg/snek',
    telegram: 'https://t.me/snek',
    website: 'https://snek.fi'
  },
  pools: [{
    dex: 'Minswap'
  }]
};

// Mock decision
const mockDecision = {
  recommendation: 'buy',
  confidence: 8,
  timeframe: 'Short-term',
  summary: 'Strong bullish momentum with high volume'
};

/**
 * Generate clickable links for token (same as Discord bot)
 */
function generateTokenLinks(ticker, unit, additionalData = {}) {
  const links = [];
  
  // TapTools trading link (always available)
  const tapToolsUrl = `https://www.taptools.io/charts/token?unit=${unit}`;
  links.push(`🔗 [Trade on TapTools](${tapToolsUrl})`);
  
  // Policy ID display (formatted beautifully)
  if (unit && unit.length > 56) {
    const policyId = unit.substring(0, 56);
    const assetName = unit.substring(56);
    links.push(`📋 **Policy ID:** \`${policyId}\``);
    if (assetName) {
      links.push(`🏷️ **Asset Name:** \`${assetName}\``);
    }
  }
  
  // Social media links from TapTools data
  if (additionalData.socialLinks) {
    if (additionalData.socialLinks.twitter) {
      const twitterUsername = extractTwitterUsername(additionalData.socialLinks.twitter);
      if (twitterUsername) {
        links.push(`🐦 [Follow @${twitterUsername}](https://twitter.com/${twitterUsername})`);
      }
    }
    
    if (additionalData.socialLinks.discord) {
      links.push(`💬 [Join Discord](${additionalData.socialLinks.discord})`);
    }
    
    if (additionalData.socialLinks.telegram) {
      links.push(`📱 [Join Telegram](${additionalData.socialLinks.telegram})`);
    }
    
    if (additionalData.socialLinks.website) {
      links.push(`🌐 [Official Website](${additionalData.socialLinks.website})`);
    }
  }
  
  // DEX links based on pools data
  if (additionalData.pools && additionalData.pools.length > 0) {
    const pool = additionalData.pools[0]; // Use the first/main pool
    if (pool.dex) {
      const dexName = pool.dex.toLowerCase();
      if (dexName.includes('minswap')) {
        links.push(`🔄 [Trade on Minswap](https://app.minswap.org/swap?currencySymbolA=&tokenNameA=&currencySymbolB=${ticker}&tokenNameB=${unit})`);
      } else if (dexName.includes('sundae')) {
        links.push(`🔄 [Trade on SundaeSwap](https://exchange.sundaeswap.finance/#/swap?swap_from=ada&swap_to=${unit})`);
      } else if (dexName.includes('muesli')) {
        links.push(`🔄 [Trade on MuesliSwap](https://ada.muesliswap.com/swap?base=.&quote=${unit})`);
      }
    }
  }
  
  // Cardano blockchain explorer links
  if (unit && unit.length > 56) {
    const policyId = unit.substring(0, 56);
    links.push(`🔍 [View on CardanoScan](https://cardanoscan.io/token/${policyId})`);
    links.push(`📊 [View on Pool.pm](https://pool.pm/${policyId})`);
  }
  
  return links.join('\n');
}

function extractTwitterUsername(twitterUrl) {
  if (!twitterUrl) return null;
  
  const patterns = [
    /twitter\.com\/([^\/\?]+)/i,
    /x\.com\/([^\/\?]+)/i,
    /@([a-zA-Z0-9_]+)/
  ];
  
  for (const pattern of patterns) {
    const match = twitterUrl.match(pattern);
    if (match && match[1]) {
      return match[1].replace('@', '');
    }
  }
  
  return null;
}

// Test the link generation
console.log('🚀 Enhanced Discord Notification Preview\n');
console.log('='.repeat(50));
console.log(`📊 ${mockTokenData.ticker} Analysis - ${mockDecision.recommendation.toUpperCase()}`);
console.log('='.repeat(50));
console.log(`Confidence: ${mockDecision.confidence}/10`);
console.log(`Timeframe: ${mockDecision.timeframe}`);
console.log(`Summary: ${mockDecision.summary}`);
console.log('\n🔗 Quick Actions & Links:');
console.log('='.repeat(30));
console.log(generateTokenLinks(mockTokenData.ticker, mockTokenData.unit, mockTokenData));
console.log('\n✅ This is how the enhanced Discord notifications will look!');
console.log('Users can now click directly to:');
console.log('- Trade on TapTools, Minswap, SundaeSwap, MuesliSwap');
console.log('- Follow on Twitter, join Discord/Telegram');
console.log('- View policy ID and explore on CardanoScan/Pool.pm');
console.log('- Visit official website');
