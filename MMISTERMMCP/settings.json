{"general": {"botEnabled": true, "autoTradeEnabled": true, "maxTradesPerDay": 5, "maxTradeSize": 100}, "portfolioAllocations": {"ada": 35, "meme": 20, "defi": 15, "major": 20, "new": 10}, "tradingLimits": {"minConfidence": 10, "minTokenAge": 7, "minLiquidity": 5000, "maxImpact": 5}, "riskManagement": {"dynamicPositionSizing": true, "stopLossEnabled": true, "trailingStopEnabled": false, "timeBasedExitEnabled": true, "defaultRiskLevel": "medium"}, "apiKeys": {"taptools": "WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO", "openai": "********************************************************************************************************************************************************************", "blockfrost": "mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu"}, "discord": {"channelId": "1329622661831327773", "notifications": {"trades": true, "analysis": true, "errors": true}}}