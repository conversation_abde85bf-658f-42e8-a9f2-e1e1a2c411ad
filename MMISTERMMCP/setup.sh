#!/bin/bash

# MISTER - Autonomous Cardano DEX Trading Agent Setup Script
# This script sets up the entire project with one command

echo "🤖 Setting up MISTER - Autonomous Cardano DEX Trading Agent..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the project
echo "🔨 Building the project..."
npm run build

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. This should be included in the repo."
    echo "Please make sure the .env file is present with all required environment variables."
    exit 1
fi

echo "✅ .env file found"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data
mkdir -p logs

# Set up MCP server
echo "🔧 Setting up MCP server..."
cd mcp-server
npm install
cd ..

# Make scripts executable
chmod +x setup.sh
if [ -f "setup-mcp-server.sh" ]; then
    chmod +x setup-mcp-server.sh
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Available commands:"
echo "  npm start                    - Start the trading agent"
echo "  npm run mcp                  - Start the MCP server"
echo "  npm run build                - Build the project"
echo "  npm test                     - Run tests (if available)"
echo ""
echo "MCP Server commands:"
echo "  cd mcp-server && node simple-server.js  - Start MCP server manually"
echo ""
echo "🚀 Your MISTER trading agent is ready to go!"
echo "💡 Make sure your wallet has sufficient ADA balance for trading"
echo "📊 Check Discord channel for trading notifications"
