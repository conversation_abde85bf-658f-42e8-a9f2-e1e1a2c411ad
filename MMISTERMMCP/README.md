# MISTER - Autonomous Cardano DEX Trading Agent

An AI-powered trading agent that uses TapTools data and GPT-4 analysis to execute trades across Cardano DEXs using [Indigo Labs' Dexter SDK](https://github.com/IndigoProtocol/dexter).

## 🚀 Quick Start (One Command Setup)

```bash
git clone https://github.com/LavonTMCQ/misteradamcp.git
cd misteradamcp
chmod +x setup.sh && ./setup.sh
```

That's it! The setup script will install dependencies, build the project, and prepare everything for you.

## Overview

MISTER is designed to automate trading on Cardano DEXs by analyzing market data and making informed trading decisions. The agent can be controlled directly or through the Model Context Protocol (MCP) server, allowing AI agents to start, stop, and monitor the trading bot.

## Features

- 🤖 GPT-4 Trading Analysis
  - Price and volume pattern recognition
  - Market structure analysis
  - Confidence-based trade execution
  - Detailed reasoning for each decision
  - Historical performance integration

- 📊 TapTools Data Integration
  - Top volume token tracking
  - Price and volume metrics
  - Trading statistics and pools data
  - OHLCV historical data

- 💼 Category-Based Portfolio Management
  - Supports multiple token categories:
    - ADA
    - Meme Coins
    - Shards/Talos
    - INDY
    - Major Projects
    - New Positions
  - Target ratio enforcement
  - Balance checks and position sizing

- 🔄 Dexter & Iris Integration
  - Automated pool discovery
  - Slippage protection
  - Transaction verification
  - Error handling

- 📈 Performance Tracking
  - Trade history database
  - Token-specific success rates
  - Profit/loss calculations
  - Historical trade analytics
  - Enhanced decision making with past performance

- 🔔 Enhanced Discord Notifications
  - Detailed token analysis
  - Trading recommendations with reasoning
  - Historical performance context
  - Transaction notifications with analysis

- 🤝 MCP Integration
  - Control the bot through Model Context Protocol
  - Start/stop trading via AI agents
  - Real-time trading results and portfolio information
  - Seamless integration with agent workflows

## Prerequisites

- Node.js v18+
- NPM or Yarn
- TapTools API Key
- OpenAI API Key (GPT-4 access)
- Blockfrost Project ID
- Cardano Wallet Seed Phrase
- Discord Webhook URL (for notifications)

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/talos-dexter-bot.git

# Install dependencies
cd talos-dexter-bot
npm install

# Copy example config files
cp src/config/prompts.template.ts src/config/prompts.ts
cp src/config/analysis-guidelines.template.ts src/config/analysis-guidelines.ts
```

## Configuration

1. Create a `.env` file:
```env
TAP_TOOLS_API_KEY=your_taptools_key
OPENAI_API_KEY=your_openai_key
BLOCKFROST_PROJECT_ID=your_blockfrost_id
SEED_PHRASE="your seed phrase"
CARDANO_ADDRESS=your_cardano_address
DISCORD_TOKEN=your_discord_bot_token
DISCORD_CHANNEL_ID=your_discord_channel_id
MAX_ADA_TRADE_PERCENTAGE=50  # Maximum percentage of available ADA to use per trade (50%)
MIN_ADA_RESERVE=5           # Minimum ADA to keep in wallet (never trade this amount)
```

2. Customize the AI agent's behavior in `src/config/prompts.ts`
3. Adjust analysis parameters in `src/config/analysis-guidelines.ts`
4. Modify portfolio ratios in `portfolio-swaps.ts` if needed
5. Configure the trading schedule (default is daily at the same time each day)

## 🎮 Usage Commands

### Start Trading Agent
```bash
npm start
```

### Start MCP Server (for AI agent control)
```bash
npm run mcp
```

### Build Project
```bash
npm run build
```

### Development Mode
```bash
npm run dev
```

## 🤖 How It Works

The bot will:
1. Fetch top volume tokens from TapTools
2. Analyze each token using GPT-4
3. Execute trades based on AI recommendations
4. Send notifications to Discord
5. Update the database with trade results

## 🔗 MCP Integration

The MISTER trading agent can be controlled through a Model Context Protocol (MCP) server, allowing AI agents to interact with the bot.

### Setting Up the MCP Server

```bash
# Navigate to the MCP server directory
cd mcp-server

# Install dependencies
npm install

# Start the MCP server
node simple-server.js
```

### Connecting to the MCP Server

To connect your AI agent or IDE to the MCP server, use the following command:

```
node /absolute/path/to/talos-dexter-integration/mcp-server/simple-server.js
```

Replace `/absolute/path/to/` with the actual path to your project directory.

### Available MCP Tools

The MCP server provides the following tools for controlling the trading bot:

1. **startTrading**: Start the trading bot
   - No parameters required
   - Returns: `{ success: boolean, message: string, pid: number }`

2. **checkStatus**: Check the status of the trading bot
   - No parameters required
   - Returns: `{ running: boolean, lastRun: string, lastResults: object, pid: number }`

3. **getResults**: Get the results of the most recent trading run
   - No parameters required
   - Returns: Trading results including portfolio information and trade decisions

4. **stopTrading**: Stop the trading bot
   - No parameters required
   - Returns: `{ success: boolean, message: string }`

### Best Practices for MCP Integration

- **Check Status Before Starting**: Always check if the bot is already running before attempting to start it
- **Periodic Result Checking**: Use the `getResults` tool to periodically check for new trading decisions
- **Always Stop Properly**: Use the `stopTrading` tool to properly stop the bot when finished
- **Error Handling**: Handle potential errors from the MCP tools in your agent's logic
- **Resource Management**: Be mindful of system resources when running the bot for extended periods

## Trading Schedule

The bot is configured to run once per day by default, which provides several benefits:

- Reduces API usage and computational resources
- Allows for more comprehensive daily market analysis
- Avoids excessive trading fees from frequent transactions
- Provides clear daily summaries via Discord notifications

Each time the bot runs, it:
1. Fetches top volume tokens from TapTools
2. Analyzes market conditions using GPT-4
3. Makes trading decisions based on portfolio balance
4. Executes trades when appropriate conditions are met
5. Schedules the next run and sends a Discord notification

You can modify the schedule in `src/portfolio-swaps.ts` if you need more frequent or less frequent trading intervals.

## Architecture

```
src/
├── config/                 # Configuration files
│   ├── prompts.ts          # AI agent personality & prompts
│   └── analysis-guidelines.ts  # Trading analysis rules
├── database.ts             # Trade tracking and performance analytics
├── discord-bot.ts          # Discord notification system
├── portfolio-swaps.ts      # Main bot logic
└── types/                  # TypeScript type definitions
mcp-server/                 # Model Context Protocol server
├── simple-server.js        # MCP server implementation
├── tools/                  # MCP tools implementation
└── bot-status.json         # Bot status tracking file
```

## Performance Tracking System

The bot now includes a comprehensive performance tracking system that:

1. **Records All Trades**: Every transaction is recorded with details including price, quantity, status, and LLM decision context.

2. **Calculates Success Metrics**: The system tracks win rates, profit/loss, and performance trends over time.

3. **Token-Specific Analysis**: Performance metrics are calculated per token, allowing the bot to learn from past successes and failures.

4. **Enhanced Decision Making**: Historical trade data is fed back into the LLM's analysis process, creating a feedback loop that improves decisions over time.

5. **Performance Dashboard**: Access trade history and performance analytics through the frontend interface.

## Frontend Integration

The bot includes a frontend dashboard that visualizes:

1. Overall trading performance
2. Token-specific metrics
3. Historical trade data
4. Success rates and profit/loss charts
5. Real-time token analysis

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Warning

This bot is experimental software. Use at your own risk. Never trade with funds you cannot afford to lose.

## Customization

- Adjust target ratios in portfolio management
- Modify trading thresholds and confidence levels
- Add new token categories
- Implement additional technical analysis
- Customize the AI agent's personality
- Configure performance metrics thresholds
- Adjust `MAX_ADA_TRADE_PERCENTAGE` to control how much of your ADA balance can be used in a single trade
- Set `MIN_ADA_RESERVE` to ensure a minimum ADA balance is always maintained

## License

MIT License

Copyright (c) 2024 Flux Point Studios, Inc. (http://www.fluxpointstudios.com)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

## Acknowledgments

- [Indigo Protocol](https://indigoprotocol.io/) & Zachary Sluder for the Dexter and Iris SDKs
- TapTools for market data API
- OpenAI for GPT-4o API

## Safety Features

The bot includes several safety mechanisms to prevent errors and protect your funds:

- **Balance Protection**: Ensures the bot never attempts to use more ADA than is available in your wallet
  - Uses configurable percentage of available ADA for trading (`MAX_ADA_TRADE_PERCENTAGE`)
  - Maintains a minimum ADA reserve that's never touched (`MIN_ADA_RESERVE`)

- **Token Verification**: Verifies token ownership before attempting to sell
  - Checks if tokens exist in wallet before initiating a sell order
  - Prevents failed transactions due to insufficient token balances

- **Portfolio Management**:
  - Maintains target ratios across different asset categories
  - Prevents overexposure to any single token or category

- **Error Handling**:
  - Comprehensive error management across all operations
  - Detailed logging of all activities for troubleshooting
  - Failed transactions are recorded with error messages
