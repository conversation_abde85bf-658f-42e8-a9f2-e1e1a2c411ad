# MISTER Dashboard

This is a web-based dashboard for the MISTER autonomous Cardano DEX trading bot. The dashboard provides visualization of trading activities, token analysis, and configuration options for the bot.

## Features

- **Dashboard Overview**: Visual summary of portfolio performance, recent trades, and token analysis
- **Trade History**: Detailed view of all trades with filtering and search capabilities
- **Token Analysis**: In-depth analysis of tokens with technical indicators and recommendations
- **Settings**: Comprehensive configuration of the trading bot, including API connections and token lists
- **Discord Integration**: Send trading signals and analysis to Discord

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- MISTER trading bot backend running

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/mister-dashboard.git
cd mister-dashboard
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env.local` file in the root directory with your environment variables:
```
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the dashboard.

## Project Structure

```
├── components/         # Reusable UI components
├── pages/              # Next.js pages
│   ├── index.js        # Dashboard overview
│   ├── trades.js       # Trade history page
│   ├── analysis.js     # Token analysis page
│   ├── settings.js     # Bot configuration page
│   └── _app.js         # App component with Chakra UI provider
├── public/             # Static assets
└── styles/             # Global styles
```

## Connecting to the Backend

By default, the dashboard expects the MISTER trading bot API to be available at the URL specified in the `NEXT_PUBLIC_API_URL` environment variable. Ensure that CORS is properly configured on your backend server to allow requests from the dashboard.

## Development

This project uses:

- [Next.js](https://nextjs.org/) - React framework
- [Chakra UI](https://chakra-ui.com/) - Component library
- [Chart.js](https://www.chartjs.org/) - Data visualization
- [Axios](https://axios-http.com/) - HTTP client

## Building for Production

To build the application for production:

```bash
npm run build
```

To start the production server:

```bash
npm start
```

## Docker

A Dockerfile is available to containerize the application:

```bash
# Build the Docker image
docker build -t mister-dashboard .

# Run the container
docker run -p 3000:3000 mister-dashboard
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.