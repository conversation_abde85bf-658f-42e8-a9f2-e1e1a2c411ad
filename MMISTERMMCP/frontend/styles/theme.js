import { extendTheme } from '@chakra-ui/react';

// Customize the color palette
const colors = {
  brand: {
    50: '#e6f1ff',
    100: '#b3d4ff',
    200: '#80b7ff',
    300: '#4d9aff',
    400: '#1a7dff',
    500: '#0066e6', // Primary brand color
    600: '#0052b8',
    700: '#003d8a',
    800: '#00295c',
    900: '#00142e',
  },
  success: {
    50: '#e6f9e6',
    100: '#b3ecb3',
    200: '#80df80',
    300: '#4dd24d',
    400: '#1ac51a',
    500: '#00b200', // Success color
    600: '#008e00',
    700: '#006b00',
    800: '#004700',
    900: '#002400',
  },
  error: {
    50: '#ffe6e6',
    100: '#ffb3b3',
    200: '#ff8080',
    300: '#ff4d4d',
    400: '#ff1a1a',
    500: '#e60000', // Error color
    600: '#b80000',
    700: '#8a0000',
    800: '#5c0000',
    900: '#2e0000',
  },
  warning: {
    50: '#fff8e6',
    100: '#ffeab3',
    200: '#ffdc80',
    300: '#ffcd4d',
    400: '#ffbf1a',
    500: '#e6ab00', // Warning color
    600: '#b88900',
    700: '#8a6600',
    800: '#5c4400',
    900: '#2e2200',
  },
  gray: {
    50: '#f7f9fa',
    100: '#ebeef0',
    200: '#d9dee2',
    300: '#c5ccd3',
    400: '#b0b9c2',
    500: '#99a4af',
    600: '#768390',
    700: '#5d6b7a',
    800: '#434e5c',
    900: '#293039',
  },
};

// Customize the font stack
const fonts = {
  heading: "'Inter', system-ui, sans-serif",
  body: "'Inter', system-ui, sans-serif",
};

// Customize button styles
const Button = {
  baseStyle: {
    fontWeight: 'medium',
    borderRadius: 'md',
  },
  variants: {
    solid: (props) => ({
      bg: props.colorScheme === 'brand' ? `brand.500` : undefined,
      _hover: {
        bg: props.colorScheme === 'brand' ? `brand.600` : undefined,
      },
    }),
    outline: (props) => ({
      borderColor: props.colorScheme === 'brand' ? `brand.500` : undefined,
      color: props.colorScheme === 'brand' ? `brand.500` : undefined,
    }),
  },
  defaultProps: {
    colorScheme: 'brand',
  },
};

// Custom Card component style
const Card = {
  baseStyle: {
    p: '4',
    borderRadius: 'lg',
    boxShadow: 'sm',
    bg: 'white',
    _dark: {
      bg: 'gray.800',
    },
  },
};

// Custom badge component style
const Badge = {
  baseStyle: {
    borderRadius: 'full',
    px: 2,
    py: 0.5,
    fontWeight: 'medium',
  },
};

// Custom styles for form elements
const Input = {
  variants: {
    outline: {
      field: {
        borderRadius: 'md',
      }
    }
  },
  defaultProps: {
    variant: 'outline',
  },
};

// Create the theme configuration
const theme = extendTheme({
  colors,
  fonts,
  components: {
    Button,
    Card,
    Badge,
    Input,
  },
  config: {
    initialColorMode: 'dark',
    useSystemColorMode: false,
  },
  styles: {
    global: (props) => ({
      body: {
        bg: props.colorMode === 'dark' ? 'gray.900' : 'gray.50',
        color: props.colorMode === 'dark' ? 'white' : 'gray.900',
      },
    }),
  },
});

export default theme; 