{"name": "mister-dashboard", "version": "1.0.0", "description": "Dashboard for MISTER Trading Bot", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": ["cardano", "dexter", "trading", "bot", "dashboard"], "author": "", "license": "ISC", "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "axios": "^1.8.3", "chart.js": "^4.4.8", "framer-motion": "^10.18.0", "next": "^13.5.8", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0"}, "devDependencies": {"tsx": "^4.19.3"}}