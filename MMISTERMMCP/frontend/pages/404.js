import {
  Box,
  Heading,
  Text,
  Button,
  Flex,
  useColorModeValue,
  Image,
} from '@chakra-ui/react';
import NextLink from 'next/link';
import Layout from '../components/Layout';

export default function Custom404() {
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const buttonColorScheme = useColorModeValue('brand', 'blue');

  return (
    <Layout>
      <Flex
        align="center"
        justify="center"
        direction="column"
        minH="70vh"
        textAlign="center"
        gap={6}
      >
        <Heading
          as="h1"
          size="4xl"
          fontWeight="bold"
          color={useColorModeValue('brand.500', 'brand.300')}
        >
          404
        </Heading>
        
        <Box position="relative" width="200px" height="200px">
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            width="150px"
            height="150px"
            borderRadius="full"
            bg={useColorModeValue('gray.100', 'gray.700')}
            overflow="hidden"
          />
          
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -60%)"
            zIndex={2}
          >
            <Text fontSize="9xl" lineHeight="1">
              🤖
            </Text>
          </Box>
          
          <Box
            position="absolute"
            bottom="10px"
            left="50%"
            transform="translateX(-50%)"
            width="120px"
            height="30px"
            borderRadius="full"
            bg={useColorModeValue('gray.200', 'gray.600')}
            filter="blur(10px)"
            zIndex={1}
          />
        </Box>
        
        <Heading
          as="h2"
          size="xl"
          mb={2}
        >
          Page Not Found
        </Heading>
        
        <Text fontSize="lg" color={textColor} maxW="lg">
          Oops! The trading bot couldn't find the page you're looking for. It might have been moved, deleted, or perhaps never existed.
        </Text>
        
        <Button 
          as={NextLink}
          href="/"
          colorScheme={buttonColorScheme} 
          size="lg"
          mt={4}
          fontWeight="medium"
        >
          Return to Dashboard
        </Button>
      </Flex>
    </Layout>
  );
} 