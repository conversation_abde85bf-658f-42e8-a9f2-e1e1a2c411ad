import { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Button,
  Select,
  Input,
  HStack,
  VStack,
  InputGroup,
  InputLeftElement,
  FormControl,
  FormLabel,
  useColorModeValue,
  Spinner,
  IconButton,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { SearchIcon, ChevronDownIcon, RepeatIcon, InfoIcon } from '@chakra-ui/icons';
import Layout from '../components/Layout';

// Mock trade data
const mockTrades = Array(20).fill(0).map((_, i) => {
  const tokens = ['SNEK', 'WMTX', 'HUNT', 'HOSKY', 'IAG', 'BODEGA', 'BTN', 'LENFI', 'LIQWID'];
  const directions = ['buy', 'sell'];
  const statuses = ['completed', 'pending', 'failed'];
  
  const direction = directions[Math.floor(Math.random() * directions.length)];
  const token = tokens[Math.floor(Math.random() * tokens.length)];
  const amount = Math.floor(Math.random() * 200) + 10;
  const price = (Math.random() * (token === 'HOSKY' ? 0.001 : 1)).toFixed(6);
  const total = (amount * price).toFixed(2);
  
  // Generate a date within the last month
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * 30));
  
  return {
    id: i + 1,
    timestamp: date.toISOString().replace('T', ' ').substring(0, 19),
    token,
    direction,
    amount,
    price: parseFloat(price),
    total: parseFloat(total),
    status: statuses[Math.floor(Math.random() * statuses.length)],
    txHash: `tx${Math.random().toString(36).substring(2, 10)}${Math.random().toString(36).substring(2, 10)}`
  };
});

export default function Trades() {
  const [loading, setLoading] = useState(false);
  const [trades, setTrades] = useState(mockTrades);
  const [filteredTrades, setFilteredTrades] = useState(mockTrades);
  const [filters, setFilters] = useState({
    token: '',
    direction: '',
    status: '',
    dateRange: '',
    search: ''
  });
  const [selectedTrade, setSelectedTrade] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Apply filters when they change
  useEffect(() => {
    setLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      let filtered = [...trades];

      // Apply filters
      if (filters.token) {
        filtered = filtered.filter(trade => trade.token === filters.token);
      }
      
      if (filters.direction) {
        filtered = filtered.filter(trade => trade.direction === filters.direction);
      }
      
      if (filters.status) {
        filtered = filtered.filter(trade => trade.status === filters.status);
      }
      
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filtered = filtered.filter(trade =>
          trade.token.toLowerCase().includes(searchTerm) ||
          trade.txHash.toLowerCase().includes(searchTerm)
        );
      }
      
      // Sort by date (newest first)
      filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      
      setFilteredTrades(filtered);
      setLoading(false);
    }, 300);
  }, [filters, trades]);

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      token: '',
      direction: '',
      status: '',
      dateRange: '',
      search: ''
    });
  };

  const openTradeDetails = (trade) => {
    setSelectedTrade(trade);
    onOpen();
  };

  const tokens = [...new Set(trades.map(trade => trade.token))].sort();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Layout>
      <Heading as="h1" size="xl" mb={6}>
        Trade History
      </Heading>

      {/* Filters Section */}
      <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm" mb={6}>
        <VStack align="stretch" spacing={4}>
          <Flex justify="space-between" align="center">
            <Heading as="h3" size="md">
              Filters
            </Heading>
            <Button
              size="sm"
              leftIcon={<RepeatIcon />}
              variant="outline"
              onClick={clearFilters}
            >
              Clear Filters
            </Button>
          </Flex>
          
          <Flex 
            direction={{ base: 'column', md: 'row' }} 
            gap={4} 
            wrap="wrap"
          >
            <FormControl maxW={{ base: "100%", md: "200px" }}>
              <FormLabel fontSize="sm">Token</FormLabel>
              <Select 
                placeholder="All Tokens" 
                size="sm"
                value={filters.token}
                onChange={(e) => handleFilterChange('token', e.target.value)}
              >
                {tokens.map(token => (
                  <option key={token} value={token}>{token}</option>
                ))}
              </Select>
            </FormControl>
            
            <FormControl maxW={{ base: "100%", md: "150px" }}>
              <FormLabel fontSize="sm">Direction</FormLabel>
              <Select 
                placeholder="All" 
                size="sm"
                value={filters.direction}
                onChange={(e) => handleFilterChange('direction', e.target.value)}
              >
                <option value="buy">Buy</option>
                <option value="sell">Sell</option>
              </Select>
            </FormControl>
            
            <FormControl maxW={{ base: "100%", md: "150px" }}>
              <FormLabel fontSize="sm">Status</FormLabel>
              <Select 
                placeholder="All" 
                size="sm"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </Select>
            </FormControl>
            
            <FormControl flex={1}>
              <FormLabel fontSize="sm">Search</FormLabel>
              <InputGroup size="sm">
                <InputLeftElement pointerEvents="none">
                  <SearchIcon color="gray.400" />
                </InputLeftElement>
                <Input 
                  placeholder="Search by token, transaction hash..." 
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </InputGroup>
            </FormControl>
          </Flex>
        </VStack>
      </Box>

      {/* Trades Table */}
      <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
        {loading ? (
          <Flex justify="center" align="center" h="200px">
            <Spinner size="lg" />
          </Flex>
        ) : filteredTrades.length === 0 ? (
          <Alert status="info" borderRadius="md">
            <AlertIcon />
            <AlertTitle>No trades found!</AlertTitle>
            <AlertDescription>Try adjusting your filters.</AlertDescription>
          </Alert>
        ) : (
          <TableContainer>
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Time</Th>
                  <Th>Token</Th>
                  <Th>Type</Th>
                  <Th isNumeric>Amount</Th>
                  <Th isNumeric>Price (ADA)</Th>
                  <Th isNumeric>Total (ADA)</Th>
                  <Th>Status</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredTrades.map((trade) => (
                  <Tr key={trade.id} cursor="pointer" _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}>
                    <Td>{trade.timestamp}</Td>
                    <Td fontWeight="bold">{trade.token}</Td>
                    <Td>
                      <Badge 
                        colorScheme={trade.direction === 'buy' ? 'green' : 'red'}
                      >
                        {trade.direction.toUpperCase()}
                      </Badge>
                    </Td>
                    <Td isNumeric>{trade.amount}</Td>
                    <Td isNumeric>{trade.price.toFixed(6)}</Td>
                    <Td isNumeric fontWeight="semibold">{trade.total.toFixed(2)}</Td>
                    <Td>
                      <Badge 
                        colorScheme={
                          trade.status === 'completed' ? 'green' : 
                          trade.status === 'pending' ? 'yellow' : 'red'
                        }
                      >
                        {trade.status}
                      </Badge>
                    </Td>
                    <Td>
                      <Tooltip label="View Details">
                        <IconButton
                          size="sm"
                          icon={<InfoIcon />}
                          aria-label="View Details"
                          variant="ghost"
                          onClick={() => openTradeDetails(trade)}
                        />
                      </Tooltip>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        )}
      </Box>

      {/* Trade Details Modal */}
      {selectedTrade && (
        <Modal isOpen={isOpen} onClose={onClose} size="lg">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>
              Trade Details
              <Badge 
                ml={2}
                colorScheme={
                  selectedTrade.status === 'completed' ? 'green' : 
                  selectedTrade.status === 'pending' ? 'yellow' : 'red'
                }
              >
                {selectedTrade.status.toUpperCase()}
              </Badge>
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack spacing={4} align="stretch">
                <Flex justify="space-between">
                  <Text fontWeight="bold">Transaction Time:</Text>
                  <Text>{selectedTrade.timestamp}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text fontWeight="bold">Token:</Text>
                  <Text>{selectedTrade.token}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text fontWeight="bold">Direction:</Text>
                  <Badge colorScheme={selectedTrade.direction === 'buy' ? 'green' : 'red'}>
                    {selectedTrade.direction.toUpperCase()}
                  </Badge>
                </Flex>
                <Flex justify="space-between">
                  <Text fontWeight="bold">Amount:</Text>
                  <Text>{selectedTrade.amount}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text fontWeight="bold">Price (ADA):</Text>
                  <Text>{selectedTrade.price.toFixed(6)}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text fontWeight="bold">Total (ADA):</Text>
                  <Text fontWeight="semibold">{selectedTrade.total.toFixed(2)}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text fontWeight="bold">Transaction Hash:</Text>
                  <Text 
                    fontFamily="monospace" 
                    fontSize="sm" 
                    color="blue.500" 
                    textDecoration="underline"
                  >
                    {selectedTrade.txHash}
                  </Text>
                </Flex>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button colorScheme="blue" mr={3} onClick={onClose}>
                Close
              </Button>
              <Button variant="outline">
                View on Explorer
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </Layout>
  );
} 