import { useState, useEffect } from 'react';
import {
  Box, 
  Heading, 
  Text, 
  SimpleGrid, 
  Stat, 
  StatLabel, 
  StatNumber, 
  StatHelpText, 
  StatArrow, 
  StatGroup,
  Flex,
  Badge,
  Icon,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
  Button,
  VStack,
  HStack,
  Divider
} from '@chakra-ui/react';
import { Line, Doughnut } from 'react-chartjs-2';
import { Chart, registerables } from 'chart.js';
import Layout from '../components/Layout';
import api from '../services/api';
import WalletSetup from '../components/WalletSetup';

// Register Chart.js components
Chart.register(...registerables);

export default function Home() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [walletConnected, setWalletConnected] = useState(false);
  const [walletStatus, setWalletStatus] = useState(null);
  const [botStatus, setBotStatus] = useState({ running: false, lastRun: null });
  const [dashboardData, setDashboardData] = useState({
    stats: {
      totalValue: 0,
      totalTrades: 0,
      successRate: 0,
      profitLoss: 0
    },
    recentTokens: [],
    recentTrades: [],
    performanceData: {
      labels: [],
      datasets: [
        {
          label: 'Portfolio Value (ADA)',
          data: [],
          fill: false,
          borderColor: 'rgb(26, 133, 255)',
          tension: 0.1
        }
      ]
    },
    portfolioData: {
      labels: [],
      datasets: [
        {
          data: [],
          backgroundColor: [
            'rgb(26, 133, 255)',
            'rgb(255, 99, 132)',
            'rgb(54, 162, 235)',
            'rgb(255, 206, 86)',
            'rgb(75, 192, 192)',
            'rgb(153, 102, 255)',
          ],
          borderWidth: 1,
        },
      ],
    }
  });

  // Fetch data from API
  useEffect(() => {
    const checkWalletStatus = async () => {
      try {
        const response = await api.wallet.getStatus();
        setWalletConnected(response.data.connected);
        setWalletStatus(response.data);
        
        // If wallet is connected, fetch dashboard data
        if (response.data.connected) {
          fetchDashboardData();
        } else {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error checking wallet status:', err);
        setWalletConnected(false);
        setLoading(false);
      }
    };
    
    const fetchBotStatus = async () => {
      try {
        const response = await api.bot.getStatus();
        setBotStatus(response.data);
      } catch (err) {
        console.error('Error fetching bot status:', err);
      }
    };
    
    checkWalletStatus();
    fetchBotStatus();
    
    // Poll for updates every 30 seconds
    const interval = setInterval(() => {
      if (walletConnected) {
        fetchDashboardData();
      }
      fetchBotStatus();
    }, 30000);
    
    return () => clearInterval(interval);
  }, [walletConnected]);

  const fetchDashboardData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch summary data
      const summaryResponse = await api.dashboard.getSummary();
      const performanceResponse = await api.dashboard.getPerformance('30d');
      const portfolioResponse = await api.dashboard.getPortfolioBreakdown();
      const recentTradesResponse = await api.trades.getRecentTrades(5);
      const analyzedTokensResponse = await api.bot.getAnalyzedTokens(5);
      
      // Format performance data for chart
      const perfData = performanceResponse.data;
      const performanceChartData = {
        labels: perfData.dates,
        datasets: [
          {
            label: 'Portfolio Value (ADA)',
            data: perfData.values,
            fill: false,
            borderColor: 'rgb(26, 133, 255)',
            tension: 0.1
          }
        ]
      };
      
      // Format portfolio data for pie chart
      const portData = portfolioResponse.data;
      const portfolioChartData = {
        labels: Object.keys(portData.allocation),
        datasets: [
          {
            data: Object.values(portData.allocation).map(val => parseFloat(val)),
            backgroundColor: [
              'rgb(26, 133, 255)',
              'rgb(255, 99, 132)',
              'rgb(54, 162, 235)',
              'rgb(255, 206, 86)',
              'rgb(75, 192, 192)',
              'rgb(153, 102, 255)',
            ],
            borderWidth: 1,
          },
        ],
      };
      
      // Update dashboard data state
      setDashboardData({
        stats: summaryResponse.data,
        recentTrades: recentTradesResponse.data,
        recentTokens: analyzedTokensResponse.data,
        performanceData: performanceChartData,
        portfolioData: portfolioChartData
      });
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to fetch dashboard data. Please try again later.');
      setLoading(false);
    }
  };

  const handleWalletSetupComplete = () => {
    setWalletConnected(true);
    fetchDashboardData();
  };

  const handleRestartBot = async () => {
    try {
      await api.bot.restart();
      // Fetch updated bot status
      const response = await api.bot.getStatus();
      setBotStatus(response.data);
    } catch (err) {
      console.error('Error restarting bot:', err);
    }
  };

  // Color modes for theme
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.500', 'gray.400');

  // Show wallet setup if wallet is not connected
  if (!walletConnected) {
    return (
      <Layout>
        <Box maxW="1200px" mx="auto" px={4} py={8}>
          <Heading mb={8}>Welcome to .parasite Trading Bot</Heading>
          
          <Alert status="info" mb={8} borderRadius="md">
            <AlertIcon />
            <Box>
              <Text fontWeight="bold">Connect your wallet to get started</Text>
              <Text>
                This trading bot allows you to automatically trade Cardano tokens based on AI analysis.
                To begin, please connect your wallet below.
              </Text>
            </Box>
          </Alert>
          
          <WalletSetup onSetupComplete={handleWalletSetupComplete} />
          
          <Box mt={10}>
            <Heading size="md" mb={4}>What to expect after connecting</Heading>
            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
              <Feature
                title="Real-time Analysis"
                description="The bot analyzes top 25 tokens by volume for trading opportunities"
                icon="📊"
              />
              <Feature
                title="Automated Trading"
                description="Execute trades automatically based on AI analysis and your portfolio settings"
                icon="🤖"
              />
              <Feature
                title="Portfolio Management"
                description="Optimize your portfolio with category-based allocation strategies"
                icon="💼"
              />
            </SimpleGrid>
          </Box>
        </Box>
      </Layout>
    );
  }

  // Show loading state while fetching data
  if (loading) {
    return (
      <Layout>
        <Flex justify="center" align="center" height="50vh" direction="column">
          <Spinner size="xl" mb={4} />
          <Text>Loading dashboard data...</Text>
        </Flex>
      </Layout>
    );
  }

  // Show error state if data fetch failed
  if (error) {
    return (
      <Layout>
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          <Box>
            <Heading size="md">Error loading dashboard</Heading>
            <Text mt={2}>{error}</Text>
            <Button mt={4} onClick={fetchDashboardData}>Try Again</Button>
          </Box>
        </Alert>
      </Layout>
    );
  }

  return (
    <Layout>
      <Box maxW="1200px" mx="auto" px={4} py={8}>
        <Flex justify="space-between" align="center" mb={8}>
          <Heading>Dashboard</Heading>
          <HStack>
            <Badge 
              colorScheme={botStatus.running ? 'green' : 'red'}
              fontSize="sm"
              p={2}
              borderRadius="md"
            >
              Bot: {botStatus.running ? 'Active' : 'Stopped'}
            </Badge>
            <Button 
              size="sm" 
              colorScheme={botStatus.running ? 'red' : 'green'}
              onClick={handleRestartBot}
            >
              {botStatus.running ? 'Restart Bot' : 'Start Bot'}
            </Button>
          </HStack>
        </Flex>
        
        {/* Wallet Status */}
        <Box 
          p={4} 
          bg={cardBg} 
          shadow="md" 
          borderRadius="lg" 
          borderWidth="1px" 
          borderColor={borderColor}
          mb={8}
        >
          <Heading size="md" mb={4}>Wallet Status</Heading>
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
            <Stat>
              <StatLabel>Wallet Address</StatLabel>
              <StatNumber fontSize="md">{walletStatus?.address ? `${walletStatus.address.substring(0, 8)}...${walletStatus.address.substring(walletStatus.address.length - 8)}` : 'N/A'}</StatNumber>
              <StatHelpText>Connected</StatHelpText>
            </Stat>
            <Stat>
              <StatLabel>ADA Balance</StatLabel>
              <StatNumber>{walletStatus?.balance?.ada.toFixed(2) || '0.00'} ₳</StatNumber>
              <StatHelpText>Available for trading</StatHelpText>
            </Stat>
            <Stat>
              <StatLabel>Token Count</StatLabel>
              <StatNumber>{walletStatus?.tokens?.length || 0}</StatNumber>
              <StatHelpText>Different tokens in wallet</StatHelpText>
            </Stat>
          </SimpleGrid>
        </Box>

        {/* Stats Overview */}
        <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6} mb={8}>
          <StatCard
            label="Portfolio Value"
            value={`${dashboardData.stats.totalValue.toFixed(2)} ₳`}
            change={dashboardData.stats.valueChange}
            trend={dashboardData.stats.valueChange >= 0 ? 'increase' : 'decrease'}
            period="24h"
          />
          <StatCard
            label="Total Trades"
            value={dashboardData.stats.totalTrades}
            change={dashboardData.stats.tradesChange}
            trend="increase"
            period="24h"
          />
          <StatCard
            label="Success Rate"
            value={`${dashboardData.stats.successRate.toFixed(1)}%`}
            change={dashboardData.stats.successRateChange}
            trend={dashboardData.stats.successRateChange >= 0 ? 'increase' : 'decrease'}
            period="7d avg"
          />
          <StatCard
            label="Profit/Loss"
            value={`${dashboardData.stats.profitLoss > 0 ? '+' : ''}${dashboardData.stats.profitLoss.toFixed(2)} ₳`}
            change={dashboardData.stats.profitLossChange}
            trend={dashboardData.stats.profitLoss >= 0 ? 'increase' : 'decrease'}
            period="7d"
          />
        </SimpleGrid>

        {/* Charts Row */}
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} mb={8}>
          <Box 
            p={4} 
            bg={cardBg} 
            shadow="md" 
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
            height="300px"
          >
            <Heading size="md" mb={4}>Portfolio Performance</Heading>
            <Box height="230px">
              <Line 
                data={dashboardData.performanceData} 
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: false
                    }
                  }
                }}
              />
            </Box>
          </Box>
          <Box 
            p={4} 
            bg={cardBg} 
            shadow="md" 
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
            height="300px"
          >
            <Heading size="md" mb={4}>Portfolio Allocation</Heading>
            <Box height="230px" display="flex" justifyContent="center">
              <Box width="230px">
                <Doughnut 
                  data={dashboardData.portfolioData} 
                  options={{
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                      legend: {
                        position: 'right',
                      }
                    }
                  }}
                />
              </Box>
            </Box>
          </Box>
        </SimpleGrid>

        {/* Recent Trades & Analyzed Tokens */}
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          <Box 
            p={4} 
            bg={cardBg} 
            shadow="md" 
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Heading size="md" mb={4}>Recent Trades</Heading>
            <TableContainer>
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>Token</Th>
                    <Th>Type</Th>
                    <Th isNumeric>Amount (₳)</Th>
                    <Th isNumeric>Status</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {dashboardData.recentTrades.length > 0 ? (
                    dashboardData.recentTrades.map((trade, index) => (
                      <Tr key={index}>
                        <Td>{trade.ticker}</Td>
                        <Td>
                          <Badge colorScheme={trade.direction === 'buy' ? 'green' : 'red'}>
                            {trade.direction.toUpperCase()}
                          </Badge>
                        </Td>
                        <Td isNumeric>{trade.amount.toFixed(2)}</Td>
                        <Td isNumeric>
                          <Badge colorScheme={
                            trade.status === 'completed' ? 'green' : 
                            trade.status === 'pending' ? 'yellow' : 
                            'red'
                          }>
                            {trade.status}
                          </Badge>
                        </Td>
                      </Tr>
                    ))
                  ) : (
                    <Tr>
                      <Td colSpan={4} textAlign="center">No recent trades</Td>
                    </Tr>
                  )}
                </Tbody>
              </Table>
            </TableContainer>
          </Box>
          <Box 
            p={4} 
            bg={cardBg} 
            shadow="md" 
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Heading size="md" mb={4}>Recently Analyzed Tokens</Heading>
            <TableContainer>
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>Token</Th>
                    <Th>Decision</Th>
                    <Th isNumeric>Confidence</Th>
                    <Th isNumeric>Price (₳)</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {dashboardData.recentTokens.length > 0 ? (
                    dashboardData.recentTokens.map((token, index) => (
                      <Tr key={index}>
                        <Td>{token.ticker}</Td>
                        <Td>
                          <Badge colorScheme={
                            token.recommendation === 'buy' ? 'green' : 
                            token.recommendation === 'sell' ? 'red' : 
                            'yellow'
                          }>
                            {token.recommendation.toUpperCase()}
                          </Badge>
                        </Td>
                        <Td isNumeric>{token.confidence}/10</Td>
                        <Td isNumeric>{token.price.toFixed(6)}</Td>
                      </Tr>
                    ))
                  ) : (
                    <Tr>
                      <Td colSpan={4} textAlign="center">No analyzed tokens yet</Td>
                    </Tr>
                  )}
                </Tbody>
              </Table>
            </TableContainer>
          </Box>
        </SimpleGrid>
      </Box>
    </Layout>
  );
}

// Helper Component: Feature Card
function Feature({ title, description, icon }) {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  return (
    <Box 
      p={5} 
      shadow="md" 
      borderWidth="1px" 
      borderRadius="lg" 
      bg={cardBg}
      borderColor={borderColor}
    >
      <Flex 
        w={16} 
        h={16} 
        align={'center'} 
        justify={'center'} 
        color={'white'} 
        rounded={'full'} 
        bg={'gray.100'} 
        mb={4}
        fontSize="2xl"
      >
        {icon}
      </Flex>
      <Heading fontSize="xl" mb={2}>{title}</Heading>
      <Text color={'gray.600'}>{description}</Text>
    </Box>
  );
}

// Helper Component: Stat Card
function StatCard({ label, value, change, trend, period }) {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  return (
    <Box 
      p={5} 
      shadow="md" 
      borderWidth="1px" 
      borderRadius="lg" 
      bg={cardBg}
      borderColor={borderColor}
    >
      <StatGroup>
        <Stat>
          <StatLabel>{label}</StatLabel>
          <StatNumber>{value}</StatNumber>
          <StatHelpText>
            {change !== undefined && (
              <>
                <StatArrow type={trend} />
                {change.toFixed(2)}% ({period})
              </>
            )}
          </StatHelpText>
        </Stat>
      </StatGroup>
    </Box>
  );
} 