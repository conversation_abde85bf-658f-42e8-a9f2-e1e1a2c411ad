import { useState } from 'react';
import {
  <PERSON>, <PERSON>ing, Tabs, <PERSON>b<PERSON>ist, TabPanels, Tab, TabPanel,
  useColorModeValue, Text, Flex, Badge, Icon
} from '@chakra-ui/react';
import { FiBarChart2, FiSettings, FiActivity, FiServer } from 'react-icons/fi';
import Layout from '../components/Layout';
import PerformanceDashboard from '../components/PerformanceDashboard';
import UserSettings from '../components/UserSettings';

const MCPHub = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const bgColor = useColorModeValue('white', 'gray.800');
  
  const handleTabChange = (index) => {
    setTabIndex(index);
  };
  
  return (
    <Layout>
      <Box maxW="1200px" mx="auto" px={4} py={8}>
        <Flex justify="space-between" align="center" mb={6}>
          <Box>
            <Heading size="lg">MCP Hub</Heading>
            <Text mt={1} color="gray.500">
              Manage your trading bot through the Model Context Protocol
            </Text>
          </Box>
          <Badge colorScheme="green" p={2} borderRadius="md">
            MCP Connected
          </Badge>
        </Flex>
        
        <Tabs 
          variant="soft-rounded" 
          colorScheme="blue" 
          index={tabIndex} 
          onChange={handleTabChange}
          mb={6}
        >
          <TabList>
            <Tab>
              <Icon as={FiBarChart2} mr={2} />
              Performance
            </Tab>
            <Tab>
              <Icon as={FiSettings} mr={2} />
              Settings
            </Tab>
            <Tab>
              <Icon as={FiActivity} mr={2} />
              Activity
            </Tab>
            <Tab>
              <Icon as={FiServer} mr={2} />
              MCP Status
            </Tab>
          </TabList>
          
          <TabPanels mt={4}>
            <TabPanel p={0}>
              <Box bg={bgColor} borderRadius="lg" boxShadow="md">
                <PerformanceDashboard />
              </Box>
            </TabPanel>
            
            <TabPanel p={0}>
              <Box bg={bgColor} borderRadius="lg" boxShadow="md">
                <UserSettings />
              </Box>
            </TabPanel>
            
            <TabPanel p={0}>
              <Box bg={bgColor} borderRadius="lg" boxShadow="md" p={6}>
                <Heading size="md" mb={4}>Activity Log</Heading>
                <Text>
                  This section will display recent activity and interactions with the MCP server.
                </Text>
                {/* Activity log component would go here */}
              </Box>
            </TabPanel>
            
            <TabPanel p={0}>
              <Box bg={bgColor} borderRadius="lg" boxShadow="md" p={6}>
                <Heading size="md" mb={4}>MCP Server Status</Heading>
                <Text>
                  This section will display the status of the MCP server and connection details.
                </Text>
                {/* MCP status component would go here */}
              </Box>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>
    </Layout>
  );
};

export default MCPHub;
