import { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  SimpleGrid,
  Button,
  Select,
  Spinner,
  Badge,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  useColorModeValue,
  Icon,
  Progress,
  Tooltip,
} from '@chakra-ui/react';
import { InfoIcon, CheckIcon, WarningIcon } from '@chakra-ui/icons';
import { BiTrendingUp, BiTrendingDown } from 'react-icons/bi';
import { Line, Bar } from 'react-chartjs-2';
import Layout from '../components/Layout';

// Mock data for token analysis
const mockTokens = [
  {
    id: 'wmtx',
    name: 'WMTX',
    fullName: 'WingRiders Metaverse Token',
    price: 0.2698,
    change24h: 2.93,
    change7d: -8.32,
    volume24h: 12475.89,
    marketCap: 2698000,
    buySignals: 7,
    sellSignals: 3,
    technicalScore: 68,
    sentimentScore: 72,
    liquidityScore: 65,
    overallScore: 7.2,
    recommendation: 'buy',
    riskLevel: 'medium',
    indicators: {
      rsi: { value: 62, interpretation: 'neutral' },
      macd: { value: 0.0012, interpretation: 'bullish' },
      volume: { value: 1.24, interpretation: 'bullish' },
      support: { value: 0.25, interpretation: 'strong' },
      resistance: { value: 0.285, interpretation: 'medium' },
    },
    priceHistory: [0.25, 0.252, 0.249, 0.255, 0.264, 0.26, 0.262, 0.269, 0.2698],
    volumeHistory: [9500, 10200, 8900, 11500, 14200, 11800, 12000, 13500, 12475],
  },
  {
    id: 'snek',
    name: 'SNEK',
    fullName: 'Snek Cardano Token',
    price: 0.00446,
    change24h: -4.44,
    change7d: 12.51,
    volume24h: 32547.65,
    marketCap: 446000,
    buySignals: 3,
    sellSignals: 8,
    technicalScore: 42,
    sentimentScore: 85,
    liquidityScore: 78,
    overallScore: 5.8,
    recommendation: 'sell',
    riskLevel: 'high',
    indicators: {
      rsi: { value: 72, interpretation: 'overbought' },
      macd: { value: -0.0008, interpretation: 'bearish' },
      volume: { value: 0.85, interpretation: 'bearish' },
      support: { value: 0.0042, interpretation: 'weak' },
      resistance: { value: 0.0048, interpretation: 'strong' },
    },
    priceHistory: [0.0048, 0.00475, 0.00482, 0.00465, 0.0046, 0.00455, 0.0045, 0.00452, 0.00446],
    volumeHistory: [28000, 29500, 31200, 30800, 32000, 33500, 32800, 33000, 32547],
  },
  {
    id: 'iag',
    name: 'IAG',
    fullName: 'Indigo Protocol Token',
    price: 0.367,
    change24h: 3.6,
    change7d: 5.12,
    volume24h: 8932.41,
    marketCap: 3670000,
    buySignals: 6,
    sellSignals: 4,
    technicalScore: 58,
    sentimentScore: 62,
    liquidityScore: 70,
    overallScore: 6.4,
    recommendation: 'hold',
    riskLevel: 'medium',
    indicators: {
      rsi: { value: 56, interpretation: 'neutral' },
      macd: { value: 0.0003, interpretation: 'neutral' },
      volume: { value: 1.05, interpretation: 'neutral' },
      support: { value: 0.355, interpretation: 'medium' },
      resistance: { value: 0.385, interpretation: 'medium' },
    },
    priceHistory: [0.352, 0.355, 0.358, 0.36, 0.362, 0.359, 0.363, 0.365, 0.367],
    volumeHistory: [7800, 8200, 8600, 8900, 9100, 8800, 8700, 9000, 8932],
  }
];

// Time periods for data visualization
const timePeriods = [
  { label: '24h', value: '24h' },
  { label: '7d', value: '7d' },
  { label: '30d', value: '30d' },
  { label: '90d', value: '90d' },
];

export default function Analysis() {
  const [loading, setLoading] = useState(false);
  const [selectedToken, setSelectedToken] = useState(mockTokens[0]);
  const [timePeriod, setTimePeriod] = useState('7d');
  const [tokens, setTokens] = useState(mockTokens);

  // In a real app, you would fetch data based on selectedToken and timePeriod
  useEffect(() => {
    setLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      setLoading(false);
    }, 500);
  }, [selectedToken, timePeriod]);

  const handleTokenChange = (tokenId) => {
    setLoading(true);
    const token = tokens.find(t => t.id === tokenId);
    setSelectedToken(token);
  };

  const handleTimePeriodChange = (period) => {
    setTimePeriod(period);
  };

  // Chart data
  const priceChartData = {
    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7', 'Day 8', 'Day 9'],
    datasets: [
      {
        label: `${selectedToken.name} Price (ADA)`,
        data: selectedToken.priceHistory,
        fill: false,
        borderColor: selectedToken.change24h >= 0 ? 'rgb(56, 178, 172)' : 'rgb(245, 101, 101)',
        tension: 0.1
      }
    ]
  };

  const volumeChartData = {
    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7', 'Day 8', 'Day 9'],
    datasets: [
      {
        label: `${selectedToken.name} Volume (ADA)`,
        data: selectedToken.volumeHistory,
        backgroundColor: 'rgba(90, 120, 255, 0.5)',
        borderColor: 'rgb(90, 120, 255)',
        borderWidth: 1
      }
    ]
  };

  const signalChartData = {
    labels: ['Technical', 'Sentiment', 'Liquidity'],
    datasets: [
      {
        label: 'Score',
        data: [
          selectedToken.technicalScore,
          selectedToken.sentimentScore,
          selectedToken.liquidityScore
        ],
        backgroundColor: [
          'rgba(49, 130, 206, 0.6)',
          'rgba(56, 178, 172, 0.6)',
          'rgba(214, 158, 46, 0.6)'
        ],
        borderColor: [
          'rgb(49, 130, 206)',
          'rgb(56, 178, 172)',
          'rgb(214, 158, 46)'
        ],
        borderWidth: 1
      }
    ]
  };

  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.800', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const getRecommendationColor = (rec) => {
    switch(rec) {
      case 'buy': return 'green';
      case 'sell': return 'red';
      default: return 'orange';
    }
  };

  const getRiskColor = (risk) => {
    switch(risk) {
      case 'low': return 'green';
      case 'medium': return 'orange';
      case 'high': return 'red';
      default: return 'gray';
    }
  };

  const getIndicatorColor = (interpretation) => {
    switch(interpretation) {
      case 'bullish': return 'green.500';
      case 'bearish': return 'red.500';
      case 'overbought': return 'red.400';
      case 'oversold': return 'green.400';
      case 'strong': return 'green.400';
      case 'weak': return 'red.400';
      default: return 'gray.500';
    }
  };

  return (
    <Layout>
      <Heading as="h1" size="xl" mb={6}>
        Token Analysis
      </Heading>

      {/* Token Selector and Time Period */}
      <Flex 
        direction={{ base: 'column', md: 'row' }} 
        mb={6} 
        gap={4}
        align={{ base: 'stretch', md: 'center' }}
      >
        <Box flex="2">
          <Flex gap={2}>
            {tokens.map(token => (
              <Button
                key={token.id}
                size="md"
                variant={selectedToken.id === token.id ? "solid" : "outline"}
                colorScheme={selectedToken.id === token.id ? "blue" : "gray"}
                onClick={() => handleTokenChange(token.id)}
              >
                {token.name}
              </Button>
            ))}
          </Flex>
        </Box>
        <Box flex="1">
          <Flex justify={{ base: 'flex-start', md: 'flex-end' }} gap={2}>
            {timePeriods.map(period => (
              <Button
                key={period.value}
                size="sm"
                variant={timePeriod === period.value ? "solid" : "outline"}
                colorScheme={timePeriod === period.value ? "blue" : "gray"}
                onClick={() => handleTimePeriodChange(period.value)}
              >
                {period.label}
              </Button>
            ))}
          </Flex>
        </Box>
      </Flex>

      {loading ? (
        <Flex justify="center" align="center" h="300px">
          <Spinner size="xl" />
        </Flex>
      ) : (
        <>
          {/* Overview Stats */}
          <SimpleGrid columns={{ base: 1, sm: 2, md: 4 }} spacing={6} mb={6}>
            <Stat bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
              <StatLabel fontSize="sm">Price (ADA)</StatLabel>
              <StatNumber>{selectedToken.price}</StatNumber>
              <StatHelpText>
                <StatArrow type={selectedToken.change24h >= 0 ? 'increase' : 'decrease'} />
                {Math.abs(selectedToken.change24h)}% (24h)
              </StatHelpText>
            </Stat>
            <Stat bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
              <StatLabel fontSize="sm">Volume (24h)</StatLabel>
              <StatNumber>{selectedToken.volume24h.toLocaleString()} ADA</StatNumber>
              <StatHelpText>
                Market Cap: {(selectedToken.marketCap / 1000).toFixed(0)}K ADA
              </StatHelpText>
            </Stat>
            <Stat bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
              <StatLabel fontSize="sm">Recommendation</StatLabel>
              <Flex align="center" mt={2}>
                <Badge 
                  colorScheme={getRecommendationColor(selectedToken.recommendation)}
                  fontSize="md"
                  p={1}
                  borderRadius="md"
                >
                  {selectedToken.recommendation.toUpperCase()}
                </Badge>
                <Text ml={2} fontSize="lg" fontWeight="bold">
                  {selectedToken.overallScore}/10
                </Text>
              </Flex>
              <StatHelpText>
                Confidence Score
              </StatHelpText>
            </Stat>
            <Stat bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
              <StatLabel fontSize="sm">Risk Level</StatLabel>
              <Flex align="center" mt={2}>
                <Badge 
                  colorScheme={getRiskColor(selectedToken.riskLevel)}
                  fontSize="md"
                  p={1}
                  borderRadius="md"
                >
                  {selectedToken.riskLevel.toUpperCase()}
                </Badge>
              </Flex>
              <StatHelpText>
                Signals: {selectedToken.buySignals} buy / {selectedToken.sellSignals} sell
              </StatHelpText>
            </Stat>
          </SimpleGrid>
          
          {/* Charts & Analysis Tabs */}
          <Tabs colorScheme="blue" mb={6}>
            <TabList>
              <Tab>Price & Volume</Tab>
              <Tab>Technical Analysis</Tab>
              <Tab>Indicators</Tab>
            </TabList>
            
            <TabPanels>
              <TabPanel p={0} pt={4}>
                <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                  {/* Price Chart */}
                  <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
                    <Heading as="h3" size="md" mb={4}>
                      Price History
                    </Heading>
                    <Box h="300px">
                      <Line 
                        data={priceChartData} 
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          scales: {
                            y: {
                              beginAtZero: false
                            }
                          }
                        }} 
                      />
                    </Box>
                  </Box>
                  
                  {/* Volume Chart */}
                  <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
                    <Heading as="h3" size="md" mb={4}>
                      Volume History
                    </Heading>
                    <Box h="300px">
                      <Bar 
                        data={volumeChartData} 
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                        }} 
                      />
                    </Box>
                  </Box>
                </SimpleGrid>
              </TabPanel>
              
              <TabPanel p={0} pt={4}>
                <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                  {/* Analysis Scores */}
                  <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
                    <Heading as="h3" size="md" mb={4}>
                      Analysis Scores
                    </Heading>
                    <Box h="300px">
                      <Bar 
                        data={signalChartData} 
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          scales: {
                            y: {
                              max: 100,
                              beginAtZero: true
                            }
                          }
                        }} 
                      />
                    </Box>
                  </Box>
                  
                  {/* Score Breakdown */}
                  <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
                    <Heading as="h3" size="md" mb={4}>
                      Score Breakdown
                    </Heading>
                    <Flex direction="column" gap={4}>
                      <Box>
                        <Flex justify="space-between" mb={1}>
                          <Text fontWeight="medium">Technical Analysis</Text>
                          <Text>{selectedToken.technicalScore}/100</Text>
                        </Flex>
                        <Progress 
                          value={selectedToken.technicalScore} 
                          colorScheme={selectedToken.technicalScore > 60 ? 'green' : selectedToken.technicalScore > 40 ? 'yellow' : 'red'} 
                          borderRadius="md" 
                          size="sm" 
                        />
                        <Text fontSize="sm" color="gray.500" mt={1}>
                          Based on price patterns, momentum, and volatility
                        </Text>
                      </Box>
                      
                      <Box>
                        <Flex justify="space-between" mb={1}>
                          <Text fontWeight="medium">Social Sentiment</Text>
                          <Text>{selectedToken.sentimentScore}/100</Text>
                        </Flex>
                        <Progress 
                          value={selectedToken.sentimentScore} 
                          colorScheme={selectedToken.sentimentScore > 60 ? 'green' : selectedToken.sentimentScore > 40 ? 'yellow' : 'red'} 
                          borderRadius="md" 
                          size="sm" 
                        />
                        <Text fontSize="sm" color="gray.500" mt={1}>
                          Based on social media activity, news, and community engagement
                        </Text>
                      </Box>
                      
                      <Box>
                        <Flex justify="space-between" mb={1}>
                          <Text fontWeight="medium">Liquidity</Text>
                          <Text>{selectedToken.liquidityScore}/100</Text>
                        </Flex>
                        <Progress 
                          value={selectedToken.liquidityScore} 
                          colorScheme={selectedToken.liquidityScore > 60 ? 'green' : selectedToken.liquidityScore > 40 ? 'yellow' : 'red'} 
                          borderRadius="md" 
                          size="sm" 
                        />
                        <Text fontSize="sm" color="gray.500" mt={1}>
                          Based on trading volume, market depth, and slippage
                        </Text>
                      </Box>
                      
                      <Box mt={2}>
                        <Flex align="center" gap={2}>
                          <Badge colorScheme={getRecommendationColor(selectedToken.recommendation)} p={1} fontSize="md">
                            {selectedToken.recommendation.toUpperCase()}
                          </Badge>
                          <Text>Overall Confidence: <strong>{selectedToken.overallScore}/10</strong></Text>
                        </Flex>
                      </Box>
                    </Flex>
                  </Box>
                </SimpleGrid>
              </TabPanel>
              
              <TabPanel p={0} pt={4}>
                <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
                  <Heading as="h3" size="md" mb={4}>
                    Technical Indicators
                  </Heading>
                  <TableContainer>
                    <Table variant="simple">
                      <Thead>
                        <Tr>
                          <Th>Indicator</Th>
                          <Th>Value</Th>
                          <Th>Interpretation</Th>
                          <Th>Signal</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        <Tr>
                          <Td fontWeight="semibold">RSI (14)</Td>
                          <Td>{selectedToken.indicators.rsi.value}</Td>
                          <Td color={getIndicatorColor(selectedToken.indicators.rsi.interpretation)}>
                            {selectedToken.indicators.rsi.interpretation}
                          </Td>
                          <Td>
                            <Tooltip label={selectedToken.indicators.rsi.interpretation === 'overbought' ? 'Potentially overvalued' : selectedToken.indicators.rsi.interpretation === 'oversold' ? 'Potentially undervalued' : 'Neither overbought nor oversold'}>
                              <Icon 
                                as={selectedToken.indicators.rsi.interpretation === 'overbought' ? BiTrendingDown : selectedToken.indicators.rsi.interpretation === 'oversold' ? BiTrendingUp : InfoIcon} 
                                color={getIndicatorColor(selectedToken.indicators.rsi.interpretation)}
                              />
                            </Tooltip>
                          </Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="semibold">MACD</Td>
                          <Td>{selectedToken.indicators.macd.value}</Td>
                          <Td color={getIndicatorColor(selectedToken.indicators.macd.interpretation)}>
                            {selectedToken.indicators.macd.interpretation}
                          </Td>
                          <Td>
                            <Tooltip label={selectedToken.indicators.macd.interpretation === 'bullish' ? 'Upward momentum' : selectedToken.indicators.macd.interpretation === 'bearish' ? 'Downward momentum' : 'No strong momentum'}>
                              <Icon 
                                as={selectedToken.indicators.macd.interpretation === 'bullish' ? BiTrendingUp : selectedToken.indicators.macd.interpretation === 'bearish' ? BiTrendingDown : InfoIcon} 
                                color={getIndicatorColor(selectedToken.indicators.macd.interpretation)}
                              />
                            </Tooltip>
                          </Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="semibold">Volume Trend</Td>
                          <Td>{selectedToken.indicators.volume.value}x avg</Td>
                          <Td color={getIndicatorColor(selectedToken.indicators.volume.interpretation)}>
                            {selectedToken.indicators.volume.interpretation}
                          </Td>
                          <Td>
                            <Tooltip label={selectedToken.indicators.volume.interpretation === 'bullish' ? 'Strong buying interest' : selectedToken.indicators.volume.interpretation === 'bearish' ? 'Strong selling pressure' : 'Average trading activity'}>
                              <Icon 
                                as={selectedToken.indicators.volume.interpretation === 'bullish' ? BiTrendingUp : selectedToken.indicators.volume.interpretation === 'bearish' ? BiTrendingDown : InfoIcon} 
                                color={getIndicatorColor(selectedToken.indicators.volume.interpretation)}
                              />
                            </Tooltip>
                          </Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="semibold">Support Level</Td>
                          <Td>{selectedToken.indicators.support.value} ADA</Td>
                          <Td color={getIndicatorColor(selectedToken.indicators.support.interpretation)}>
                            {selectedToken.indicators.support.interpretation}
                          </Td>
                          <Td>
                            <Tooltip label={`${selectedToken.indicators.support.interpretation} support at ${selectedToken.indicators.support.value} ADA`}>
                              <Icon 
                                as={selectedToken.indicators.support.interpretation === 'strong' ? CheckIcon : selectedToken.indicators.support.interpretation === 'weak' ? WarningIcon : InfoIcon} 
                                color={getIndicatorColor(selectedToken.indicators.support.interpretation)}
                              />
                            </Tooltip>
                          </Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="semibold">Resistance Level</Td>
                          <Td>{selectedToken.indicators.resistance.value} ADA</Td>
                          <Td color={getIndicatorColor(selectedToken.indicators.resistance.interpretation)}>
                            {selectedToken.indicators.resistance.interpretation}
                          </Td>
                          <Td>
                            <Tooltip label={`${selectedToken.indicators.resistance.interpretation} resistance at ${selectedToken.indicators.resistance.value} ADA`}>
                              <Icon 
                                as={selectedToken.indicators.resistance.interpretation === 'strong' ? WarningIcon : selectedToken.indicators.resistance.interpretation === 'weak' ? CheckIcon : InfoIcon} 
                                color={getIndicatorColor(selectedToken.indicators.resistance.interpretation)}
                              />
                            </Tooltip>
                          </Td>
                        </Tr>
                      </Tbody>
                    </Table>
                  </TableContainer>
                </Box>
              </TabPanel>
            </TabPanels>
          </Tabs>
          
          {/* Summary Box */}
          <Box bg={bgColor} p={4} borderRadius="lg" boxShadow="sm">
            <Heading as="h3" size="md" mb={3}>
              Analysis Summary
            </Heading>
            <Text>
              <strong>{selectedToken.name} ({selectedToken.fullName})</strong> is currently showing 
              {' '}
              <Text as="span" fontWeight="bold" color={selectedToken.buySignals > selectedToken.sellSignals ? 'green.500' : 'red.500'}>
                {selectedToken.buySignals > selectedToken.sellSignals ? 'positive' : 'negative'} signals
              </Text>
              {' '}with {selectedToken.buySignals} buy indicators and {selectedToken.sellSignals} sell indicators. 
              The token has a {selectedToken.change24h >= 0 ? 'positive' : 'negative'} 24-hour change of {Math.abs(selectedToken.change24h)}% and 
              a {selectedToken.change7d >= 0 ? 'positive' : 'negative'} 7-day change of {Math.abs(selectedToken.change7d)}%.
            </Text>
            <Text mt={2}>
              Technical analysis suggests a {selectedToken.recommendation.toUpperCase()} recommendation with a confidence score of {selectedToken.overallScore}/10. 
              The token currently has a {selectedToken.riskLevel} risk assessment based on volatility and market indicators.
            </Text>
            <Text mt={2}>
              Key support level at {selectedToken.indicators.support.value} ADA and resistance at {selectedToken.indicators.resistance.value} ADA 
              should be monitored for potential breakouts or reversals.
            </Text>
          </Box>
        </>
      )}
    </Layout>
  );
} 