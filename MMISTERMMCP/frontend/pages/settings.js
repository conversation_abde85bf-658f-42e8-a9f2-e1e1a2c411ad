import { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Button,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Select,
  Switch,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  VStack,
  HStack,
  SimpleGrid,
  Divider,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Flex,
  Alert,
  AlertIcon,
  useToast,
  Badge,
  useColorModeValue,
  InputGroup,
  InputRightElement,
  Code,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Checkbox,
  IconButton,
  Link,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { 
  AddIcon, 
  DeleteIcon, 
  CheckIcon, 
  WarningIcon, 
  InfoIcon, 
  ViewIcon, 
  ViewOffIcon 
} from '@chakra-ui/icons';
import Layout from '../components/Layout';
import { Doughnut } from 'react-chartjs-2';

// Mock settings data
const mockSettings = {
  general: {
    botEnabled: true,
    tradingMode: 'automatic',
    baseAsset: 'ADA',
    maxTokensToTrack: 20,
    priceDataSource: 'muesliswap',
    updateInterval: 30,
    riskLevel: 'medium'
  },
  trading: {
    maxSlippage: 2.5,
    maxOrderSize: 100,
    minOrderSize: 25,
    tradeDelay: 10,
    useTechnicalAnalysis: true,
    useAiPrediction: true,
    autoSellCriteria: {
      profitTarget: 15,
      stopLoss: 10,
      timeLimit: 48
    },
    enableStablecoinFilter: true
  },
  api: {
    dexter: {
      enabled: true,
      apiKey: 'dex_284c8e9af56a1b349860c7d39c8b6d23',
      apiSecret: '****************************************',
      baseUrl: 'https://api.dexterlab.com/v1'
    },
    discordWebhook: {
      enabled: false,
      url: '',
      notifyOnTrade: true,
      notifyOnAnalysis: false
    }
  },
  wallets: [
    { id: 1, name: 'Main Wallet', address: 'addr1q8g5p...3md2qpw4x', balance: 1253.45, active: true },
    { id: 2, name: 'Test Wallet', address: 'addr1v9j7c...q2p6mls9f', balance: 250.0, active: false }
  ],
  tokens: {
    blacklist: ['HOSKY', 'SHIT', 'SCAM'],
    whitelist: ['SNEK', 'WMTX', 'IAG', 'HUNT', 'NTX', 'LQ', 'MIN']
  }
};

export default function Settings() {
  const [settings, setSettings] = useState({
    general: {
      botEnabled: true,
      autoTradeEnabled: true,
      maxTradesPerDay: 5,
      maxTradeSize: 100,
    },
    wallets: [
      // Will be populated from API
    ],
    portfolioAllocations: {
      ada: 30,
      meme: 15,
      defi: 20,
      major: 15,
      shard: 10,
      new: 10
    },
    tradingLimits: {
      minConfidence: 7,
      minTokenAge: 7,
      minLiquidity: 5000,
      maxImpact: 5,
    },
    apiKeys: {
      taptools: '',
      openai: '',
      blockfrost: '',
    },
    discord: {
      webhookUrl: '',
      notifications: {
        trades: true,
        analysis: true,
        errors: true,
      }
    },
  });
  
  const [activeWallet, setActiveWallet] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [saveStatus, setSaveStatus] = useState(null);
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [walletLoading, setWalletLoading] = useState(false);
  const [walletStatus, setWalletStatus] = useState(null);
  
  const {
    isOpen: isAddWalletOpen,
    onOpen: onAddWalletOpen,
    onClose: onAddWalletClose
  } = useDisclosure();
  
  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  // Load settings from API
  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        // Fetch general settings
        const settingsResponse = await api.settings.getAll();
        
        // Fetch wallets
        const walletsResponse = await api.settings.getWallets();
        
        // Fetch portfolio allocations
        const allocationsResponse = await api.settings.getPortfolioAllocations();
        
        // Check wallet status
        const walletStatusResponse = await api.wallet.getStatus();
        setWalletStatus(walletStatusResponse.data);
        
        // Update active wallet if one is connected
        if (walletStatusResponse.data.connected && walletStatusResponse.data.address) {
          const connectedWallet = walletsResponse.data.find(
            wallet => wallet.address === walletStatusResponse.data.address
          );
          if (connectedWallet) {
            setActiveWallet(connectedWallet.id);
          }
        }
        
        // Merge all settings
        const mergedSettings = {
          ...settingsResponse.data,
          wallets: walletsResponse.data,
          portfolioAllocations: allocationsResponse.data,
        };
        
        setSettings(mergedSettings);
      } catch (error) {
        console.error('Error loading settings:', error);
        toast({
          title: 'Error loading settings',
          description: 'Could not load your settings. Please try again later.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSettings();
  }, [toast]);

  const handleSaveSettings = async () => {
    setSaveStatus('saving');
    try {
      // Update general settings
      await api.settings.update({
        general: settings.general,
        tradingLimits: settings.tradingLimits,
        apiKeys: settings.apiKeys,
        discord: settings.discord,
      });
      
      // Update portfolio allocations
      await api.settings.updatePortfolioAllocations(settings.portfolioAllocations);
      
      setSaveStatus('success');
      toast({
        title: 'Settings saved',
        description: 'Your settings have been successfully updated.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      // Reset status after a delay
      setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setSaveStatus('error');
      toast({
        title: 'Error saving settings',
        description: 'Could not save your settings. Please try again later.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (section, field, value) => {
    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [field]: value
      }
    });
  };
  
  const handleNestedInputChange = (section, nestedSection, field, value) => {
    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [nestedSection]: {
          ...settings[section][nestedSection],
          [field]: value
        }
      }
    });
  };

  const handleAllocationChange = (category, value) => {
    const newValue = Math.min(Math.max(0, value), 100); // Ensure value is between 0 and 100
    setSettings({
      ...settings,
      portfolioAllocations: {
        ...settings.portfolioAllocations,
        [category]: newValue
      }
    });
  };

  const addWallet = async (walletData) => {
    setWalletLoading(true);
    try {
      // Add wallet to settings
      const response = await api.settings.addWallet(walletData);
      
      // Update wallets list
      setSettings({
        ...settings,
        wallets: [...settings.wallets, response.data]
      });
      
      toast({
        title: 'Wallet added',
        description: 'Your wallet has been successfully added.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      onAddWalletClose();
    } catch (error) {
      console.error('Error adding wallet:', error);
      toast({
        title: 'Error adding wallet',
        description: 'Could not add your wallet. Please try again later.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setWalletLoading(false);
    }
  };

  const removeWallet = async (id) => {
    try {
      await api.settings.removeWallet(id);
      
      // Update wallets list
      setSettings({
        ...settings,
        wallets: settings.wallets.filter(wallet => wallet.id !== id)
      });
      
      // If removed wallet was active, reset active wallet
      if (activeWallet === id) {
        setActiveWallet(null);
      }
      
      toast({
        title: 'Wallet removed',
        description: 'Your wallet has been successfully removed.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error removing wallet:', error);
      toast({
        title: 'Error removing wallet',
        description: 'Could not remove your wallet. Please try again later.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const activateWallet = async (id) => {
    setWalletLoading(true);
    try {
      const wallet = settings.wallets.find(w => w.id === id);
      if (!wallet) {
        throw new Error('Wallet not found');
      }
      
      // Set the wallet in the bot
      await api.wallet.setWallet({
        address: wallet.address,
        seedPhrase: wallet.seedPhrase,
      });
      
      setActiveWallet(id);
      
      toast({
        title: 'Wallet activated',
        description: 'Your wallet has been successfully activated for trading.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      // Refresh wallet status
      const walletStatusResponse = await api.wallet.getStatus();
      setWalletStatus(walletStatusResponse.data);
    } catch (error) {
      console.error('Error activating wallet:', error);
      toast({
        title: 'Error activating wallet',
        description: 'Could not activate your wallet. Please try again later.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setWalletLoading(false);
    }
  };

  const toggleShowApiKeys = () => {
    setShowApiKeys(!showApiKeys);
  };
  
  if (isLoading) {
    return (
      <Layout>
        <Flex justify="center" align="center" height="50vh" direction="column">
          <Spinner size="xl" mb={4} />
          <Text>Loading settings...</Text>
        </Flex>
      </Layout>
    );
  }

  return (
    <Layout>
      <Box maxW="1200px" mx="auto" px={4} py={8}>
        <Flex justify="space-between" align="center" mb={8}>
          <Heading>Settings</Heading>
          <Button
            colorScheme={saveStatus === 'error' ? 'red' : 'blue'}
            onClick={handleSaveSettings}
            isLoading={saveStatus === 'saving'}
            leftIcon={saveStatus === 'success' ? <CheckIcon /> : undefined}
          >
            {saveStatus === 'success' ? 'Saved' : 'Save Settings'}
          </Button>
        </Flex>
        
        <Tabs variant="enclosed" isFitted isLazy>
          <TabList mb="1em">
            <Tab>General</Tab>
            <Tab>Wallets</Tab>
            <Tab>Portfolio</Tab>
            <Tab>Trading Limits</Tab>
            <Tab>API Keys</Tab>
            <Tab>Notifications</Tab>
          </TabList>
          
          <TabPanels>
            <TabPanel>
              <Box
                p={5}
                shadow="md"
                borderWidth="1px"
                borderRadius="lg"
                bg={bgColor}
                borderColor={borderColor}
              >
                <Heading size="md" mb={4}>General Settings</Heading>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="bot-enabled" mb="0">
                      Enable Trading Bot
                    </FormLabel>
                    <Switch
                      id="bot-enabled"
                      isChecked={settings.general.botEnabled}
                      onChange={(e) => handleInputChange('general', 'botEnabled', e.target.checked)}
                      colorScheme="blue"
                    />
                  </FormControl>
                  
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="auto-trade" mb="0">
                      Enable Automatic Trading
                    </FormLabel>
                    <Switch
                      id="auto-trade"
                      isChecked={settings.general.autoTradeEnabled}
                      onChange={(e) => handleInputChange('general', 'autoTradeEnabled', e.target.checked)}
                      colorScheme="blue"
                    />
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel htmlFor="max-trades">Maximum Trades Per Day</FormLabel>
                    <Slider
                      id="max-trades"
                      min={1}
                      max={20}
                      step={1}
                      value={settings.general.maxTradesPerDay}
                      onChange={(val) => handleInputChange('general', 'maxTradesPerDay', val)}
                      colorScheme="blue"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Current: {settings.general.maxTradesPerDay} trades</FormHelperText>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel htmlFor="max-trade-size">Maximum Trade Size (ADA)</FormLabel>
                    <Slider
                      id="max-trade-size"
                      min={10}
                      max={500}
                      step={10}
                      value={settings.general.maxTradeSize}
                      onChange={(val) => handleInputChange('general', 'maxTradeSize', val)}
                      colorScheme="blue"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Current: {settings.general.maxTradeSize} ADA</FormHelperText>
                  </FormControl>
                </SimpleGrid>
              </Box>
            </TabPanel>
            
            {/* Wallets Tab */}
            <TabPanel>
              <Box
                p={5}
                shadow="md"
                borderWidth="1px"
                borderRadius="lg"
                bg={bgColor}
                borderColor={borderColor}
                mb={6}
              >
                <Flex justify="space-between" align="center" mb={4}>
                  <Heading size="md">Wallet Management</Heading>
                  <Button 
                    colorScheme="blue" 
                    leftIcon={<AddIcon />} 
                    onClick={onAddWalletOpen}
                  >
                    Add Wallet
                  </Button>
                </Flex>
                
                {walletStatus && (
                  <Alert 
                    status={walletStatus.connected ? "success" : "info"} 
                    mb={4}
                    borderRadius="md"
                  >
                    <AlertIcon />
                    {walletStatus.connected ? (
                      <Box>
                        <AlertTitle>Wallet Connected</AlertTitle>
                        <AlertDescription>
                          The bot is currently using wallet address: {walletStatus.address.substring(0, 8)}...{walletStatus.address.substring(walletStatus.address.length - 8)}
                        </AlertDescription>
                      </Box>
                    ) : (
                      <Box>
                        <AlertTitle>No Wallet Connected</AlertTitle>
                        <AlertDescription>
                          Please connect a wallet to enable trading functionality.
                        </AlertDescription>
                      </Box>
                    )}
                  </Alert>
                )}
                
                {settings.wallets.length > 0 ? (
                  <Table variant="simple">
                    <Thead>
                      <Tr>
                        <Th>Name</Th>
                        <Th>Address</Th>
                        <Th>Status</Th>
                        <Th>Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {settings.wallets.map((wallet) => (
                        <Tr key={wallet.id}>
                          <Td>{wallet.name}</Td>
                          <Td>
                            {wallet.address.substring(0, 8)}...{wallet.address.substring(wallet.address.length - 8)}
                          </Td>
                          <Td>
                            {activeWallet === wallet.id ? (
                              <Badge colorScheme="green">Active</Badge>
                            ) : (
                              <Badge colorScheme="gray">Inactive</Badge>
                            )}
                          </Td>
                          <Td>
                            <HStack spacing={2}>
                              {activeWallet !== wallet.id && (
                                <Button 
                                  size="sm" 
                                  colorScheme="blue"
                                  onClick={() => activateWallet(wallet.id)}
                                  isLoading={walletLoading}
                                >
                                  Activate
                                </Button>
                              )}
                              <IconButton
                                size="sm"
                                colorScheme="red"
                                icon={<DeleteIcon />}
                                onClick={() => removeWallet(wallet.id)}
                                aria-label="Remove wallet"
                              />
                            </HStack>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                ) : (
                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <Box>
                      <AlertTitle>No Wallets</AlertTitle>
                      <AlertDescription>
                        Add a wallet to start trading.
                      </AlertDescription>
                    </Box>
                  </Alert>
                )}
              </Box>
              
              <Box
                p={5}
                shadow="md"
                borderWidth="1px"
                borderRadius="lg"
                bg={bgColor}
                borderColor={borderColor}
              >
                <Heading size="md" mb={4}>Wallet Security</Heading>
                <Text mb={4}>
                  Your wallet seed phrases are stored securely in an encrypted format. The encryption keys are only accessible during the trading bot's runtime and are never stored in plain text.
                </Text>
                <Alert status="warning" borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <AlertTitle>Important Security Information</AlertTitle>
                    <AlertDescription>
                      While we take every precaution to secure your wallet information, please ensure you use a dedicated wallet for trading and limit the funds you make available to the bot.
                    </AlertDescription>
                  </Box>
                </Alert>
              </Box>
              
              {/* Add Wallet Modal */}
              <Modal isOpen={isAddWalletOpen} onClose={onAddWalletClose}>
                <ModalOverlay />
                <ModalContent>
                  <ModalHeader>Add New Wallet</ModalHeader>
                  <ModalCloseButton />
                  <ModalBody>
                    <WalletSetup 
                      onSetupComplete={(walletData) => {
                        addWallet(walletData);
                        onAddWalletClose();
                      }} 
                      isModal={true}
                    />
                  </ModalBody>
                </ModalContent>
              </Modal>
            </TabPanel>
            
            {/* Portfolio Tab */}
            <TabPanel>
              <Box
                p={5}
                shadow="md"
                borderWidth="1px"
                borderRadius="lg"
                bg={bgColor}
                borderColor={borderColor}
              >
                <Heading size="md" mb={4}>Portfolio Allocation</Heading>
                <Text mb={6}>
                  Set your target allocation percentages for each asset category. The bot will use these settings to balance your portfolio during trading.
                </Text>
                
                <Alert status="info" mb={6} borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <AlertTitle>How Allocations Work</AlertTitle>
                    <AlertDescription>
                      The bot will aim to maintain your portfolio according to these allocations. When deciding to buy a token, it will check if the category is under its target allocation.
                    </AlertDescription>
                  </Box>
                </Alert>
                
                <VStack spacing={6} align="stretch">
                  <FormControl>
                    <FormLabel htmlFor="ada-alloc">ADA (Native Token)</FormLabel>
                    <Slider
                      id="ada-alloc"
                      min={0}
                      max={100}
                      step={5}
                      value={settings.portfolioAllocations.ada}
                      onChange={(val) => handleAllocationChange('ada', val)}
                      colorScheme="blue"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Target: {settings.portfolioAllocations.ada}%</FormHelperText>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel htmlFor="meme-alloc">Meme Tokens</FormLabel>
                    <Slider
                      id="meme-alloc"
                      min={0}
                      max={100}
                      step={5}
                      value={settings.portfolioAllocations.meme}
                      onChange={(val) => handleAllocationChange('meme', val)}
                      colorScheme="pink"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Target: {settings.portfolioAllocations.meme}%</FormHelperText>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel htmlFor="defi-alloc">DeFi Tokens</FormLabel>
                    <Slider
                      id="defi-alloc"
                      min={0}
                      max={100}
                      step={5}
                      value={settings.portfolioAllocations.defi}
                      onChange={(val) => handleAllocationChange('defi', val)}
                      colorScheme="purple"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Target: {settings.portfolioAllocations.defi}%</FormHelperText>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel htmlFor="major-alloc">Major/Established Tokens</FormLabel>
                    <Slider
                      id="major-alloc"
                      min={0}
                      max={100}
                      step={5}
                      value={settings.portfolioAllocations.major}
                      onChange={(val) => handleAllocationChange('major', val)}
                      colorScheme="yellow"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Target: {settings.portfolioAllocations.major}%</FormHelperText>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel htmlFor="shard-alloc">Shard Tokens</FormLabel>
                    <Slider
                      id="shard-alloc"
                      min={0}
                      max={100}
                      step={5}
                      value={settings.portfolioAllocations.shard}
                      onChange={(val) => handleAllocationChange('shard', val)}
                      colorScheme="teal"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Target: {settings.portfolioAllocations.shard}%</FormHelperText>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel htmlFor="new-alloc">New/Emerging Tokens</FormLabel>
                    <Slider
                      id="new-alloc"
                      min={0}
                      max={100}
                      step={5}
                      value={settings.portfolioAllocations.new}
                      onChange={(val) => handleAllocationChange('new', val)}
                      colorScheme="orange"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={6} />
                    </Slider>
                    <FormHelperText>Target: {settings.portfolioAllocations.new}%</FormHelperText>
                  </FormControl>
                </VStack>
                
                <Flex justify="center" mt={8}>
                  <Box w="300px" h="300px">
                    <Doughnut 
                      data={{
                        labels: Object.keys(settings.portfolioAllocations).map(
                          key => key.charAt(0).toUpperCase() + key.slice(1)
                        ),
                        datasets: [{
                          data: Object.values(settings.portfolioAllocations),
                          backgroundColor: [
                            'rgb(26, 133, 255)',
                            'rgb(255, 99, 132)',
                            'rgb(153, 102, 255)',
                            'rgb(255, 206, 86)',
                            'rgb(75, 192, 192)',
                            'rgb(255, 159, 64)',
                          ],
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                          legend: {
                            position: 'right',
                          }
                        }
                      }}
                    />
                  </Box>
                </Flex>
                
                <Alert status="warning" mt={6} borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <AlertTitle>Verification</AlertTitle>
                    <AlertDescription>
                      Please ensure your allocation percentages add up to 100%. Current total: {Object.values(settings.portfolioAllocations).reduce((sum, val) => sum + val, 0)}%
                    </AlertDescription>
                  </Box>
                </Alert>
              </Box>
            </TabPanel>
            
            {/* Keep the rest of your tabs... */}
            
          </TabPanels>
        </Tabs>
      </Box>
    </Layout>
  );
} 