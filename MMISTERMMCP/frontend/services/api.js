import axios from 'axios';

// Base API URL - either from environment variable or a fallback
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Create an axios instance with common configuration
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds
});

// Add a request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage - this is where you would add auth tokens if needed
    const token = typeof window !== 'undefined' ? localStorage.getItem('talos_auth_token') : null;

    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle specific error cases
    if (error.response) {
      // Server responded with a status code outside of 2xx range
      if (error.response.status === 401) {
        // Handle unauthorized errors (e.g., redirect to login)
        if (typeof window !== 'undefined') {
          localStorage.removeItem('talos_auth_token');
          // window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

// API service functions
const api = {
  // Auth Endpoints
  auth: {
    login: (credentials) => apiClient.post('/auth/login', credentials),
    logout: () => apiClient.post('/auth/logout'),
    getProfile: () => apiClient.get('/auth/profile'),
  },

  // Dashboard Endpoints
  dashboard: {
    getSummary: () => apiClient.get('/dashboard/summary'),
    getPerformance: (timeframe = '7d') => apiClient.get(`/dashboard/performance?timeframe=${timeframe}`),
    getPortfolioBreakdown: () => apiClient.get('/dashboard/portfolio-breakdown'),
    getTokenAnalysis: (count = 5) => apiClient.get(`/dashboard/token-analysis?count=${count}`),
  },

  // Trades Endpoints
  trades: {
    getAll: (filters) => apiClient.get('/trades', { params: filters }),
    getById: (id) => apiClient.get(`/trades/${id}`),
    create: (tradeData) => apiClient.post('/trades', tradeData),
    cancel: (id) => apiClient.post(`/trades/${id}/cancel`),
    getRecentTrades: (limit = 10) => apiClient.get(`/trades/recent?limit=${limit}`),
    getTradesByToken: (token, limit = 10) => apiClient.get(`/trades?token=${token}&limit=${limit}`),
    getTradesByStatus: (status, limit = 10) => apiClient.get(`/trades?status=${status}&limit=${limit}`),
    getTradesByDirection: (direction, limit = 10) => apiClient.get(`/trades?direction=${direction}&limit=${limit}`),
  },

  // Analysis Endpoints
  analysis: {
    getTokens: () => apiClient.get('/analysis/tokens'),
    getTopVolumeTokens: (count = 25) => apiClient.get(`/analysis/tokens/top-volume?count=${count}`),
    getTokenAnalysis: (tokenId, timeframe = '7d') =>
      apiClient.get(`/analysis/tokens/${tokenId}?timeframe=${timeframe}`),
    runAnalysis: (tokenId) => apiClient.post(`/analysis/tokens/${tokenId}/analyze`),
  },

  // Wallet Management
  wallet: {
    getStatus: () => apiClient.get('/wallet/status'),
    getBalance: () => apiClient.get('/wallet/balance'),
    getTokens: () => apiClient.get('/wallet/tokens'),
    setWallet: (walletData) => apiClient.post('/wallet/set', walletData),
    validateAddress: (address) => apiClient.post('/wallet/validate-address', { address }),
    getTransactionHistory: (limit = 20) => apiClient.get(`/wallet/transactions?limit=${limit}`),
  },

  // Settings Endpoints
  settings: {
    getAll: () => apiClient.get('/settings'),
    update: (settings) => apiClient.put('/settings', settings),
    getWallets: () => apiClient.get('/settings/wallets'),
    addWallet: (wallet) => apiClient.post('/settings/wallets', wallet),
    removeWallet: (id) => apiClient.delete(`/settings/wallets/${id}`),
    updateWallet: (id, data) => apiClient.put(`/settings/wallets/${id}`, data),
    testApi: (apiData) => apiClient.post('/settings/test-api', apiData),
    getPortfolioAllocations: () => apiClient.get('/settings/portfolio-allocations'),
    updatePortfolioAllocations: (allocations) => apiClient.put('/settings/portfolio-allocations', allocations),
  },

  // Discord Integration
  discord: {
    testWebhook: (url) => apiClient.post('/discord/test-webhook', { url }),
    getSettings: () => apiClient.get('/discord/settings'),
    updateSettings: (settings) => apiClient.put('/discord/settings', settings),
    getRecentNotifications: (limit = 10) => apiClient.get(`/discord/notifications?limit=${limit}`),
  },

  // Trading Bot Controls
  bot: {
    getStatus: () => apiClient.get('/bot/status'),
    start: () => apiClient.post('/bot/start'),
    stop: () => apiClient.post('/bot/stop'),
    restart: () => apiClient.post('/bot/restart'),
    getLogs: (limit = 100) => apiClient.get(`/bot/logs?limit=${limit}`),
    getTradingData: () => apiClient.get('/bot/trading-data'),
    getAnalyzedTokens: (limit = 25) => apiClient.get(`/bot/analyzed-tokens?limit=${limit}`),
    getPerformanceMetrics: () => apiClient.get('/bot/performance-metrics'),
    getTokenPerformance: (ticker) => apiClient.get(`/bot/token-performance/${ticker}`),
    getPerformanceHistory: (timeframe = '30d') => apiClient.get(`/bot/performance-history?timeframe=${timeframe}`),
    getRiskManagementStats: () => apiClient.get('/bot/risk-management-stats'),
    getSettings: () => apiClient.get('/bot/settings'),
  },

  // Settings Management
  settings: {
    getAll: () => apiClient.get('/settings'),
    update: (settings) => apiClient.put('/settings', settings),
    updateGeneralSettings: (settings) => apiClient.put('/settings/general', settings),
    updatePortfolioAllocations: (allocations) => apiClient.put('/settings/portfolio-allocations', allocations),
    updateTradingLimits: (limits) => apiClient.put('/settings/trading-limits', limits),
    updateRiskManagement: (riskSettings) => apiClient.put('/settings/risk-management', riskSettings),
    updateApiKeys: (apiKeys) => apiClient.put('/settings/api-keys', apiKeys),
    reset: () => apiClient.post('/settings/reset'),
  },

  // MCP Integration
  mcp: {
    getStatus: () => apiClient.get('/mcp/status'),
    getTools: () => apiClient.get('/mcp/tools'),
    executeAction: (action, params) => apiClient.post('/mcp/execute', { action, params }),
    getActivityLog: (limit = 20) => apiClient.get(`/mcp/activity?limit=${limit}`),
  },

  // Token Performance Analysis
  tokenAnalysis: {
    getPerformance: (token) => apiClient.get(`/analysis/token-performance/${token}`),
    getTokenHitRate: (token) => apiClient.get(`/analysis/token-performance/${token}`).then(res => res.data.winRate || 0),
    getTokenTradeHistory: (token, limit = 10) => apiClient.get(`/analysis/token-performance/${token}?limit=${limit}`)
      .then(res => res.data.history || []),
  },
};

export default api;