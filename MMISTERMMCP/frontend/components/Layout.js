import { useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Flex,
  HStack,
  Link,
  IconButton,
  Button,
  useDisclosure,
  useColorModeValue,
  Stack,
  Icon,
  Text,
  Avatar,
  Image,
  Container,
  Collapse,
  Popover,
  PopoverTrigger,
  PopoverContent,
  useBreakpointValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  useColorMode,
  Tooltip,
} from '@chakra-ui/react';
import {
  HamburgerIcon,
  CloseIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  MoonIcon,
  SunIcon,
  SettingsIcon,
} from '@chakra-ui/icons';
import NextLink from 'next/link';
import Logo from './Logo';

// Custom navigation icons
const DashboardIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="currentColor"
      d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"
    />
  </Icon>
);

const TradesIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="currentColor"
      d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99l1.5 1.5z"
    />
  </Icon>
);

const AnalysisIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="currentColor"
      d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"
    />
  </Icon>
);

const SettingsPageIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="currentColor"
      d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z"
    />
  </Icon>
);

// MCP Hub icon
const MCPHubIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="currentColor"
      d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14zM8 15c0-1.66 1.34-3 3-3h2c1.66 0 3 1.34 3 3v1H8v-1zm3-7c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm7-3h-4v2h4V5z"
    />
  </Icon>
);

const Links = [
  { name: 'Dashboard', icon: DashboardIcon, href: '/' },
  { name: 'Trades', icon: TradesIcon, href: '/trades' },
  { name: 'Analysis', icon: AnalysisIcon, href: '/analysis' },
  { name: 'Settings', icon: SettingsPageIcon, href: '/settings' },
  { name: 'MCP Hub', icon: MCPHubIcon, href: '/mcp-hub' },
];

const NavLink = ({ name, icon, href, isActive }) => {
  const linkColor = useColorModeValue('gray.600', 'gray.200');
  const linkActiveColor = useColorModeValue('brand.500', 'brand.300');
  const linkActiveTextColor = useColorModeValue('brand.600', 'white');
  const linkActiveBg = useColorModeValue('brand.50', 'brand.900');
  const linkHoverBg = useColorModeValue('gray.100', 'gray.700');

  return (
    <Link
      as={NextLink}
      href={href}
      px={4}
      py={2}
      rounded={'md'}
      color={isActive ? linkActiveTextColor : linkColor}
      bg={isActive ? linkActiveBg : 'transparent'}
      _hover={{
        textDecoration: 'none',
        bg: isActive ? linkActiveBg : linkHoverBg,
      }}
      display="flex"
      alignItems="center"
    >
      <Icon
        as={icon}
        mr={3}
        fontSize="xl"
        color={isActive ? linkActiveColor : linkColor}
      />
      {name}
    </Link>
  );
};

export default function Layout({ children }) {
  const { isOpen, onToggle } = useDisclosure();
  const { colorMode, toggleColorMode } = useColorMode();
  const router = useRouter();
  const [isBotActive, setIsBotActive] = useState(true);

  return (
    <Box minH="100vh">
      <Box
        bg={useColorModeValue('white', 'gray.800')}
        color={useColorModeValue('gray.600', 'white')}
        borderBottom={1}
        borderStyle={'solid'}
        borderColor={useColorModeValue('gray.200', 'gray.700')}
        px={{ base: 4, md: 10 }}
        py={4}
        position="sticky"
        top={0}
        zIndex={10}
      >
        <Flex
          align={'center'}
          justify="space-between"
        >
          <Flex flex={{ base: 1 }} justify={{ base: 'start', md: 'start' }}>
            <Link
              as={NextLink}
              href="/"
              _hover={{ textDecoration: 'none' }}
            >
              <Logo size="md" />
            </Link>

            <Flex display={{ base: 'none', md: 'flex' }} ml={10}>
              <Stack direction={'row'} spacing={4}>
                {Links.map((link) => (
                  <NavLink
                    key={link.name}
                    name={link.name}
                    icon={link.icon}
                    href={link.href}
                    isActive={router.pathname === link.href}
                  />
                ))}
              </Stack>
            </Flex>
          </Flex>

          <HStack spacing={3}>
            <Button
              size="sm"
              rounded="full"
              colorScheme={isBotActive ? "green" : "red"}
              onClick={() => setIsBotActive(!isBotActive)}
            >
              <Box mr={2} w={2} h={2} borderRadius="full" bg="white" />
              {isBotActive ? "Bot Active" : "Bot Inactive"}
            </Button>

            <Tooltip label={colorMode === 'light' ? 'Dark mode' : 'Light mode'}>
              <IconButton
                icon={colorMode === 'light' ? <MoonIcon /> : <SunIcon />}
                onClick={toggleColorMode}
                variant="ghost"
                aria-label="Toggle color mode"
              />
            </Tooltip>

            <Menu>
              <MenuButton
                as={Button}
                rounded={'full'}
                variant={'link'}
                cursor={'pointer'}
                minW={0}
              >
                <Avatar
                  size={'sm'}
                  src={
                    'https://api.dicebear.com/7.x/identicon/svg?seed=MISTER'
                  }
                />
              </MenuButton>
              <MenuList>
                <MenuItem>Profile</MenuItem>
                <MenuItem>API Keys</MenuItem>
                <MenuDivider />
                <MenuItem>Logout</MenuItem>
              </MenuList>
            </Menu>
          </HStack>

          <Flex
            flex={{ base: 1, md: 0 }}
            ml={{ base: -2 }}
            display={{ base: 'flex', md: 'none' }}
            justify="flex-end"
          >
            <IconButton
              onClick={onToggle}
              icon={
                isOpen ? <CloseIcon w={3} h={3} /> : <HamburgerIcon w={5} h={5} />
              }
              variant={'ghost'}
              aria-label={'Toggle Navigation'}
            />
          </Flex>
        </Flex>

        <Collapse in={isOpen} animateOpacity>
          <Stack
            mt={4}
            pb={4}
            display={{ md: 'none' }}
            spacing={4}
          >
            {Links.map((link) => (
              <NavLink
                key={link.name}
                name={link.name}
                icon={link.icon}
                href={link.href}
                isActive={router.pathname === link.href}
              />
            ))}
          </Stack>
        </Collapse>
      </Box>

      <Container maxW="container.xl" py={6}>
        {children}
      </Container>
    </Box>
  );
}