import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  VStack,
  HStack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast,
  InputGroup,
  InputRightElement,
  Text,
  useColorModeValue,
  Heading,
  Textarea,
  Checkbox,
  Divider,
  Link,
  Badge
} from '@chakra-ui/react';
import { ViewIcon, ViewOffIcon, LockIcon, UnlockIcon, CheckIcon, WarningIcon } from '@chakra-ui/icons';
import api from '../services/api';

const WalletSetup = ({ onSetupComplete }) => {
  const [walletAddress, setWalletAddress] = useState('');
  const [seedPhrase, setSeedPhrase] = useState('');
  const [showSeedPhrase, setShowSeedPhrase] = useState(false);
  const [addressValid, setAddressValid] = useState(false);
  const [seedPhraseValid, setSeedPhraseValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);
  
  const toast = useToast();
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  useEffect(() => {
    // Validate address format when it changes
    const validateAddress = () => {
      // Simple validation - Cardano addresses start with 'addr' and are 103-104 characters long
      const isValid = walletAddress.startsWith('addr') && 
        (walletAddress.length >= 103 && walletAddress.length <= 104);
      setAddressValid(isValid);
    };
    
    if (walletAddress) {
      validateAddress();
    } else {
      setAddressValid(false);
    }
  }, [walletAddress]);
  
  useEffect(() => {
    // Validate seed phrase format when it changes
    const validateSeedPhrase = () => {
      // Seed phrases typically consist of 12, 15, 18, 21, or 24 words
      const words = seedPhrase.trim().split(/\s+/);
      const validWordCounts = [12, 15, 18, 21, 24];
      setSeedPhraseValid(validWordCounts.includes(words.length));
    };
    
    if (seedPhrase) {
      validateSeedPhrase();
    } else {
      setSeedPhraseValid(false);
    }
  }, [seedPhrase]);
  
  const handleSubmit = async () => {
    if (!addressValid || !seedPhraseValid || !termsAccepted) {
      setErrorMessage('Please ensure your wallet information is valid and you have accepted the terms.');
      return;
    }
    
    setIsLoading(true);
    setErrorMessage('');
    
    try {
      // First validate the address with the backend
      const validateResponse = await api.wallet.validateAddress(walletAddress);
      
      if (!validateResponse.data.valid) {
        throw new Error('Invalid wallet address. Please check and try again.');
      }
      
      // If address is valid, submit wallet information
      await api.wallet.setWallet({
        address: walletAddress,
        seedPhrase: seedPhrase,
      });
      
      toast({
        title: 'Wallet setup successful',
        description: 'Your wallet has been successfully connected to the trading bot.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      // Notify parent component that setup is complete
      if (onSetupComplete) {
        onSetupComplete();
      }
    } catch (error) {
      console.error('Wallet setup error:', error);
      setErrorMessage(error.message || 'An error occurred while setting up your wallet. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  const toggleShowSeedPhrase = () => {
    setShowSeedPhrase(!showSeedPhrase);
  };
  
  return (
    <Box
      p={6}
      borderWidth="1px"
      borderRadius="lg"
      bg={bgColor}
      borderColor={borderColor}
      shadow="md"
    >
      <VStack spacing={6} align="stretch">
        <Heading size="md">Connect Your Wallet</Heading>
        
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Why connect your wallet?</AlertTitle>
            <AlertDescription>
              Connecting your wallet allows the trading bot to execute trades on your behalf.
              Your seed phrase is encrypted and never stored in plain text.
            </AlertDescription>
          </Box>
        </Alert>
        
        <FormControl isRequired>
          <FormLabel>Wallet Address</FormLabel>
          <Input
            value={walletAddress}
            onChange={(e) => setWalletAddress(e.target.value)}
            placeholder="addr1..."
            isInvalid={walletAddress && !addressValid}
          />
          <FormHelperText>
            Enter your Cardano wallet address (starts with 'addr')
          </FormHelperText>
          {walletAddress && addressValid && (
            <Badge colorScheme="green" mt={1}>
              <CheckIcon mr={1} /> Valid address format
            </Badge>
          )}
        </FormControl>
        
        <FormControl isRequired>
          <FormLabel>Seed Phrase</FormLabel>
          <InputGroup>
            <Textarea
              value={seedPhrase}
              onChange={(e) => setSeedPhrase(e.target.value)}
              placeholder="Enter your 12 or 24-word seed phrase..."
              type={showSeedPhrase ? 'text' : 'password'}
              isInvalid={seedPhrase && !seedPhraseValid}
              height="100px"
            />
            <InputRightElement>
              <Button
                h="1.75rem"
                size="sm"
                onClick={toggleShowSeedPhrase}
                position="absolute"
                top="10px"
                right="10px"
              >
                {showSeedPhrase ? <ViewOffIcon /> : <ViewIcon />}
              </Button>
            </InputRightElement>
          </InputGroup>
          <FormHelperText>
            Your seed phrase is encrypted and will only be used for trading
          </FormHelperText>
          {seedPhrase && seedPhraseValid && (
            <Badge colorScheme="green" mt={1}>
              <CheckIcon mr={1} /> Valid seed phrase format
            </Badge>
          )}
        </FormControl>
        
        <Alert status="warning" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Security Information</AlertTitle>
            <AlertDescription>
              Your seed phrase is your private key and should never be shared. 
              We encrypt your seed phrase and only use it for executing trades you approve.
            </AlertDescription>
          </Box>
        </Alert>
        
        <FormControl>
          <Checkbox
            isChecked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            colorScheme="green"
          >
            I understand that I am giving trading permissions to this bot and take full responsibility for the trading outcomes
          </Checkbox>
        </FormControl>
        
        {errorMessage && (
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}
        
        <Button
          colorScheme="blue"
          size="lg"
          onClick={handleSubmit}
          isLoading={isLoading}
          isDisabled={!addressValid || !seedPhraseValid || !termsAccepted}
          leftIcon={termsAccepted ? <UnlockIcon /> : <LockIcon />}
        >
          Connect Wallet
        </Button>
        
        <Divider />
        
        <Text fontSize="sm" color="gray.500">
          By connecting your wallet, you're allowing the bot to execute trades on your behalf. 
          You can revoke access at any time by removing your wallet information.
        </Text>
      </VStack>
    </Box>
  );
};

export default WalletSetup; 