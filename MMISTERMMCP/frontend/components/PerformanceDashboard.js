import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  StatGroup,
  Grid,
  GridItem,
  Badge,
  Divider,
  Flex,
  useColorModeValue,
  CircularProgress,
  CircularProgressLabel,
  Card,
  CardBody,
  CardHeader,
  SimpleGrid,
  Skeleton,
  Select,
  Button,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Tooltip,
  Icon
} from '@chakra-ui/react';
import { ArrowUpIcon, ArrowDownIcon, CheckIcon, WarningIcon, InfoIcon } from '@chakra-ui/icons';
import { Line, Bar, Doughnut, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip as ChartTooltip, Legend, CategoryScale, LinearScale, BarElement, Title, PointElement, LineElement } from 'chart.js';
import api from '../services/api';

// Register Chart.js components
ChartJS.register(ArcElement, ChartTooltip, Legend, CategoryScale, LinearScale, BarElement, Title, PointElement, LineElement);

const PerformanceDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeframe, setTimeframe] = useState('30d');
  const [metrics, setMetrics] = useState({
    totalTrades: 0,
    successfulTrades: 0,
    failedTrades: 0,
    winRate: 0,
    totalProfitLoss: 0,
    averageProfitLoss: 0,
    bestTrade: null,
    worstTrade: null,
    timeframePerformance: {
      daily: 0,
      weekly: 0,
      monthly: 0
    },
    tokenPerformance: []
  });
  const [performanceHistory, setPerformanceHistory] = useState({
    labels: [],
    datasets: [
      {
        label: 'Cumulative Profit/Loss (ADA)',
        data: [],
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      }
    ]
  });
  const [riskStats, setRiskStats] = useState({
    riskCategoryDistribution: {
      ultra_high: 0,
      high: 0,
      medium: 0,
      low: 0
    },
    stopLossHits: 0,
    targetHits: 0,
    averageRiskRewardRatio: 0,
    timeBasedExits: 0
  });
  
  // Colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');
  
  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch performance metrics
        const metricsResponse = await api.bot.getPerformanceMetrics();
        setMetrics(metricsResponse.data);
        
        // Fetch performance history
        const historyResponse = await api.dashboard.getPerformance(timeframe);
        setPerformanceHistory(historyResponse.data);
        
        // Fetch risk management stats
        const riskResponse = await api.bot.getRiskManagementStats();
        setRiskStats(riskResponse.data);
        
        setError(null);
      } catch (err) {
        console.error('Error fetching performance data:', err);
        setError('Failed to load performance data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [timeframe]);
  
  // Prepare chart data
  const tokenPerformanceData = {
    labels: metrics.tokenPerformance.map(token => token.ticker),
    datasets: [
      {
        label: 'Win Rate (%)',
        data: metrics.tokenPerformance.map(token => token.winRate),
        backgroundColor: metrics.tokenPerformance.map(token => 
          token.winRate >= 70 ? 'rgba(72, 187, 120, 0.7)' :
          token.winRate >= 50 ? 'rgba(237, 137, 54, 0.7)' :
          'rgba(229, 62, 62, 0.7)'
        ),
        borderColor: metrics.tokenPerformance.map(token => 
          token.winRate >= 70 ? 'rgb(72, 187, 120)' :
          token.winRate >= 50 ? 'rgb(237, 137, 54)' :
          'rgb(229, 62, 62)'
        ),
        borderWidth: 1
      }
    ]
  };
  
  const tradeResultsData = {
    labels: ['Successful', 'Failed'],
    datasets: [
      {
        data: [metrics.successfulTrades, metrics.failedTrades],
        backgroundColor: [
          'rgba(72, 187, 120, 0.7)',
          'rgba(229, 62, 62, 0.7)'
        ],
        borderColor: [
          'rgb(72, 187, 120)',
          'rgb(229, 62, 62)'
        ],
        borderWidth: 1
      }
    ]
  };
  
  const riskDistributionData = {
    labels: ['Ultra High', 'High', 'Medium', 'Low'],
    datasets: [
      {
        data: [
          riskStats.riskCategoryDistribution.ultra_high,
          riskStats.riskCategoryDistribution.high,
          riskStats.riskCategoryDistribution.medium,
          riskStats.riskCategoryDistribution.low
        ],
        backgroundColor: [
          'rgba(229, 62, 62, 0.7)',
          'rgba(237, 137, 54, 0.7)',
          'rgba(246, 173, 85, 0.7)',
          'rgba(72, 187, 120, 0.7)'
        ],
        borderColor: [
          'rgb(229, 62, 62)',
          'rgb(237, 137, 54)',
          'rgb(246, 173, 85)',
          'rgb(72, 187, 120)'
        ],
        borderWidth: 1
      }
    ]
  };
  
  const exitReasonsData = {
    labels: ['Stop Loss', 'Target Reached', 'Time-Based', 'Other'],
    datasets: [
      {
        data: [
          riskStats.stopLossHits,
          riskStats.targetHits,
          riskStats.timeBasedExits,
          metrics.totalTrades - (riskStats.stopLossHits + riskStats.targetHits + riskStats.timeBasedExits)
        ],
        backgroundColor: [
          'rgba(229, 62, 62, 0.7)',
          'rgba(72, 187, 120, 0.7)',
          'rgba(66, 153, 225, 0.7)',
          'rgba(160, 174, 192, 0.7)'
        ],
        borderColor: [
          'rgb(229, 62, 62)',
          'rgb(72, 187, 120)',
          'rgb(66, 153, 225)',
          'rgb(160, 174, 192)'
        ],
        borderWidth: 1
      }
    ]
  };
  
  // Handle timeframe change
  const handleTimeframeChange = (event) => {
    setTimeframe(event.target.value);
  };
  
  if (loading) {
    return (
      <Box p={4}>
        <Skeleton height="40px" mb={4} />
        <SimpleGrid columns={{ base: 1, md: 4 }} spacing={5} mb={8}>
          <Skeleton height="150px" />
          <Skeleton height="150px" />
          <Skeleton height="150px" />
          <Skeleton height="150px" />
        </SimpleGrid>
        <Skeleton height="300px" mb={8} />
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          <Skeleton height="300px" />
          <Skeleton height="300px" />
        </SimpleGrid>
      </Box>
    );
  }
  
  if (error) {
    return (
      <Box p={4} textAlign="center">
        <WarningIcon boxSize="50px" color="red.500" mb={4} />
        <Heading size="md" mb={2}>Error Loading Data</Heading>
        <Text>{error}</Text>
        <Button mt={4} colorScheme="blue" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Box>
    );
  }
  
  return (
    <Box p={4}>
      <Flex justify="space-between" align="center" mb={6}>
        <Heading size="lg">Performance Analytics</Heading>
        <Flex align="center">
          <Text mr={2}>Timeframe:</Text>
          <Select value={timeframe} onChange={handleTimeframeChange} w="120px">
            <option value="7d">7 Days</option>
            <option value="30d">30 Days</option>
            <option value="90d">90 Days</option>
            <option value="1y">1 Year</option>
          </Select>
        </Flex>
      </Flex>
      
      {/* Key Metrics */}
      <SimpleGrid columns={{ base: 1, md: 4 }} spacing={5} mb={8}>
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Win Rate</StatLabel>
              <StatNumber fontSize="2xl">
                {metrics.winRate.toFixed(1)}%
              </StatNumber>
              <StatHelpText>
                {metrics.successfulTrades} / {metrics.totalTrades} trades
              </StatHelpText>
              <CircularProgress value={metrics.winRate} color="green.400" size="70px" thickness="8px">
                <CircularProgressLabel>{metrics.winRate.toFixed(0)}%</CircularProgressLabel>
              </CircularProgress>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Total Profit/Loss</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={metrics.totalProfitLoss >= 0 ? 'green.500' : 'red.500'}
              >
                {metrics.totalProfitLoss >= 0 ? '+' : ''}{metrics.totalProfitLoss.toFixed(2)} ADA
              </StatNumber>
              <StatHelpText>
                <StatArrow type={metrics.totalProfitLoss >= 0 ? 'increase' : 'decrease'} />
                Avg: {metrics.averageProfitLoss.toFixed(2)} ADA per trade
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Best Trade</StatLabel>
              {metrics.bestTrade ? (
                <>
                  <StatNumber fontSize="2xl" color="green.500">
                    +{metrics.bestTrade.performanceTracking.profitLoss.toFixed(2)} ADA
                  </StatNumber>
                  <StatHelpText>
                    {metrics.bestTrade.ticker} ({new Date(metrics.bestTrade.timestamp).toLocaleDateString()})
                  </StatHelpText>
                </>
              ) : (
                <StatNumber fontSize="2xl">N/A</StatNumber>
              )}
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Worst Trade</StatLabel>
              {metrics.worstTrade ? (
                <>
                  <StatNumber fontSize="2xl" color="red.500">
                    {metrics.worstTrade.performanceTracking.profitLoss.toFixed(2)} ADA
                  </StatNumber>
                  <StatHelpText>
                    {metrics.worstTrade.ticker} ({new Date(metrics.worstTrade.timestamp).toLocaleDateString()})
                  </StatHelpText>
                </>
              ) : (
                <StatNumber fontSize="2xl">N/A</StatNumber>
              )}
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* Period Performance */}
      <Heading size="md" mb={4}>Period Performance</Heading>
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={5} mb={8}>
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Daily</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={metrics.timeframePerformance.daily >= 0 ? 'green.500' : 'red.500'}
              >
                {metrics.timeframePerformance.daily >= 0 ? '+' : ''}{metrics.timeframePerformance.daily.toFixed(2)} ADA
              </StatNumber>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Weekly</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={metrics.timeframePerformance.weekly >= 0 ? 'green.500' : 'red.500'}
              >
                {metrics.timeframePerformance.weekly >= 0 ? '+' : ''}{metrics.timeframePerformance.weekly.toFixed(2)} ADA
              </StatNumber>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Monthly</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={metrics.timeframePerformance.monthly >= 0 ? 'green.500' : 'red.500'}
              >
                {metrics.timeframePerformance.monthly >= 0 ? '+' : ''}{metrics.timeframePerformance.monthly.toFixed(2)} ADA
              </StatNumber>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* Performance History Chart */}
      <Card bg={bgColor} boxShadow="md" mb={8}>
        <CardHeader>
          <Heading size="md">Performance History</Heading>
        </CardHeader>
        <CardBody>
          <Box height="300px" position="relative">
            <Line 
              data={performanceHistory}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: false,
                    title: {
                      display: true,
                      text: 'Profit/Loss (ADA)'
                    }
                  },
                  x: {
                    title: {
                      display: true,
                      text: 'Date'
                    }
                  }
                }
              }}
            />
          </Box>
        </CardBody>
      </Card>
      
      {/* Charts Section */}
      <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={6} mb={8}>
        <GridItem>
          <Card bg={bgColor} boxShadow="md" height="100%">
            <CardHeader>
              <Heading size="sm">Token Performance</Heading>
            </CardHeader>
            <CardBody>
              <Box height="300px" position="relative">
                <Bar 
                  data={tokenPerformanceData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            return `Win Rate: ${context.parsed.y.toFixed(1)}%`;
                          }
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                          display: true,
                          text: 'Win Rate (%)'
                        }
                      }
                    }
                  }}
                />
              </Box>
            </CardBody>
          </Card>
        </GridItem>
        
        <GridItem>
          <Card bg={bgColor} boxShadow="md" height="100%">
            <CardHeader>
              <Heading size="sm">Trading Results</Heading>
            </CardHeader>
            <CardBody>
              <Flex justify="center" align="center" height="300px">
                <Box width="60%">
                  <Doughnut 
                    data={tradeResultsData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              const label = context.label || '';
                              const value = context.parsed || 0;
                              const total = context.dataset.data.reduce((a, b) => a + b, 0);
                              const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                              return `${label}: ${value} (${percentage}%)`;
                            }
                          }
                        }
                      }
                    }}
                  />
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </GridItem>
      </Grid>
      
      {/* Risk Management Section */}
      <Heading size="md" mb={4}>Risk Management</Heading>
      <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={6} mb={8}>
        <GridItem>
          <Card bg={bgColor} boxShadow="md" height="100%">
            <CardHeader>
              <Heading size="sm">Risk Category Distribution</Heading>
            </CardHeader>
            <CardBody>
              <Flex justify="center" align="center" height="300px">
                <Box width="60%">
                  <Pie 
                    data={riskDistributionData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              const label = context.label || '';
                              const value = context.parsed || 0;
                              const total = context.dataset.data.reduce((a, b) => a + b, 0);
                              const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                              return `${label}: ${value} (${percentage}%)`;
                            }
                          }
                        }
                      }
                    }}
                  />
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </GridItem>
        
        <GridItem>
          <Card bg={bgColor} boxShadow="md" height="100%">
            <CardHeader>
              <Heading size="sm">Exit Reasons</Heading>
            </CardHeader>
            <CardBody>
              <Flex justify="center" align="center" height="300px">
                <Box width="60%">
                  <Doughnut 
                    data={exitReasonsData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              const label = context.label || '';
                              const value = context.parsed || 0;
                              const total = context.dataset.data.reduce((a, b) => a + b, 0);
                              const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                              return `${label}: ${value} (${percentage}%)`;
                            }
                          }
                        }
                      }
                    }}
                  />
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </GridItem>
      </Grid>
      
      {/* Risk Stats */}
      <Card bg={bgColor} boxShadow="md" mb={8}>
        <CardHeader>
          <Heading size="sm">Risk Management Statistics</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={5}>
            <Stat>
              <StatLabel fontSize="sm">Average Risk/Reward Ratio</StatLabel>
              <StatNumber fontSize="xl">
                {riskStats.averageRiskRewardRatio.toFixed(2)}
              </StatNumber>
              <StatHelpText>
                Higher is better
              </StatHelpText>
            </Stat>
            
            <Stat>
              <StatLabel fontSize="sm">Stop Loss Hits</StatLabel>
              <StatNumber fontSize="xl">
                {riskStats.stopLossHits}
              </StatNumber>
              <StatHelpText>
                {metrics.totalTrades > 0 ? ((riskStats.stopLossHits / metrics.totalTrades) * 100).toFixed(1) : 0}% of trades
              </StatHelpText>
            </Stat>
            
            <Stat>
              <StatLabel fontSize="sm">Target Price Hits</StatLabel>
              <StatNumber fontSize="xl">
                {riskStats.targetHits}
              </StatNumber>
              <StatHelpText>
                {metrics.totalTrades > 0 ? ((riskStats.targetHits / metrics.totalTrades) * 100).toFixed(1) : 0}% of trades
              </StatHelpText>
            </Stat>
          </SimpleGrid>
        </CardBody>
      </Card>
      
      {/* Token Performance Table */}
      <Card bg={bgColor} boxShadow="md">
        <CardHeader>
          <Heading size="sm">Token Performance Details</Heading>
        </CardHeader>
        <CardBody>
          <TableContainer>
            <Table variant="simple" size="sm">
              <Thead>
                <Tr>
                  <Th>Token</Th>
                  <Th isNumeric>Trades</Th>
                  <Th isNumeric>Win Rate</Th>
                  <Th isNumeric>Profit/Loss</Th>
                  <Th isNumeric>Avg. P/L</Th>
                </Tr>
              </Thead>
              <Tbody>
                {metrics.tokenPerformance.map((token, index) => (
                  <Tr key={index}>
                    <Td fontWeight="semibold">{token.ticker}</Td>
                    <Td isNumeric>{token.totalTrades}</Td>
                    <Td isNumeric>
                      <Badge colorScheme={token.winRate >= 70 ? 'green' : token.winRate >= 50 ? 'yellow' : 'red'}>
                        {token.winRate.toFixed(1)}%
                      </Badge>
                    </Td>
                    <Td isNumeric color={token.totalProfitLoss >= 0 ? 'green.500' : 'red.500'}>
                      {token.totalProfitLoss >= 0 ? '+' : ''}{token.totalProfitLoss.toFixed(2)} ADA
                    </Td>
                    <Td isNumeric color={token.averageProfitLoss >= 0 ? 'green.500' : 'red.500'}>
                      {token.averageProfitLoss >= 0 ? '+' : ''}{token.averageProfitLoss.toFixed(2)} ADA
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        </CardBody>
      </Card>
    </Box>
  );
};

export default PerformanceDashboard;
