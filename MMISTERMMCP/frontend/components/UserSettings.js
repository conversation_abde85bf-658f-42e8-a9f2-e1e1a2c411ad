import React, { useState, useEffect } from 'react';
import {
  Box, Heading, Text, FormControl, FormLabel, Input, Switch, Slider,
  SliderTrack, SliderFilledTrack, SliderThumb, Button, useToast,
  SimpleGrid, Card, CardBody, CardHeader, Divider, Select,
  NumberInput, NumberInputField, NumberInputStepper,
  NumberIncrementStepper, NumberDecrementStepper, Flex, Tooltip,
  useColorModeValue, Icon
} from '@chakra-ui/react';
import { InfoIcon, CheckIcon } from '@chakra-ui/icons';
import api from '../services/api';

const UserSettings = () => {
  const [loading, setLoading] = useState(true);
  const [saveStatus, setSaveStatus] = useState(null);
  const [settings, setSettings] = useState({
    general: {
      botEnabled: true,
      autoTradeEnabled: true,
      maxTradesPerDay: 5,
      maxTradeSize: 100,
    },
    portfolioAllocations: {
      ada: 30,
      meme: 15,
      defi: 20,
      major: 15,
      shard: 10,
      new: 10
    },
    tradingLimits: {
      minConfidence: 7,
      minTokenAge: 7,
      minLiquidity: 5000,
      maxImpact: 5,
    },
    riskManagement: {
      dynamicPositionSizing: true,
      stopLossEnabled: true,
      trailingStopEnabled: false,
      timeBasedExitEnabled: true,
      defaultRiskLevel: "medium"
    }
  });
  
  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      try {
        const response = await api.bot.getSettings();
        setSettings(response.data);
      } catch (error) {
        console.error('Error fetching settings:', error);
        toast({
          title: 'Error fetching settings',
          description: 'Could not load your settings. Please try again later.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchSettings();
  }, [toast]);

  const handleSaveSettings = async () => {
    setSaveStatus('saving');
    try {
      // Update general settings
      await api.settings.updateGeneralSettings(settings.general);
      
      // Update portfolio allocations
      await api.settings.updatePortfolioAllocations(settings.portfolioAllocations);
      
      // Update trading limits
      await api.settings.updateTradingLimits(settings.tradingLimits);
      
      // Update risk management settings
      await api.settings.updateRiskManagement(settings.riskManagement);
      
      setSaveStatus('success');
      toast({
        title: 'Settings saved',
        description: 'Your settings have been successfully updated.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      // Reset status after a delay
      setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setSaveStatus('error');
      toast({
        title: 'Error saving settings',
        description: 'Could not save your settings. Please try again later.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (section, field, value) => {
    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [field]: value
      }
    });
  };
  
  const validatePortfolioAllocations = () => {
    const { ada, meme, defi, major, shard, new: newTokens } = settings.portfolioAllocations;
    const total = ada + meme + defi + major + shard + newTokens;
    return total === 100;
  };
  
  return (
    <Box p={4}>
      <Flex justify="space-between" align="center" mb={6}>
        <Heading size="lg">Trading Settings</Heading>
        <Button
          colorScheme={saveStatus === 'error' ? 'red' : 'blue'}
          onClick={handleSaveSettings}
          isLoading={saveStatus === 'saving'}
          leftIcon={saveStatus === 'success' ? <CheckIcon /> : undefined}
          isDisabled={!validatePortfolioAllocations()}
        >
          {saveStatus === 'success' ? 'Saved' : 'Save Settings'}
        </Button>
      </Flex>
      
      {/* General Settings */}
      <Card bg={bgColor} mb={6} boxShadow="md">
        <CardHeader>
          <Heading size="md">General Settings</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="bot-enabled" mb="0">
                Enable Trading Bot
              </FormLabel>
              <Switch
                id="bot-enabled"
                isChecked={settings.general.botEnabled}
                onChange={(e) => handleInputChange('general', 'botEnabled', e.target.checked)}
                colorScheme="blue"
              />
            </FormControl>
            
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="auto-trade" mb="0">
                Enable Automatic Trading
              </FormLabel>
              <Switch
                id="auto-trade"
                isChecked={settings.general.autoTradeEnabled}
                onChange={(e) => handleInputChange('general', 'autoTradeEnabled', e.target.checked)}
                colorScheme="blue"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="max-trades">Maximum Trades Per Day</FormLabel>
              <NumberInput
                id="max-trades"
                min={1}
                max={20}
                value={settings.general.maxTradesPerDay}
                onChange={(valueString) => handleInputChange('general', 'maxTradesPerDay', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="max-trade-size">Maximum Trade Size (ADA)</FormLabel>
              <NumberInput
                id="max-trade-size"
                min={10}
                max={1000}
                value={settings.general.maxTradeSize}
                onChange={(valueString) => handleInputChange('general', 'maxTradeSize', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
          </SimpleGrid>
        </CardBody>
      </Card>
      
      {/* Portfolio Allocations */}
      <Card bg={bgColor} mb={6} boxShadow="md">
        <CardHeader>
          <Heading size="md">Portfolio Allocations</Heading>
          <Text fontSize="sm" mt={1}>
            Allocate your portfolio across different token categories (must total 100%)
          </Text>
          {!validatePortfolioAllocations() && (
            <Text color="red.500" fontSize="sm" mt={1}>
              Portfolio allocations must total 100%
            </Text>
          )}
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            <FormControl>
              <FormLabel htmlFor="ada-allocation">ADA (%)</FormLabel>
              <NumberInput
                id="ada-allocation"
                min={0}
                max={100}
                value={settings.portfolioAllocations.ada}
                onChange={(valueString) => handleInputChange('portfolioAllocations', 'ada', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="meme-allocation">Meme Tokens (%)</FormLabel>
              <NumberInput
                id="meme-allocation"
                min={0}
                max={100}
                value={settings.portfolioAllocations.meme}
                onChange={(valueString) => handleInputChange('portfolioAllocations', 'meme', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="defi-allocation">DeFi Tokens (%)</FormLabel>
              <NumberInput
                id="defi-allocation"
                min={0}
                max={100}
                value={settings.portfolioAllocations.defi}
                onChange={(valueString) => handleInputChange('portfolioAllocations', 'defi', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="major-allocation">Major Tokens (%)</FormLabel>
              <NumberInput
                id="major-allocation"
                min={0}
                max={100}
                value={settings.portfolioAllocations.major}
                onChange={(valueString) => handleInputChange('portfolioAllocations', 'major', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="shard-allocation">Shard Tokens (%)</FormLabel>
              <NumberInput
                id="shard-allocation"
                min={0}
                max={100}
                value={settings.portfolioAllocations.shard}
                onChange={(valueString) => handleInputChange('portfolioAllocations', 'shard', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="new-allocation">New Tokens (%)</FormLabel>
              <NumberInput
                id="new-allocation"
                min={0}
                max={100}
                value={settings.portfolioAllocations.new}
                onChange={(valueString) => handleInputChange('portfolioAllocations', 'new', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
          </SimpleGrid>
        </CardBody>
      </Card>
      
      {/* Trading Limits */}
      <Card bg={bgColor} mb={6} boxShadow="md">
        <CardHeader>
          <Heading size="md">Trading Limits</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
            <FormControl>
              <FormLabel htmlFor="min-confidence">
                Minimum Confidence Score (1-10)
                <Tooltip label="Minimum AI confidence score required to execute a trade">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <Slider
                id="min-confidence"
                min={1}
                max={10}
                step={1}
                value={settings.tradingLimits.minConfidence}
                onChange={(value) => handleInputChange('tradingLimits', 'minConfidence', value)}
                colorScheme="blue"
              >
                <SliderTrack>
                  <SliderFilledTrack />
                </SliderTrack>
                <SliderThumb boxSize={6}>
                  <Text fontSize="xs">{settings.tradingLimits.minConfidence}</Text>
                </SliderThumb>
              </Slider>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="min-token-age">
                Minimum Token Age (days)
                <Tooltip label="Minimum age of a token in days to be considered for trading">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <NumberInput
                id="min-token-age"
                min={0}
                max={30}
                value={settings.tradingLimits.minTokenAge}
                onChange={(valueString) => handleInputChange('tradingLimits', 'minTokenAge', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="min-liquidity">
                Minimum Liquidity (ADA)
                <Tooltip label="Minimum liquidity in ADA required for a token to be traded">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <NumberInput
                id="min-liquidity"
                min={1000}
                max={50000}
                step={1000}
                value={settings.tradingLimits.minLiquidity}
                onChange={(valueString) => handleInputChange('tradingLimits', 'minLiquidity', parseInt(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="max-impact">
                Maximum Price Impact (%)
                <Tooltip label="Maximum price impact percentage allowed for a trade">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <NumberInput
                id="max-impact"
                min={1}
                max={10}
                step={0.5}
                value={settings.tradingLimits.maxImpact}
                onChange={(valueString) => handleInputChange('tradingLimits', 'maxImpact', parseFloat(valueString))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
          </SimpleGrid>
        </CardBody>
      </Card>
      
      {/* Risk Management */}
      <Card bg={bgColor} mb={6} boxShadow="md">
        <CardHeader>
          <Heading size="md">Risk Management</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="dynamic-sizing" mb="0">
                Dynamic Position Sizing
                <Tooltip label="Adjust position sizes based on risk assessment">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <Switch
                id="dynamic-sizing"
                isChecked={settings.riskManagement.dynamicPositionSizing}
                onChange={(e) => handleInputChange('riskManagement', 'dynamicPositionSizing', e.target.checked)}
                colorScheme="blue"
              />
            </FormControl>
            
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="stop-loss" mb="0">
                Stop Loss Tracking
                <Tooltip label="Track stop loss levels for each trade">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <Switch
                id="stop-loss"
                isChecked={settings.riskManagement.stopLossEnabled}
                onChange={(e) => handleInputChange('riskManagement', 'stopLossEnabled', e.target.checked)}
                colorScheme="blue"
              />
            </FormControl>
            
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="trailing-stop" mb="0">
                Trailing Stop
                <Tooltip label="Use trailing stops that move with price">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <Switch
                id="trailing-stop"
                isChecked={settings.riskManagement.trailingStopEnabled}
                onChange={(e) => handleInputChange('riskManagement', 'trailingStopEnabled', e.target.checked)}
                colorScheme="blue"
              />
            </FormControl>
            
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="time-based" mb="0">
                Time-Based Exits
                <Tooltip label="Set maximum hold times for positions">
                  <InfoIcon ml={1} boxSize={3} />
                </Tooltip>
              </FormLabel>
              <Switch
                id="time-based"
                isChecked={settings.riskManagement.timeBasedExitEnabled}
                onChange={(e) => handleInputChange('riskManagement', 'timeBasedExitEnabled', e.target.checked)}
                colorScheme="blue"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel htmlFor="default-risk">Default Risk Level</FormLabel>
              <Select
                id="default-risk"
                value={settings.riskManagement.defaultRiskLevel}
                onChange={(e) => handleInputChange('riskManagement', 'defaultRiskLevel', e.target.value)}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="ultra_high">Ultra High</option>
              </Select>
            </FormControl>
          </SimpleGrid>
        </CardBody>
      </Card>
    </Box>
  );
};

export default UserSettings;
