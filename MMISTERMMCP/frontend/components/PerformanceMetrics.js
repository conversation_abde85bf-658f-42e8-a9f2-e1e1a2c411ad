import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  StatGroup,
  Grid,
  GridItem,
  Badge,
  Divider,
  Flex,
  useColorModeValue,
  CircularProgress,
  CircularProgressLabel,
  Card,
  CardBody,
  CardHeader,
  SimpleGrid,
  Skeleton
} from '@chakra-ui/react';
import { ArrowUpIcon, ArrowDownIcon, CheckIcon, WarningIcon, InfoIcon } from '@chakra-ui/icons';
import { Doughnut, Bar } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';
import api from '../services/api';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);

const PerformanceMetrics = () => {
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeframe, setTimeframe] = useState('7d');
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.600', 'gray.200');
  const statBg = useColorModeValue('gray.50', 'gray.700');
  
  useEffect(() => {
    const fetchMetrics = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would fetch from your API
        const response = await api.bot.getPerformanceMetrics();
        setMetrics(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching performance metrics:', err);
        setError('Failed to load performance metrics. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMetrics();
    
    // Poll for updates every 5 minutes
    const intervalId = setInterval(fetchMetrics, 5 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  if (loading) {
    return (
      <Box p={5}>
        <Skeleton height="20px" mb={4} />
        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={5}>
          <Skeleton height="150px" />
          <Skeleton height="150px" />
          <Skeleton height="150px" />
        </SimpleGrid>
        <Skeleton height="300px" mt={5} />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Box p={5} textAlign="center">
        <WarningIcon boxSize={10} color="red.500" mb={3} />
        <Text fontSize="xl" fontWeight="bold">{error}</Text>
      </Box>
    );
  }
  
  // Use mock data for demonstration
  const mockMetrics = {
    totalTrades: 42,
    successfulTrades: 28,
    failedTrades: 14,
    pendingTrades: 3,
    winRate: 66.7,
    averageProfitLoss: 15.3,
    totalProfitLoss: 642.6,
    bestTrade: {
      token: 'HUNT',
      profitLossPercentage: 48.5,
      timestamp: '2023-11-15T14:23:45Z'
    },
    worstTrade: {
      token: 'WMTX',
      profitLossPercentage: -22.3,
      timestamp: '2023-11-10T09:12:32Z'
    },
    tokenPerformance: {
      'SNEK': { trades: 8, successfulTrades: 6, winRate: 75.0, averageProfitLoss: 18.2 },
      'HUNT': { trades: 12, successfulTrades: 10, winRate: 83.3, averageProfitLoss: 22.5 },
      'WMTX': { trades: 7, successfulTrades: 3, winRate: 42.9, averageProfitLoss: -5.3 },
      'IAG': { trades: 6, successfulTrades: 4, winRate: 66.7, averageProfitLoss: 12.1 },
      'HOSKY': { trades: 9, successfulTrades: 5, winRate: 55.6, averageProfitLoss: 8.7 }
    },
    timeframePerformance: {
      daily: 36.5,
      weekly: 124.8,
      monthly: 642.6
    }
  };
  
  // Use actual metrics if available, otherwise use mock data
  const data = metrics || mockMetrics;
  
  // Prepare chart data for token performance
  const tokenData = {
    labels: Object.keys(data.tokenPerformance),
    datasets: [
      {
        label: 'Win Rate (%)',
        data: Object.values(data.tokenPerformance).map(token => token.winRate),
        backgroundColor: [
          'rgba(54, 162, 235, 0.6)',
          'rgba(75, 192, 192, 0.6)',
          'rgba(255, 206, 86, 0.6)',
          'rgba(255, 99, 132, 0.6)',
          'rgba(153, 102, 255, 0.6)'
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(153, 102, 255, 1)'
        ],
        borderWidth: 1
      }
    ]
  };
  
  // Prepare chart data for success vs failed
  const tradeResultsData = {
    labels: ['Successful', 'Failed'],
    datasets: [
      {
        data: [data.successfulTrades, data.failedTrades],
        backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(255, 99, 132, 0.6)'],
        borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)'],
        borderWidth: 1
      }
    ]
  };
  
  return (
    <Box p={5}>
      <Heading size="lg" mb={5}>Trading Bot Performance</Heading>
      
      {/* Key Metrics */}
      <SimpleGrid columns={{ base: 1, md: 4 }} spacing={5} mb={8}>
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Win Rate</StatLabel>
              <StatNumber fontSize="2xl">
                {data.winRate.toFixed(1)}%
              </StatNumber>
              <StatHelpText>
                {data.successfulTrades} / {data.totalTrades} trades
              </StatHelpText>
              <CircularProgress value={data.winRate} color="green.400" size="70px" thickness="8px">
                <CircularProgressLabel>{data.winRate.toFixed(0)}%</CircularProgressLabel>
              </CircularProgress>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Total Profit/Loss</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={data.totalProfitLoss >= 0 ? 'green.500' : 'red.500'}
              >
                {data.totalProfitLoss >= 0 ? '+' : ''}{data.totalProfitLoss.toFixed(2)} ADA
              </StatNumber>
              <StatHelpText>
                <StatArrow type={data.totalProfitLoss >= 0 ? 'increase' : 'decrease'} />
                Avg: {data.averageProfitLoss.toFixed(2)} ADA per trade
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Best Trade</StatLabel>
              <StatNumber fontSize="2xl">
                {data.bestTrade.token}
              </StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                {data.bestTrade.profitLossPercentage.toFixed(1)}% profit
              </StatHelpText>
              <Text fontSize="xs" color={textColor}>
                {new Date(data.bestTrade.timestamp).toLocaleDateString()}
              </Text>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Worst Trade</StatLabel>
              <StatNumber fontSize="2xl">
                {data.worstTrade.token}
              </StatNumber>
              <StatHelpText>
                <StatArrow type="decrease" />
                {Math.abs(data.worstTrade.profitLossPercentage).toFixed(1)}% loss
              </StatHelpText>
              <Text fontSize="xs" color={textColor}>
                {new Date(data.worstTrade.timestamp).toLocaleDateString()}
              </Text>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* Period Performance */}
      <Heading size="md" mb={4}>Period Performance</Heading>
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={5} mb={8}>
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Daily</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={data.timeframePerformance.daily >= 0 ? 'green.500' : 'red.500'}
              >
                {data.timeframePerformance.daily >= 0 ? '+' : ''}{data.timeframePerformance.daily.toFixed(2)} ADA
              </StatNumber>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Weekly</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={data.timeframePerformance.weekly >= 0 ? 'green.500' : 'red.500'}
              >
                {data.timeframePerformance.weekly >= 0 ? '+' : ''}{data.timeframePerformance.weekly.toFixed(2)} ADA
              </StatNumber>
            </Stat>
          </CardBody>
        </Card>
        
        <Card bg={bgColor} boxShadow="md">
          <CardBody>
            <Stat>
              <StatLabel fontSize="md">Monthly</StatLabel>
              <StatNumber 
                fontSize="2xl" 
                color={data.timeframePerformance.monthly >= 0 ? 'green.500' : 'red.500'}
              >
                {data.timeframePerformance.monthly >= 0 ? '+' : ''}{data.timeframePerformance.monthly.toFixed(2)} ADA
              </StatNumber>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* Charts Section */}
      <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={6} mb={8}>
        <GridItem>
          <Card bg={bgColor} boxShadow="md" height="100%">
            <CardHeader>
              <Heading size="sm">Token Performance</Heading>
            </CardHeader>
            <CardBody>
              <Box height="300px" position="relative">
                <Bar 
                  data={tokenData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            return `Win Rate: ${context.parsed.y.toFixed(1)}%`;
                          }
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100
                      }
                    }
                  }}
                />
              </Box>
            </CardBody>
          </Card>
        </GridItem>
        
        <GridItem>
          <Card bg={bgColor} boxShadow="md" height="100%">
            <CardHeader>
              <Heading size="sm">Trading Results</Heading>
            </CardHeader>
            <CardBody>
              <Flex justify="center" align="center" height="300px">
                <Box width="60%">
                  <Doughnut 
                    data={tradeResultsData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              const label = context.label || '';
                              const value = context.parsed || 0;
                              const total = context.dataset.data.reduce((a, b) => a + b, 0);
                              const percentage = ((value / total) * 100).toFixed(1);
                              return `${label}: ${value} (${percentage}%)`;
                            }
                          }
                        }
                      }
                    }}
                  />
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </GridItem>
      </Grid>
      
      {/* Pending Trades */}
      {data.pendingTrades > 0 && (
        <Card bg={bgColor} boxShadow="md" mb={8}>
          <CardHeader>
            <Heading size="sm">
              <InfoIcon mr={2} color="blue.500" />
              Pending Trades
            </Heading>
          </CardHeader>
          <CardBody>
            <Text>
              There are currently <Badge colorScheme="blue">{data.pendingTrades}</Badge> pending trade(s).
            </Text>
          </CardBody>
        </Card>
      )}
      
      <Text fontSize="sm" color={textColor} textAlign="center" mt={10}>
        Performance data refreshes automatically every 5 minutes. Last update: {new Date().toLocaleTimeString()}
      </Text>
    </Box>
  );
};

export default PerformanceMetrics; 