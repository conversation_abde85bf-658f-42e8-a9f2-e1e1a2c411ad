import { Box, Text, Flex, useColorModeValue } from '@chakra-ui/react';

const Logo = ({ size = 'md', withText = true }) => {
  const logoColor = useColorModeValue('brand.500', 'brand.300');
  const textColor = useColorModeValue('gray.800', 'white');

  // Size variants
  const sizesMap = {
    sm: {
      boxSize: '24px',
      textSize: 'md',
      spacing: 2,
    },
    md: {
      boxSize: '32px',
      textSize: 'xl',
      spacing: 2,
    },
    lg: {
      boxSize: '40px',
      textSize: '2xl',
      spacing: 3,
    },
  };

  const { boxSize, textSize, spacing } = sizesMap[size] || sizesMap.md;

  return (
    <Flex align="center" gap={spacing}>
      <Box
        width={boxSize}
        height={boxSize}
        borderRadius="md"
        bg={logoColor}
        position="relative"
        overflow="hidden"
      >
        {/* T letter stylized as a chart/graph */}
        <Box
          position="absolute"
          top="20%"
          left="50%"
          transform="translateX(-50%)"
          width="60%"
          height="70%"
          borderRadius="sm"
          bg="white"
        >
          <Box
            position="absolute"
            bottom="0"
            left="0"
            width="100%"
            height="4px"
            bg={logoColor}
          />
          <Box
            position="absolute"
            bottom="4px"
            left="calc(50% - 1px)"
            width="2px"
            height="calc(100% - 4px)"
            bg={logoColor}
          />

          {/* Chart lines */}
          <Box
            position="absolute"
            bottom="30%"
            left="15%"
            width="70%"
            height="1px"
            bg={logoColor}
          />
          <Box
            position="absolute"
            bottom="50%"
            left="15%"
            width="70%"
            height="1px"
            bg={logoColor}
          />
          <Box
            position="absolute"
            bottom="70%"
            left="15%"
            width="70%"
            height="1px"
            bg={logoColor}
          />
        </Box>
      </Box>

      {withText && (
        <Text
          fontSize={textSize}
          fontWeight="bold"
          letterSpacing="tight"
          color={textColor}
        >
          MISTER
          <Text as="span" color={logoColor}>Dashboard</Text>
        </Text>
      )}
    </Flex>
  );
};

export default Logo;