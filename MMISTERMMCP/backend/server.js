const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage (for development purposes)
// In production, you would use a database
let walletStatus = {
  connected: false,
  address: null,
  balance: { ada: 0 },
  tokens: []
};

let botStatus = {
  running: false,
  lastRun: null,
  logs: []
};

let settings = {
  general: {
    botEnabled: true,
    autoTradeEnabled: true,
    maxTradesPerDay: 5,
    maxTradeSize: 100,
  },
  wallets: [],
  portfolioAllocations: {
    ada: 30,
    meme: 15,
    defi: 20,
    major: 15,
    shard: 10,
    new: 10
  },
  tradingLimits: {
    minConfidence: 7,
    minTokenAge: 7,
    minLiquidity: 5000,
    maxImpact: 5,
  },
  apiKeys: {
    taptools: process.env.TAP_TOOLS_API_KEY || '',
    openai: process.env.OPENAI_API_KEY || '',
    blockfrost: process.env.BLOCKFROST_PROJECT_ID || '',
  },
  discord: {
    webhookUrl: '',
    notifications: {
      trades: true,
      analysis: true,
      errors: true,
    }
  },
};

// Encryption functions for securely handling seed phrases
const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';

const encryptSeedPhrase = (seedPhrase) => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(encryptionKey), iv);
  let encrypted = cipher.update(seedPhrase, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return { iv: iv.toString('hex'), encryptedData: encrypted };
};

const decryptSeedPhrase = (encryptedData, iv) => {
  const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(encryptionKey), Buffer.from(iv, 'hex'));
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

// Bot process management
let botProcess = null;

const startBot = () => {
  if (botProcess) {
    return { success: false, message: 'Bot is already running' };
  }

  try {
    // Start the bot as a child process
    botProcess = spawn('node', ['src/portfolio-swaps.ts'], {
      env: { ...process.env },
      stdio: 'pipe'
    });

    botProcess.stdout.on('data', (data) => {
      const logLine = data.toString().trim();
      botStatus.logs.push({
        timestamp: new Date().toISOString(),
        message: logLine,
        type: 'info'
      });

      // Keep only the last 1000 logs
      if (botStatus.logs.length > 1000) {
        botStatus.logs.shift();
      }
    });

    botProcess.stderr.on('data', (data) => {
      const logLine = data.toString().trim();
      botStatus.logs.push({
        timestamp: new Date().toISOString(),
        message: logLine,
        type: 'error'
      });
    });

    botProcess.on('close', (code) => {
      botStatus.running = false;
      botStatus.logs.push({
        timestamp: new Date().toISOString(),
        message: `Bot process exited with code ${code}`,
        type: 'system'
      });
      botProcess = null;
    });

    botStatus.running = true;
    botStatus.lastRun = new Date().toISOString();
    return { success: true, message: 'Bot started successfully' };
  } catch (error) {
    return { success: false, message: `Failed to start bot: ${error.message}` };
  }
};

const stopBot = () => {
  if (!botProcess) {
    return { success: false, message: 'Bot is not running' };
  }

  try {
    botProcess.kill();
    botStatus.running = false;
    botProcess = null;
    return { success: true, message: 'Bot stopped successfully' };
  } catch (error) {
    return { success: false, message: `Failed to stop bot: ${error.message}` };
  }
};

const restartBot = () => {
  stopBot();
  return startBot();
};

// API Routes
// Auth endpoints (simplified, in production you would use JWT)
app.post('/api/auth/login', (req, res) => {
  // Mock login, in production implement proper authentication
  res.json({
    success: true,
    token: 'mock-token',
    user: { id: 1, username: 'admin' }
  });
});

// Dashboard endpoints
app.get('/api/dashboard/summary', (req, res) => {
  // In production, you would pull real data from the trading bot
  const summary = {
    totalValue: walletStatus.balance.ada,
    valueChange: 2.5,
    totalTrades: 12,
    tradesChange: 4.2,
    successRate: 75.5,
    successRateChange: 5.3,
    profitLoss: 45.8,
    profitLossChange: 12.5
  };
  
  res.json(summary);
});

app.get('/api/dashboard/performance', (req, res) => {
  const { timeframe } = req.query;
  
  // Generate mock data for performance chart
  const dates = [];
  const values = [];
  const now = new Date();
  
  // Convert timeframe to days
  let days = 7;
  if (timeframe === '30d') days = 30;
  if (timeframe === '90d') days = 90;
  
  for (let i = days; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    dates.push(date.toISOString().split('T')[0]);
    
    // Generate a somewhat realistic performance curve
    const baseValue = 100;
    const randomFactor = Math.random() * 5 - 2.5; // Random between -2.5 and 2.5
    const trendFactor = (days - i) / 10; // Upward trend over time
    values.push(baseValue + randomFactor + trendFactor);
  }
  
  res.json({ dates, values });
});

app.get('/api/dashboard/portfolio-breakdown', (req, res) => {
  // In production, you would calculate this from wallet data
  res.json({
    allocation: settings.portfolioAllocations
  });
});

// Wallet endpoints
app.get('/api/wallet/status', (req, res) => {
  res.json(walletStatus);
});

app.post('/api/wallet/set', (req, res) => {
  const { address, seedPhrase } = req.body;
  
  if (!address || !seedPhrase) {
    return res.status(400).json({ success: false, message: 'Address and seed phrase are required' });
  }
  
  try {
    // Encrypt the seed phrase before storing
    const encrypted = encryptSeedPhrase(seedPhrase);
    
    // Update wallet status
    walletStatus = {
      connected: true,
      address,
      balance: { ada: 2.94 }, // In production, you would fetch the real balance
      tokens: [] // In production, you would fetch tokens from the wallet
    };
    
    // Check if wallet already exists in settings
    const existingWalletIndex = settings.wallets.findIndex(w => w.address === address);
    
    if (existingWalletIndex !== -1) {
      // Update existing wallet
      settings.wallets[existingWalletIndex].seedPhrase = encrypted.encryptedData;
      settings.wallets[existingWalletIndex].iv = encrypted.iv;
    } else {
      // Add new wallet
      const newWallet = {
        id: Date.now().toString(),
        name: `Wallet ${settings.wallets.length + 1}`,
        address,
        seedPhrase: encrypted.encryptedData,
        iv: encrypted.iv
      };
      
      settings.wallets.push(newWallet);
    }
    
    // In production, you would save this to a database
    // For the demo, we just keep it in memory
    
    res.json({ success: true, message: 'Wallet set successfully' });
  } catch (error) {
    res.status(500).json({ success: false, message: `Failed to set wallet: ${error.message}` });
  }
});

app.post('/api/wallet/validate-address', (req, res) => {
  const { address } = req.body;
  
  // In production, you would validate against Cardano address format
  const isValid = address && address.startsWith('addr') && 
    (address.length >= 103 && address.length <= 104);
  
  res.json({ valid: isValid });
});

// Settings endpoints
app.get('/api/settings', (req, res) => {
  // Remove sensitive data before sending
  const safeSettings = { ...settings };
  delete safeSettings.apiKeys;
  
  // Remove seed phrases from wallets
  safeSettings.wallets = settings.wallets.map(wallet => {
    const { seedPhrase, iv, ...safeWallet } = wallet;
    return safeWallet;
  });
  
  res.json(safeSettings);
});

app.put('/api/settings', (req, res) => {
  const { general, tradingLimits, discord } = req.body;
  
  if (general) settings.general = { ...settings.general, ...general };
  if (tradingLimits) settings.tradingLimits = { ...settings.tradingLimits, ...tradingLimits };
  if (discord) settings.discord = { ...settings.discord, ...discord };
  
  // In production, you would save this to a database
  
  res.json({ success: true, message: 'Settings updated successfully' });
});

app.get('/api/settings/wallets', (req, res) => {
  // Remove sensitive data before sending
  const safeWallets = settings.wallets.map(wallet => {
    const { seedPhrase, iv, ...safeWallet } = wallet;
    return safeWallet;
  });
  
  res.json(safeWallets);
});

app.post('/api/settings/wallets', (req, res) => {
  const { name, address, seedPhrase } = req.body;
  
  if (!address || !seedPhrase) {
    return res.status(400).json({ success: false, message: 'Address and seed phrase are required' });
  }
  
  try {
    // Encrypt the seed phrase
    const encrypted = encryptSeedPhrase(seedPhrase);
    
    // Create new wallet
    const newWallet = {
      id: Date.now().toString(),
      name: name || `Wallet ${settings.wallets.length + 1}`,
      address,
      seedPhrase: encrypted.encryptedData,
      iv: encrypted.iv
    };
    
    // Add to wallets list
    settings.wallets.push(newWallet);
    
    // Return safe version without seed phrase
    const { seedPhrase: _, iv: __, ...safeWallet } = newWallet;
    
    res.json({ success: true, data: safeWallet });
  } catch (error) {
    res.status(500).json({ success: false, message: `Failed to add wallet: ${error.message}` });
  }
});

app.delete('/api/settings/wallets/:id', (req, res) => {
  const { id } = req.params;
  
  const walletIndex = settings.wallets.findIndex(w => w.id === id);
  
  if (walletIndex === -1) {
    return res.status(404).json({ success: false, message: 'Wallet not found' });
  }
  
  // Remove wallet
  settings.wallets.splice(walletIndex, 1);
  
  // If deleted wallet was the active one, disconnect
  if (walletStatus.address === settings.wallets[walletIndex].address) {
    walletStatus = {
      connected: false,
      address: null,
      balance: { ada: 0 },
      tokens: []
    };
  }
  
  res.json({ success: true, message: 'Wallet removed successfully' });
});

app.get('/api/settings/portfolio-allocations', (req, res) => {
  res.json(settings.portfolioAllocations);
});

app.put('/api/settings/portfolio-allocations', (req, res) => {
  settings.portfolioAllocations = { ...settings.portfolioAllocations, ...req.body };
  res.json({ success: true, message: 'Portfolio allocations updated successfully' });
});

// Bot control endpoints
app.get('/api/bot/status', (req, res) => {
  res.json({
    running: botStatus.running,
    lastRun: botStatus.lastRun
  });
});

app.post('/api/bot/start', (req, res) => {
  const result = startBot();
  res.json(result);
});

app.post('/api/bot/stop', (req, res) => {
  const result = stopBot();
  res.json(result);
});

app.post('/api/bot/restart', (req, res) => {
  const result = restartBot();
  res.json(result);
});

app.get('/api/bot/logs', (req, res) => {
  const limit = parseInt(req.query.limit) || 100;
  const logs = botStatus.logs.slice(-limit);
  res.json(logs);
});

// Trading data endpoints
app.get('/api/bot/trading-data', (req, res) => {
  // In production, you would get this from your running bot
  res.json({
    lastAnalyzedTokens: 10,
    activeTraders: 2,
    pendingTrades: 1,
    completedTrades: 15
  });
});

app.get('/api/bot/analyzed-tokens', (req, res) => {
  const limit = parseInt(req.query.limit) || 5;
  
  // In production, you would get this from your running bot
  const analyzedTokens = [
    { ticker: 'WMTX', recommendation: 'hold', confidence: 6, price: 0.268 },
    { ticker: 'SNEK', recommendation: 'sell', confidence: 8, price: 0.00446 },
    { ticker: 'IAG', recommendation: 'hold', confidence: 7, price: 0.365 },
    { ticker: 'HUNT', recommendation: 'buy', confidence: 7, price: 0.155 },
    { ticker: 'BODEGA', recommendation: 'sell', confidence: 6, price: 0.204 },
    { ticker: 'MIN', recommendation: 'buy', confidence: 8, price: 0.421 },
    { ticker: 'ROLL', recommendation: 'hold', confidence: 5, price: 0.072 },
    { ticker: 'COCK', recommendation: 'buy', confidence: 7, price: 0.00021 }
  ];
  
  res.json(analyzedTokens.slice(0, limit));
});

// Trade endpoints
app.get('/api/trades/recent', (req, res) => {
  const limit = parseInt(req.query.limit) || 5;
  
  // In production, you would get this from your trading history
  const recentTrades = [
    { ticker: 'SNEK', direction: 'sell', amount: 50, status: 'completed', timestamp: new Date().toISOString() },
    { ticker: 'HUNT', direction: 'sell', amount: 50, status: 'pending', timestamp: new Date(Date.now() - 3600000).toISOString() },
    { ticker: 'WMTX', direction: 'buy', amount: 75, status: 'failed', timestamp: new Date(Date.now() - 7200000).toISOString() },
    { ticker: 'BTN', direction: 'buy', amount: 40, status: 'completed', timestamp: new Date(Date.now() - 10800000).toISOString() },
    { ticker: 'HOSKY', direction: 'sell', amount: 100, status: 'completed', timestamp: new Date(Date.now() - 14400000).toISOString() },
    { ticker: 'MIN', direction: 'buy', amount: 60, status: 'completed', timestamp: new Date(Date.now() - 18000000).toISOString() },
    { ticker: 'COCK', direction: 'buy', amount: 30, status: 'completed', timestamp: new Date(Date.now() - 21600000).toISOString() }
  ];
  
  res.json(recentTrades.slice(0, limit));
});

// Analysis endpoints
app.get('/api/analysis/tokens/top-volume', (req, res) => {
  const count = parseInt(req.query.count) || 25;
  
  // In production, you would get this from TapTools API
  // For now, we'll return mock data
  const topVolumeTokens = [
    { ticker: 'WMTX', unit: 'your-unit-here', price: 0.268, volume: 457533 },
    { ticker: 'SNEK', unit: 'your-unit-here', price: 0.00446, volume: 398479 },
    { ticker: 'IAG', unit: 'your-unit-here', price: 0.365, volume: 362789 },
    { ticker: 'HUNT', unit: 'your-unit-here', price: 0.155, volume: 245623 },
    { ticker: 'BODEGA', unit: 'your-unit-here', price: 0.204, volume: 198742 },
    { ticker: 'MIN', unit: 'your-unit-here', price: 0.421, volume: 187532 },
    { ticker: 'ROLL', unit: 'your-unit-here', price: 0.072, volume: 176543 },
    { ticker: 'COCK', unit: 'your-unit-here', price: 0.00021, volume: 165432 },
    { ticker: 'SUGR', unit: 'your-unit-here', price: 0.00076, volume: 154321 },
    { ticker: 'SYNTH', unit: 'your-unit-here', price: 0.18, volume: 143210 },
    { ticker: 'COPI', unit: 'your-unit-here', price: 0.032, volume: 132109 },
    { ticker: 'DEDI', unit: 'your-unit-here', price: 0.0018, volume: 121098 },
    { ticker: 'TOKE', unit: 'your-unit-here', price: 0.0052, volume: 110987 },
    { ticker: 'YAP', unit: 'your-unit-here', price: 0.00045, volume: 100876 },
    { ticker: 'TITAN', unit: 'your-unit-here', price: 0.076, volume: 90765 },
    { ticker: 'BSKT', unit: 'your-unit-here', price: 0.032, volume: 80654 },
    { ticker: 'RAGE', unit: 'your-unit-here', price: 0.00024, volume: 70543 },
    { ticker: 'HOSKY', unit: 'your-unit-here', price: 0.00035, volume: 60432 },
    { ticker: 'CHRTR', unit: 'your-unit-here', price: 0.0105, volume: 50321 },
    { ticker: 'BOO', unit: 'your-unit-here', price: 0.00015, volume: 40210 },
    { ticker: 'CHRY', unit: 'your-unit-here', price: 0.0023, volume: 30109 },
    { ticker: 'CROWN', unit: 'your-unit-here', price: 0.0047, volume: 20098 },
    { ticker: 'CLAY', unit: 'your-unit-here', price: 0.0039, volume: 10987 },
    { ticker: 'DRIP', unit: 'your-unit-here', price: 0.0018, volume: 9876 },
    { ticker: 'WINKY', unit: 'your-unit-here', price: 0.00008, volume: 8765 }
  ];
  
  res.json(topVolumeTokens.slice(0, count));
});

// Ensure data directory exists
const ensureDataDirExists = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Get performance metrics from the trading bot
app.get('/api/bot/performance-metrics', (req, res) => {
  try {
    ensureDataDirExists();
    
    // Path to the performance metrics file
    const performanceFilePath = path.join(process.cwd(), 'data', 'performance.json');
    
    // Check if file exists
    if (!fs.existsSync(performanceFilePath)) {
      // Return default metrics if file doesn't exist
      return res.json({
        totalTrades: 0,
        successfulTrades: 0,
        failedTrades: 0,
        pendingTrades: 0,
        winRate: 0,
        averageProfitLoss: 0,
        totalProfitLoss: 0,
        bestTrade: {
          token: '',
          profitLossPercentage: 0,
          timestamp: new Date().toISOString()
        },
        worstTrade: {
          token: '',
          profitLossPercentage: 0,
          timestamp: new Date().toISOString()
        },
        tokenPerformance: {},
        timeframePerformance: {
          daily: 0,
          weekly: 0,
          monthly: 0
        }
      });
    }
    
    // Read performance metrics from file
    const performanceData = fs.readFileSync(performanceFilePath, 'utf8');
    const metrics = JSON.parse(performanceData);
    
    res.json(metrics);
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    res.status(500).json({ success: false, message: `Failed to get performance metrics: ${error.message}` });
  }
});

// Get all trades
app.get('/api/trades', (req, res) => {
  try {
    ensureDataDirExists();
    
    // Path to the trades file
    const tradesFilePath = path.join(process.cwd(), 'data', 'trades.json');
    
    // Check if file exists
    if (!fs.existsSync(tradesFilePath)) {
      return res.json([]);
    }
    
    // Read trades from file
    const tradesData = fs.readFileSync(tradesFilePath, 'utf8');
    const trades = JSON.parse(tradesData);
    
    // Apply filters if provided
    let filteredTrades = [...trades];
    
    // Filter by token if provided
    if (req.query.token) {
      filteredTrades = filteredTrades.filter(
        trade => trade.ticker === req.query.token || trade.token === req.query.token
      );
    }
    
    // Filter by status if provided
    if (req.query.status && ['completed', 'pending', 'failed'].includes(req.query.status)) {
      filteredTrades = filteredTrades.filter(trade => trade.status === req.query.status);
    }
    
    // Filter by direction if provided
    if (req.query.direction && ['buy', 'sell'].includes(req.query.direction)) {
      filteredTrades = filteredTrades.filter(trade => trade.direction === req.query.direction);
    }
    
    // Sort by timestamp (newest first)
    filteredTrades.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    // Apply limit if provided
    const limit = parseInt(req.query.limit) || filteredTrades.length;
    filteredTrades = filteredTrades.slice(0, limit);
    
    res.json(filteredTrades);
  } catch (error) {
    console.error('Error getting trades:', error);
    res.status(500).json({ success: false, message: `Failed to get trades: ${error.message}` });
  }
});

// Get token performance data
app.get('/api/analysis/token-performance/:token', (req, res) => {
  try {
    ensureDataDirExists();
    
    const { token } = req.params;
    
    if (!token) {
      return res.status(400).json({ success: false, message: 'Token is required' });
    }
    
    // Path to the performance metrics file
    const performanceFilePath = path.join(process.cwd(), 'data', 'performance.json');
    
    // Check if file exists
    if (!fs.existsSync(performanceFilePath)) {
      return res.json({
        token,
        trades: 0,
        successfulTrades: 0,
        winRate: 0,
        averageProfitLoss: 0,
        history: []
      });
    }
    
    // Read performance metrics from file
    const performanceData = fs.readFileSync(performanceFilePath, 'utf8');
    const metrics = JSON.parse(performanceData);
    
    // Get token performance
    const tokenPerformance = metrics.tokenPerformance[token] || {
      trades: 0,
      successfulTrades: 0,
      winRate: 0,
      averageProfitLoss: 0
    };
    
    // Get token trades
    const tradesFilePath = path.join(process.cwd(), 'data', 'trades.json');
    let tokenTrades = [];
    
    if (fs.existsSync(tradesFilePath)) {
      const tradesData = fs.readFileSync(tradesFilePath, 'utf8');
      const trades = JSON.parse(tradesData);
      
      // Get token trades
      tokenTrades = trades.filter(trade => trade.ticker === token || trade.token === token);
      
      // Sort by timestamp (newest first)
      tokenTrades.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      // Apply limit
      const limit = parseInt(req.query.limit) || 10;
      tokenTrades = tokenTrades.slice(0, limit);
    }
    
    // Return token performance with trade history
    res.json({
      token,
      ...tokenPerformance,
      history: tokenTrades
    });
  } catch (error) {
    console.error(`Error getting token performance for ${req.params.token}:`, error);
    res.status(500).json({ success: false, message: `Failed to get token performance: ${error.message}` });
  }
});

// Update the API endpoint for bot performance metrics
app.get('/api/bot/getPerformanceMetrics', (req, res) => {
  try {
    ensureDataDirExists();
    
    // Path to the performance metrics file
    const performanceFilePath = path.join(process.cwd(), 'data', 'performance.json');
    
    // Check if file exists
    if (!fs.existsSync(performanceFilePath)) {
      // Return default metrics if file doesn't exist
      return res.json({
        totalTrades: 0,
        successfulTrades: 0,
        failedTrades: 0,
        pendingTrades: 0,
        winRate: 0,
        averageProfitLoss: 0,
        totalProfitLoss: 0,
        bestTrade: {
          token: '',
          profitLossPercentage: 0,
          timestamp: new Date().toISOString()
        },
        worstTrade: {
          token: '',
          profitLossPercentage: 0,
          timestamp: new Date().toISOString()
        },
        tokenPerformance: {},
        timeframePerformance: {
          daily: 0,
          weekly: 0,
          monthly: 0
        }
      });
    }
    
    // Read performance metrics from file
    const performanceData = fs.readFileSync(performanceFilePath, 'utf8');
    const metrics = JSON.parse(performanceData);
    
    res.json(metrics);
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    res.status(500).json({ success: false, message: `Failed to get performance metrics: ${error.message}` });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
}); 