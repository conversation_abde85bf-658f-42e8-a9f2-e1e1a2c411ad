{"displayName": "Default Trader", "riskTolerance": {"level": "moderate", "customMultipliers": {"lowRisk": 0.15, "mediumRisk": 0.1, "highRisk": 0.05}}, "positionSizing": {"baseMultiplier": 1, "maxPositionPercent": 0.25, "minPositionAda": 5, "maxPositionAda": 500, "confidenceMultipliers": {"highConfidence": 1.5, "mediumConfidence": 1, "lowConfidence": 0.5}, "confluenceMultipliers": {"highConfluence": 1.3, "mediumConfluence": 1, "lowConfluence": 0.7}}, "portfolioLimits": {"categories": {"meme": 0.3, "defi": 0.4, "major": 0.5, "new": 0.2}, "maxCorrelatedTokens": 0.2, "maxSingleToken": 0.15}, "tradeExecution": {"confidenceThreshold": 10, "enableProfessionalFilter": true, "enableCorrelationExits": true, "stopLoss": {"enabled": true, "defaultPercent": 0.1, "trailingEnabled": false}, "takeProfit": {"enabled": true, "defaultPercent": 0.2, "partialEnabled": false}}, "notifications": {"discord": {"enabled": true}, "email": {"enabled": false}, "webhook": {"enabled": false}}, "advanced": {"enableMultiTimeframeAnalysis": true, "enableTwitterSentiment": true, "enableRelativeStrength": true, "rateLimiting": {"maxTradesPerHour": 10, "maxTradesPerDay": 50, "cooldownMinutes": 5}}, "userId": "default-trader", "createdAt": "2025-07-05T20:56:41.552Z", "updatedAt": "2025-07-05T20:56:41.552Z", "lastUsed": "2025-07-05T20:56:41.552Z"}