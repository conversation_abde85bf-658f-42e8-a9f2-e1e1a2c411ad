# MISTER-MCP Server (Simplified)

A simplified MCP (Model Context Protocol) server for controlling the MISTER Autonomous Cardano DEX Trading Agent.

## Overview

This MCP server provides a simple interface for AI agents to control the MISTER trading agent. It allows agents to start the trading bot, check its status, and retrieve trading results.

## Features

- **Start Trading**: Start the MISTER trading agent
- **Check Status**: Check if the agent is running and when it was last started
- **Get Results**: Retrieve the results of the most recent trading run
- **Stop Trading**: Stop the MISTER trading agent

## Installation

1. Install dependencies:
   ```bash
   cd mcp-server
   npm install
   ```

## Usage

### Starting the MCP Server

To start the MCP server:

```bash
cd mcp-server
npm start
```

### Connecting to the Server

#### For IDE Integration

To connect your IDE (like Cursor or Windsurf) to this MCP server:

1. Add a new MCP server in your IDE settings
2. Name: `mister-ada-mcp`
3. Command: `node`
4. Args: `/absolute/path/to/talos-dexter-integration/mcp-server/simple-server.js`

Replace `/absolute/path/to/` with the actual path to your project directory.

## Available Tools

The MCP server provides the following tools:

1. **startTrading**: Start the trading bot
   - No parameters required
   - Returns: `{ success: boolean, message: string, pid: number }`

2. **checkStatus**: Check the status of the trading bot
   - No parameters required
   - Returns: `{ running: boolean, lastRun: string, lastResults: object, pid: number }`

3. **getResults**: Get the results of the most recent trading run
   - No parameters required
   - Returns: Trading results including portfolio information and trade decisions

4. **stopTrading**: Stop the trading bot
   - No parameters required
   - Returns: `{ success: boolean, message: string }`

## Example Prompts for Agents

Here are some example prompts to use with your AI agent:

- "Start the trading bot"
- "Is the trading bot running?"
- "When was the last time the trading bot ran?"
- "What were the results of the last trading run?"
- "Stop the trading bot"
- "What tokens did the bot analyze?"
- "What trading decisions did the bot make?"

## Best Practices

### For Agents

- **Check Status First**: Always check if the bot is already running before attempting to start it
- **Monitor Results**: Periodically check for new trading decisions while the bot is running
- **Always Stop Properly**: Always use the stopTrading tool to properly stop the bot when finished
- **Handle Errors**: Be prepared to handle potential errors from the MCP tools
- **Resource Awareness**: Be mindful of system resources when running the bot for extended periods

### For Developers

- **Logging**: The bot outputs to a log file that is parsed for results; don't modify this logging format
- **Process Management**: The MCP server tracks the bot's process ID to enable proper stopping
- **Error Handling**: The MCP server includes fallback implementations if direct integration fails
- **Configuration**: Adjust the polling interval in simple-server.js if needed (default is 5 seconds)
- **Security**: The MCP server is designed for local use; implement additional security if exposing to a network
