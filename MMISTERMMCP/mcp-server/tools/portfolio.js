import { z } from "zod";
import path from "path";
import { fileURLToPath } from "url";

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the trading bot functions
// We need to go up two directories to reach the src folder
const projectRoot = path.resolve(__dirname, "../../");
const srcPath = path.join(projectRoot, "src");

// Dynamically import the trading bot functions
// Import from the actual file structure of the Talos Dexter trading bot
let portfolioManager;
try {
  // Try to import the portfolio-swaps.ts file which contains portfolio functions
  const module = await import(path.join(srcPath, "portfolio-swaps.ts"));
  portfolioManager = module.default || module;
} catch (error) {
  console.error("Error importing portfolio manager:", error);
  // Fallback to mock implementation if import fails
  portfolioManager = {
    getPortfolioStatus: async () => {
      return {
        ada: { amount: 100, value: 100 },
        meme: { amount: 0, value: 0 },
        defi: { amount: 0, value: 0 },
        major: { amount: 0, value: 0 },
        shard: { amount: 0, value: 0 },
        new: { amount: 0, value: 0 },
        totalValue: 100
      };
    },
    getTokenHoldings: async (token) => {
      return {
        token,
        amount: 0,
        value: 0
      };
    },
    getTradeHistory: async (token, limit) => {
      return [];
    }
  };
}

export const portfolioTools = {
  getPortfolioStatus: {
    schema: {},
    handler: async () => {
      try {
        const portfolioData = await portfolioManager.getPortfolioStatus();
        return {
          content: [{
            type: "text",
            text: JSON.stringify(portfolioData, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving portfolio status: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },

  getTokenHoldings: {
    schema: {
      token: z.string().describe("The token symbol to get holdings for")
    },
    handler: async ({ token }) => {
      try {
        const holdings = await portfolioManager.getTokenHoldings(token);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(holdings, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving token holdings: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },

  getTradeHistory: {
    schema: {
      token: z.string().optional().describe("Optional token symbol to filter trade history"),
      limit: z.number().optional().default(10).describe("Maximum number of trades to return")
    },
    handler: async ({ token, limit }) => {
      try {
        const tradeHistory = await portfolioManager.getTradeHistory(token, limit);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(tradeHistory, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving trade history: ${error.message}`
          }],
          isError: true
        };
      }
    }
  }
};
