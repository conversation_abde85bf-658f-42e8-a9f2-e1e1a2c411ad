import { z } from "zod";
import path from "path";
import { fileURLToPath } from "url";

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the trading bot functions
const projectRoot = path.resolve(__dirname, "../../");
const srcPath = path.join(projectRoot, "src");

// Dynamically import the trading bot functions
let tradingManager;
try {
  // Try to import the portfolio-swaps.ts file
  const module = await import(path.join(srcPath, "portfolio-swaps.ts"));
  tradingManager = module.default || module;
} catch (error) {
  console.error("Error importing trading manager:", error);
  // Fallback to mock implementation if import fails
  tradingManager = {
    executeTrade: async (direction, amount, token, ticker) => {
      return {
        success: true,
        transaction: {
          id: "mock-transaction-id",
          direction,
          amount,
          token,
          ticker,
          timestamp: new Date().toISOString()
        }
      };
    },
    setTradeParameters: async (params) => {
      return {
        success: true,
        message: "Trade parameters updated successfully",
        params
      };
    },
    toggleAutoTrading: async (enabled) => {
      return {
        success: true,
        autoTradingEnabled: enabled,
        message: `Auto trading ${enabled ? "enabled" : "disabled"}`
      };
    }
  };
}

export const tradingTools = {
  executeTrade: {
    schema: {
      direction: z.enum(["buy", "sell"]).describe("Trade direction (buy or sell)"),
      amount: z.number().positive().describe("Amount of ADA to trade"),
      token: z.string().describe("Token unit identifier"),
      ticker: z.string().describe("Token ticker symbol")
    },
    handler: async ({ direction, amount, token, ticker }) => {
      try {
        const result = await tradingManager.executeTrade(direction, amount, token, ticker);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(result, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error executing trade: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },

  setTradeParameters: {
    schema: {
      maxTradesPerDay: z.number().optional().describe("Maximum number of trades per day"),
      maxTradeSize: z.number().optional().describe("Maximum size of a single trade in ADA"),
      minConfidence: z.number().optional().describe("Minimum confidence score required for a trade (1-10)"),
      minTokenAge: z.number().optional().describe("Minimum age of token in days"),
      minLiquidity: z.number().optional().describe("Minimum liquidity required for a token"),
      maxImpact: z.number().optional().describe("Maximum price impact allowed for a trade")
    },
    handler: async (params) => {
      try {
        const result = await tradingManager.setTradeParameters(params);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(result, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error setting trade parameters: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },

  toggleAutoTrading: {
    schema: {
      enabled: z.boolean().describe("Whether auto trading should be enabled")
    },
    handler: async ({ enabled }) => {
      try {
        const result = await tradingManager.toggleAutoTrading(enabled);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(result, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error toggling auto trading: ${error.message}`
          }],
          isError: true
        };
      }
    }
  }
};
