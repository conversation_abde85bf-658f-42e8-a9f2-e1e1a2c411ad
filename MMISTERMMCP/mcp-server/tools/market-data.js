import { z } from "zod";
import path from "path";
import { fileURLToPath } from "url";

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the trading bot functions
const projectRoot = path.resolve(__dirname, "../../");
const srcPath = path.join(projectRoot, "src");

// Dynamically import the trading bot functions
let marketDataManager;
try {
  // Try to import the portfolio-swaps.ts file which contains market data functions
  const module = await import(path.join(srcPath, "portfolio-swaps.ts"));
  marketDataManager = module.default || module;
} catch (error) {
  console.error("Error importing market data manager:", error);
  // Fallback to mock implementation if import fails
  marketDataManager = {
    getTopTokens: async (limit) => {
      return [
        { symbol: "ADA", name: "Cardano", price: 1.0, volume: 1000000 },
        { symbol: "SNE<PERSON>", name: "<PERSON>ne<PERSON>", price: 0.001, volume: 500000 },
        { symbol: "HOSKY", name: "<PERSON><PERSON>", price: 0.0001, volume: 300000 }
      ].slice(0, limit);
    },
    getTokenData: async (token) => {
      return {
        symbol: token,
        name: token,
        price: 1.0,
        volume: 100000,
        marketCap: 1000000,
        change24h: 0.5,
        change7d: 1.2
      };
    },
    getMarketTrends: async () => {
      return {
        overallTrend: "bullish",
        topGainers: [
          { symbol: "TOKEN1", change: 5.2 },
          { symbol: "TOKEN2", change: 3.1 }
        ],
        topLosers: [
          { symbol: "TOKEN3", change: -4.5 },
          { symbol: "TOKEN4", change: -2.8 }
        ],
        volumeLeaders: [
          { symbol: "TOKEN5", volume: 2000000 },
          { symbol: "TOKEN6", volume: 1500000 }
        ]
      };
    }
  };
}

export const marketDataTools = {
  getTopTokens: {
    schema: {
      limit: z.number().optional().default(10).describe("Maximum number of tokens to return")
    },
    handler: async ({ limit }) => {
      try {
        const tokens = await marketDataManager.getTopTokens(limit);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(tokens, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving top tokens: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },

  getTokenData: {
    schema: {
      token: z.string().describe("The token symbol to get data for")
    },
    handler: async ({ token }) => {
      try {
        const tokenData = await marketDataManager.getTokenData(token);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(tokenData, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving token data: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },

  getMarketTrends: {
    schema: {},
    handler: async () => {
      try {
        const trends = await marketDataManager.getMarketTrends();
        return {
          content: [{
            type: "text",
            text: JSON.stringify(trends, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving market trends: ${error.message}`
          }],
          isError: true
        };
      }
    }
  }
};
