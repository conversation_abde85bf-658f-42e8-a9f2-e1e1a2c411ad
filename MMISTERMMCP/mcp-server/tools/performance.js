import { z } from "zod";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { getAllTrades, getTradesByToken } from "../../src/database.js";

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "../../");

// Create a performance manager to handle analytics
const createPerformanceManager = () => {
  return {
    /**
     * Get overall performance metrics
     */
    getPerformanceMetrics: async () => {
      try {
        // Get all trades from the database
        const allTrades = getAllTrades();
        
        if (!allTrades || allTrades.length === 0) {
          return {
            totalTrades: 0,
            successfulTrades: 0,
            failedTrades: 0,
            winRate: 0,
            totalProfitLoss: 0,
            averageProfitLoss: 0,
            bestTrade: null,
            worstTrade: null,
            timeframePerformance: {
              daily: 0,
              weekly: 0,
              monthly: 0
            },
            tokenPerformance: []
          };
        }
        
        // Calculate performance metrics
        const completedTrades = allTrades.filter(trade => 
          trade.performanceTracking && trade.performanceTracking.exitPrice
        );
        
        const successfulTrades = completedTrades.filter(trade => 
          trade.performanceTracking && trade.performanceTracking.success
        );
        
        const totalProfitLoss = completedTrades.reduce((sum, trade) => 
          sum + (trade.performanceTracking?.profitLoss || 0), 0
        );
        
        // Get best and worst trades
        const sortedTrades = [...completedTrades].sort((a, b) => 
          (b.performanceTracking?.profitLoss || 0) - (a.performanceTracking?.profitLoss || 0)
        );
        
        const bestTrade = sortedTrades.length > 0 ? sortedTrades[0] : null;
        const worstTrade = sortedTrades.length > 0 ? sortedTrades[sortedTrades.length - 1] : null;
        
        // Calculate timeframe performance
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        const dailyTrades = completedTrades.filter(trade => 
          new Date(trade.timestamp) >= oneDayAgo
        );
        
        const weeklyTrades = completedTrades.filter(trade => 
          new Date(trade.timestamp) >= oneWeekAgo
        );
        
        const monthlyTrades = completedTrades.filter(trade => 
          new Date(trade.timestamp) >= oneMonthAgo
        );
        
        const dailyProfitLoss = dailyTrades.reduce((sum, trade) => 
          sum + (trade.performanceTracking?.profitLoss || 0), 0
        );
        
        const weeklyProfitLoss = weeklyTrades.reduce((sum, trade) => 
          sum + (trade.performanceTracking?.profitLoss || 0), 0
        );
        
        const monthlyProfitLoss = monthlyTrades.reduce((sum, trade) => 
          sum + (trade.performanceTracking?.profitLoss || 0), 0
        );
        
        // Calculate token performance
        const tokenMap = new Map();
        
        completedTrades.forEach(trade => {
          if (!tokenMap.has(trade.ticker)) {
            tokenMap.set(trade.ticker, {
              ticker: trade.ticker,
              totalTrades: 0,
              successfulTrades: 0,
              totalProfitLoss: 0
            });
          }
          
          const tokenStats = tokenMap.get(trade.ticker);
          tokenStats.totalTrades++;
          
          if (trade.performanceTracking?.success) {
            tokenStats.successfulTrades++;
          }
          
          tokenStats.totalProfitLoss += (trade.performanceTracking?.profitLoss || 0);
        });
        
        const tokenPerformance = Array.from(tokenMap.values())
          .map(token => ({
            ...token,
            winRate: token.totalTrades > 0 ? (token.successfulTrades / token.totalTrades) * 100 : 0,
            averageProfitLoss: token.totalTrades > 0 ? token.totalProfitLoss / token.totalTrades : 0
          }))
          .sort((a, b) => b.totalTrades - a.totalTrades);
        
        return {
          totalTrades: completedTrades.length,
          successfulTrades: successfulTrades.length,
          failedTrades: completedTrades.length - successfulTrades.length,
          winRate: completedTrades.length > 0 ? (successfulTrades.length / completedTrades.length) * 100 : 0,
          totalProfitLoss,
          averageProfitLoss: completedTrades.length > 0 ? totalProfitLoss / completedTrades.length : 0,
          bestTrade,
          worstTrade,
          timeframePerformance: {
            daily: dailyProfitLoss,
            weekly: weeklyProfitLoss,
            monthly: monthlyProfitLoss
          },
          tokenPerformance: tokenPerformance.slice(0, 10) // Top 10 tokens by trade count
        };
      } catch (error) {
        console.error("Error calculating performance metrics:", error);
        throw error;
      }
    },
    
    /**
     * Get token-specific performance metrics
     */
    getTokenPerformance: async (ticker) => {
      try {
        // Get trades for the specific token
        const tokenTrades = getTradesByToken(ticker);
        
        if (!tokenTrades || tokenTrades.length === 0) {
          return {
            ticker,
            totalTrades: 0,
            successfulTrades: 0,
            failedTrades: 0,
            winRate: 0,
            totalProfitLoss: 0,
            averageProfitLoss: 0,
            bestTrade: null,
            worstTrade: null,
            holdTimes: {
              average: 0,
              min: 0,
              max: 0
            },
            trades: []
          };
        }
        
        // Calculate performance metrics
        const completedTrades = tokenTrades.filter(trade => 
          trade.performanceTracking && trade.performanceTracking.exitPrice
        );
        
        const successfulTrades = completedTrades.filter(trade => 
          trade.performanceTracking && trade.performanceTracking.success
        );
        
        const totalProfitLoss = completedTrades.reduce((sum, trade) => 
          sum + (trade.performanceTracking?.profitLoss || 0), 0
        );
        
        // Get best and worst trades
        const sortedTrades = [...completedTrades].sort((a, b) => 
          (b.performanceTracking?.profitLoss || 0) - (a.performanceTracking?.profitLoss || 0)
        );
        
        const bestTrade = sortedTrades.length > 0 ? sortedTrades[0] : null;
        const worstTrade = sortedTrades.length > 0 ? sortedTrades[sortedTrades.length - 1] : null;
        
        // Calculate hold times
        const holdTimes = completedTrades
          .filter(trade => trade.performanceTracking?.holdDuration !== undefined)
          .map(trade => trade.performanceTracking.holdDuration);
        
        const averageHoldTime = holdTimes.length > 0 
          ? holdTimes.reduce((sum, time) => sum + time, 0) / holdTimes.length 
          : 0;
        
        const minHoldTime = holdTimes.length > 0 
          ? Math.min(...holdTimes) 
          : 0;
        
        const maxHoldTime = holdTimes.length > 0 
          ? Math.max(...holdTimes) 
          : 0;
        
        return {
          ticker,
          totalTrades: completedTrades.length,
          successfulTrades: successfulTrades.length,
          failedTrades: completedTrades.length - successfulTrades.length,
          winRate: completedTrades.length > 0 ? (successfulTrades.length / completedTrades.length) * 100 : 0,
          totalProfitLoss,
          averageProfitLoss: completedTrades.length > 0 ? totalProfitLoss / completedTrades.length : 0,
          bestTrade,
          worstTrade,
          holdTimes: {
            average: averageHoldTime,
            min: minHoldTime,
            max: maxHoldTime
          },
          trades: completedTrades.map(trade => ({
            id: trade.id,
            timestamp: trade.timestamp,
            direction: trade.direction,
            amount: trade.amount,
            price: trade.price,
            exitPrice: trade.performanceTracking?.exitPrice,
            exitTimestamp: trade.performanceTracking?.exitTimestamp,
            profitLoss: trade.performanceTracking?.profitLoss,
            profitLossPercentage: trade.performanceTracking?.profitLossPercentage,
            holdDuration: trade.performanceTracking?.holdDuration,
            success: trade.performanceTracking?.success
          }))
        };
      } catch (error) {
        console.error(`Error calculating performance metrics for ${ticker}:`, error);
        throw error;
      }
    },
    
    /**
     * Get performance history for charting
     */
    getPerformanceHistory: async (timeframe = '30d') => {
      try {
        // Get all trades from the database
        const allTrades = getAllTrades();
        
        if (!allTrades || allTrades.length === 0) {
          return {
            labels: [],
            datasets: []
          };
        }
        
        // Determine date range based on timeframe
        const now = new Date();
        let startDate;
        let interval;
        
        switch (timeframe) {
          case '7d':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            interval = 'day';
            break;
          case '30d':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            interval = 'day';
            break;
          case '90d':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            interval = 'week';
            break;
          case '1y':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            interval = 'month';
            break;
          default:
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            interval = 'day';
        }
        
        // Filter trades within the timeframe
        const filteredTrades = allTrades.filter(trade => 
          new Date(trade.timestamp) >= startDate
        );
        
        // Sort trades by timestamp
        const sortedTrades = [...filteredTrades].sort((a, b) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );
        
        // Generate data points based on interval
        const dataPoints = [];
        let currentDate = new Date(startDate);
        
        while (currentDate <= now) {
          const dateLabel = currentDate.toISOString().split('T')[0];
          
          // Find trades that occurred on or before this date
          const tradesUntilDate = sortedTrades.filter(trade => 
            new Date(trade.timestamp) <= currentDate
          );
          
          // Calculate cumulative profit/loss
          const cumulativeProfitLoss = tradesUntilDate.reduce((sum, trade) => 
            sum + (trade.performanceTracking?.profitLoss || 0), 0
          );
          
          dataPoints.push({
            date: dateLabel,
            value: cumulativeProfitLoss
          });
          
          // Increment date based on interval
          switch (interval) {
            case 'day':
              currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
              break;
            case 'week':
              currentDate = new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);
              break;
            case 'month':
              currentDate.setMonth(currentDate.getMonth() + 1);
              break;
          }
        }
        
        return {
          labels: dataPoints.map(point => point.date),
          datasets: [
            {
              label: 'Cumulative Profit/Loss (ADA)',
              data: dataPoints.map(point => point.value)
            }
          ]
        };
      } catch (error) {
        console.error("Error calculating performance history:", error);
        throw error;
      }
    },
    
    /**
     * Get risk management statistics
     */
    getRiskManagementStats: async () => {
      try {
        // Get all trades from the database
        const allTrades = getAllTrades();
        
        if (!allTrades || allTrades.length === 0) {
          return {
            riskCategoryDistribution: {
              ultra_high: 0,
              high: 0,
              medium: 0,
              low: 0
            },
            stopLossHits: 0,
            targetHits: 0,
            averageRiskRewardRatio: 0,
            timeBasedExits: 0
          };
        }
        
        // Count trades by risk category
        const riskCategories = {
          ultra_high: 0,
          high: 0,
          medium: 0,
          low: 0
        };
        
        allTrades.forEach(trade => {
          if (trade.riskManagement?.riskCategory) {
            riskCategories[trade.riskManagement.riskCategory]++;
          } else {
            riskCategories.medium++; // Default to medium if not specified
          }
        });
        
        // Count stop loss hits, target hits, and time-based exits
        let stopLossHits = 0;
        let targetHits = 0;
        let timeBasedExits = 0;
        let riskRewardRatios = [];
        
        allTrades.forEach(trade => {
          if (trade.performanceTracking?.exitReason === 'stop_loss') {
            stopLossHits++;
          } else if (trade.performanceTracking?.exitReason === 'target_reached') {
            targetHits++;
          } else if (trade.performanceTracking?.exitReason === 'time_based') {
            timeBasedExits++;
          }
          
          if (trade.riskManagement?.riskRewardRatio) {
            riskRewardRatios.push(trade.riskManagement.riskRewardRatio);
          }
        });
        
        const averageRiskRewardRatio = riskRewardRatios.length > 0
          ? riskRewardRatios.reduce((sum, ratio) => sum + ratio, 0) / riskRewardRatios.length
          : 0;
        
        return {
          riskCategoryDistribution: riskCategories,
          stopLossHits,
          targetHits,
          timeBasedExits,
          averageRiskRewardRatio
        };
      } catch (error) {
        console.error("Error calculating risk management stats:", error);
        throw error;
      }
    }
  };
};

// Create the performance manager
const performanceManager = createPerformanceManager();

// Export the performance tools
export const performanceTools = {
  getPerformanceMetrics: {
    schema: {},
    handler: async () => {
      try {
        const metrics = await performanceManager.getPerformanceMetrics();
        return {
          content: [{
            type: "text",
            text: JSON.stringify(metrics, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving performance metrics: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  getTokenPerformance: {
    schema: {
      ticker: z.string().describe("Token ticker symbol")
    },
    handler: async ({ ticker }) => {
      try {
        const performance = await performanceManager.getTokenPerformance(ticker);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(performance, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving token performance: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  getPerformanceHistory: {
    schema: {
      timeframe: z.string().optional().default('30d').describe("Timeframe for history (7d, 30d, 90d, 1y)")
    },
    handler: async ({ timeframe }) => {
      try {
        const history = await performanceManager.getPerformanceHistory(timeframe);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(history, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving performance history: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  getRiskManagementStats: {
    schema: {},
    handler: async () => {
      try {
        const stats = await performanceManager.getRiskManagementStats();
        return {
          content: [{
            type: "text",
            text: JSON.stringify(stats, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving risk management stats: ${error.message}`
          }],
          isError: true
        };
      }
    }
  }
};
