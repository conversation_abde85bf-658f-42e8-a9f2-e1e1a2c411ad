import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { spawn, exec } from "child_process";

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "../");

// Lock file to prevent multiple instances
const LOCK_FILE = path.join(__dirname, "mcp-server.lock");

// Check if another instance is running
if (fs.existsSync(LOCK_FILE)) {
  try {
    const lockData = JSON.parse(fs.readFileSync(LOCK_FILE, 'utf8'));
    const pid = lockData.pid;

    // Check if the process is still running
    try {
      process.kill(pid, 0); // This doesn't actually kill the process, just checks if it exists
      console.error(`Another MCP server instance is already running with PID ${pid}`);
      console.error("Please stop that instance before starting a new one");
      process.exit(1);
    } catch (e) {
      // Process doesn't exist, so we can remove the stale lock file
      console.log("Found stale lock file, removing it");
      fs.unlinkSync(LOCK_FILE);
    }
  } catch (e) {
    // Invalid lock file, remove it
    console.log("Found invalid lock file, removing it");
    fs.unlinkSync(LOCK_FILE);
  }
}

// Create lock file with current PID
fs.writeFileSync(LOCK_FILE, JSON.stringify({
  pid: process.pid,
  startTime: new Date().toISOString()
}));

// Status file to track bot state
const STATUS_FILE = path.join(__dirname, "bot-status.json");

// Results file to store trading results
const RESULTS_FILE = path.join(__dirname, "bot-results.json");

// Initialize status file if it doesn't exist
if (!fs.existsSync(STATUS_FILE)) {
  fs.writeFileSync(STATUS_FILE, JSON.stringify({
    running: false,
    lastRun: null,
    lastResults: null,
    pid: null
  }));
}

// Initialize results file if it doesn't exist
if (!fs.existsSync(RESULTS_FILE)) {
  fs.writeFileSync(RESULTS_FILE, JSON.stringify({
    trades: [],
    portfolio: {},
    analysis: {}
  }));
}

// Function to read status
function getStatus() {
  try {
    return JSON.parse(fs.readFileSync(STATUS_FILE, 'utf8'));
  } catch (error) {
    console.error("Error reading status file:", error);
    return { running: false, lastRun: null, lastResults: null };
  }
}

// Function to update status
function updateStatus(updates) {
  try {
    const currentStatus = getStatus();
    const newStatus = { ...currentStatus, ...updates };
    fs.writeFileSync(STATUS_FILE, JSON.stringify(newStatus, null, 2));
    return newStatus;
  } catch (error) {
    console.error("Error updating status file:", error);
    return null;
  }
}

// Function to read results
function getResults() {
  try {
    return JSON.parse(fs.readFileSync(RESULTS_FILE, 'utf8'));
  } catch (error) {
    console.error("Error reading results file:", error);
    return { trades: [], portfolio: {}, analysis: {} };
  }
}

// Function to update results
function updateResults(updates) {
  try {
    const currentResults = getResults();
    const newResults = { ...currentResults, ...updates };
    fs.writeFileSync(RESULTS_FILE, JSON.stringify(newResults, null, 2));
    return newResults;
  } catch (error) {
    console.error("Error updating results file:", error);
    return null;
  }
}

// Function to capture bot output
function captureBotOutput() {
  const logFile = path.join(projectRoot, 'bot-output.log');

  // Check if log file exists
  if (fs.existsSync(logFile)) {
    try {
      const logContent = fs.readFileSync(logFile, 'utf8');

      // Extract portfolio information
      const portfolioMatch = logContent.match(/\[PORTFOLIO\] Value Breakdown:[\s\S]*?\[PORTFOLIO\] Total Value: ([\d\.]+) ADA/);
      if (portfolioMatch) {
        const portfolio = {
          totalValue: parseFloat(portfolioMatch[1]),
          breakdown: {}
        };

        // Extract category breakdowns
        const breakdownMatches = logContent.match(/\[PORTFOLIO\] Value Breakdown:[\s\S]*?\[PORTFOLIO\] Total Value/)[0];
        const categories = breakdownMatches.match(/([a-z]+): ([\d\.]+) ADA \(([\d\.]+)%\)/g);

        if (categories) {
          categories.forEach(category => {
            const [_, name, amount, percentage] = category.match(/([a-z]+): ([\d\.]+) ADA \(([\d\.]+)%\)/);
            portfolio.breakdown[name] = {
              amount: parseFloat(amount),
              percentage: parseFloat(percentage)
            };
          });
        }

        updateResults({ portfolio });
      }

      // Extract trade decisions
      const tradeMatches = logContent.match(/\[LLM\] Decision for ([A-Z]+): ([\s\S]*?)(?=\[LLM\]|$)/g);
      if (tradeMatches) {
        const trades = tradeMatches.map(match => {
          const [_, token, decisionText] = match.match(/\[LLM\] Decision for ([A-Z]+): ([\s\S]*?)(?=\[LLM\]|$)/);

          // Try to parse the JSON decision
          try {
            const decision = JSON.parse(decisionText.trim());
            return {
              token,
              timestamp: new Date().toISOString(),
              decision
            };
          } catch (e) {
            // If can't parse as JSON, return as text
            return {
              token,
              timestamp: new Date().toISOString(),
              decision: decisionText.trim()
            };
          }
        });

        updateResults({ trades });
      }

      // Update the status with the results
      const results = getResults();
      updateStatus({ lastResults: results });

      return results;
    } catch (error) {
      console.error("Error capturing bot output:", error);
      return null;
    }
  }

  return null;
}

// Function to start the trading bot
function startTradingBot() {
  try {
    // Check if already running
    const status = getStatus();
    if (status.running) {
      return { success: false, message: "Trading bot is already running" };
    }

    // Create log file for output
    const logFile = path.join(projectRoot, 'bot-output.log');
    const logStream = fs.openSync(logFile, 'w');

    // Start the bot as a background process with environment variables explicitly set
    const botProcess = spawn('npx', ['tsx', 'src/portfolio-swaps.ts'], {
      cwd: projectRoot,
      detached: true,
      stdio: ['ignore', logStream, logStream],
      env: {
        ...process.env,
        DISCORD_TOKEN: process.env.DISCORD_TOKEN,
        // Explicitly set the Discord channel ID to ensure it's using the correct one
        DISCORD_CHANNEL_ID: "1329622661831327773",
        TAP_TOOLS_API_KEY: process.env.TAP_TOOLS_API_KEY,
        OPENAI_API_KEY: process.env.OPENAI_API_KEY,
        BLOCKFROST_PROJECT_ID: process.env.BLOCKFROST_PROJECT_ID,
        SEED_PHRASE: process.env.SEED_PHRASE,
        CARDANO_ADDRESS: process.env.CARDANO_ADDRESS
      }
    });

    // Unref the process so it can run independently
    botProcess.unref();

    // Update status with process ID
    updateStatus({
      running: true,
      lastRun: new Date().toISOString(),
      pid: botProcess.pid
    });

    // Set up a timer to periodically capture output
    const resultTimer = setInterval(() => {
      captureBotOutput();
    }, 5000); // Check every 5 seconds

    // Store the timer ID globally so we can clear it later
    global.resultTimer = resultTimer;

    return { success: true, message: "Trading bot started successfully", pid: botProcess.pid };
  } catch (error) {
    console.error("Error starting trading bot:", error);
    return { success: false, message: `Error starting trading bot: ${error.message}` };
  }
}

// Function to stop the trading bot
function stopTradingBot() {
  try {
    // Check if running
    const status = getStatus();
    if (!status.running) {
      return { success: false, message: "Trading bot is not running" };
    }

    // Get the process ID
    const pid = status.pid;
    if (!pid) {
      return { success: false, message: "No process ID found for running bot" };
    }

    // Kill the process
    exec(`kill -9 ${pid}`, (error) => {
      if (error) {
        console.error(`Error killing process ${pid}:`, error);
      }
    });

    // Also kill any other portfolio-swaps processes that might be running
    exec(`ps aux | grep "portfolio-swaps" | grep -v grep | awk '{print $2}' | xargs -I {} kill -9 {}`, (error) => {
      if (error) {
        console.error("Error killing other portfolio-swaps processes:", error);
      } else {
        console.log("Successfully killed all portfolio-swaps processes");
      }
    });

    // Clear the result timer if it exists
    if (global.resultTimer) {
      clearInterval(global.resultTimer);
      global.resultTimer = null;
    }

    // Capture final output
    captureBotOutput();

    // Update status
    updateStatus({
      running: false,
      pid: null
    });

    return { success: true, message: "Trading bot stopped successfully" };
  } catch (error) {
    console.error("Error stopping trading bot:", error);
    return { success: false, message: `Error stopping trading bot: ${error.message}` };
  }
}

// Create an MCP server
const server = new McpServer({
  name: "mister-ada-mcp",
  version: "1.0.0",
  description: "Simple MCP server for MISTER trading bot"
});

// Tool to start the trading bot
server.tool(
  "startTrading",
  {},
  async () => {
    try {
      const result = startTradingBot();
      return {
        content: [{ type: "text", text: JSON.stringify(result, null, 2) }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error starting trading: ${error.message}` }],
        isError: true
      };
    }
  }
);

// Tool to get performance metrics
server.tool(
  "getPerformanceMetrics",
  {},
  async () => {
    try {
      // Read the results file to get performance data
      const resultsData = fs.readFileSync(RESULTS_FILE, 'utf8');
      const results = JSON.parse(resultsData);

      // Calculate performance metrics
      const metrics = {
        totalTrades: results.trades.length || 0,
        successfulTrades: results.trades.filter(t => t.success).length || 0,
        winRate: results.trades.length > 0
          ? (results.trades.filter(t => t.success).length / results.trades.length) * 100
          : 0,
        totalProfitLoss: results.trades.reduce((sum, trade) => sum + (trade.profitLoss || 0), 0),
        tokenPerformance: results.analysis.tokenPerformance || []
      };

      return {
        content: [{ type: "text", text: JSON.stringify(metrics, null, 2) }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error getting performance metrics: ${error.message}` }],
        isError: true
      };
    }
  }
);

// Tool to get settings
server.tool(
  "getSettings",
  {},
  async () => {
    try {
      // Read the status file to get settings
      const statusData = fs.readFileSync(STATUS_FILE, 'utf8');
      const status = JSON.parse(statusData);

      // Get settings from the results file
      const resultsData = fs.readFileSync(RESULTS_FILE, 'utf8');
      const results = JSON.parse(resultsData);

      const settings = {
        general: {
          botEnabled: status.running || false,
          autoTradeEnabled: true,
          maxTradesPerDay: 5,
          maxTradeSize: 100
        },
        portfolioAllocations: results.portfolio.allocations || {
          ada: 30,
          meme: 15,
          defi: 20,
          major: 15,
          shard: 10,
          new: 10
        },
        tradingLimits: results.analysis.limits || {
          minConfidence: 7,
          minTokenAge: 7,
          minLiquidity: 5000,
          maxImpact: 5
        }
      };

      return {
        content: [{ type: "text", text: JSON.stringify(settings, null, 2) }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error getting settings: ${error.message}` }],
        isError: true
      };
    }
  }
);

// Tool to update settings
server.tool(
  "updateSettings",
  {
    section: z.string().describe("Settings section to update (general, portfolioAllocations, tradingLimits)"),
    settings: z.any().describe("New settings for the section")
  },
  async ({ section, settings }) => {
    try {
      // Read the results file
      const resultsData = fs.readFileSync(RESULTS_FILE, 'utf8');
      const results = JSON.parse(resultsData);

      // Update the appropriate section
      switch (section) {
        case "general":
          // For general settings, we might need to start/stop the bot
          if (settings.botEnabled !== undefined) {
            const status = getStatus();
            if (settings.botEnabled && !status.running) {
              startTradingBot();
            } else if (!settings.botEnabled && status.running) {
              stopTradingBot();
            }
          }
          break;

        case "portfolioAllocations":
          // Update portfolio allocations
          if (!results.portfolio) {
            results.portfolio = {};
          }
          results.portfolio.allocations = {
            ...(results.portfolio.allocations || {}),
            ...settings
          };
          break;

        case "tradingLimits":
          // Update trading limits
          if (!results.analysis) {
            results.analysis = {};
          }
          results.analysis.limits = {
            ...(results.analysis.limits || {}),
            ...settings
          };
          break;

        default:
          return {
            content: [{ type: "text", text: `Invalid settings section: ${section}` }],
            isError: true
          };
      }

      // Save updated results
      fs.writeFileSync(RESULTS_FILE, JSON.stringify(results, null, 2));

      return {
        content: [{ type: "text", text: JSON.stringify({ success: true, message: `${section} settings updated` }, null, 2) }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error updating settings: ${error.message}` }],
        isError: true
      };
    }
  }
);

// Tool to check bot status
server.tool(
  "checkStatus",
  {},
  async () => {
    try {
      const status = getStatus();
      return {
        content: [{ type: "text", text: JSON.stringify(status, null, 2) }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error checking status: ${error.message}` }],
        isError: true
      };
    }
  }
);

// Tool to get trading results
server.tool(
  "getResults",
  {},
  async () => {
    try {
      // Force capture of latest output
      captureBotOutput();

      const status = getStatus();
      if (!status.lastResults) {
        return {
          content: [{ type: "text", text: "No trading results available yet" }]
        };
      }
      return {
        content: [{ type: "text", text: JSON.stringify(status.lastResults, null, 2) }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error getting results: ${error.message}` }],
        isError: true
      };
    }
  }
);

// Tool to stop the trading bot
server.tool(
  "stopTrading",
  {},
  async () => {
    try {
      // First, try to stop the bot using our tracking
      const result = stopTradingBot();

      // Then, make sure all portfolio-swaps processes are killed
      // This is a more aggressive approach to ensure everything stops
      exec(`ps aux | grep "portfolio-swaps" | grep -v grep | awk '{print $2}' | xargs -I {} kill -9 {}`, (error) => {
        if (error) {
          console.error("Error killing portfolio-swaps processes:", error);
        } else {
          console.log("Successfully killed all portfolio-swaps processes");
        }
      });

      // Clear the result timer if it exists
      if (global.resultTimer) {
        clearInterval(global.resultTimer);
        global.resultTimer = null;
      }

      // Update status to ensure it shows as stopped
      updateStatus({
        running: false,
        pid: null
      });

      return {
        content: [{ type: "text", text: JSON.stringify(result, null, 2) }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error stopping trading: ${error.message}` }],
        isError: true
      };
    }
  }
);

// Function to clean up resources and exit
function cleanup(exitCode = 0) {
  console.log("Cleaning up resources before exit...");

  // Stop the trading bot if it's running
  const status = getStatus();
  if (status.running) {
    console.log("Stopping trading bot as part of cleanup...");
    stopTradingBot();
  }

  // Kill any other portfolio-swaps processes that might be running
  exec(`ps aux | grep "portfolio-swaps" | grep -v grep | awk '{print $2}' | xargs -I {} kill -9 {}`, (error) => {
    if (error) {
      console.error("Error killing portfolio-swaps processes:", error);
    } else {
      console.log("Successfully killed all portfolio-swaps processes");
    }

    // Clear the result timer if it exists
    if (global.resultTimer) {
      clearInterval(global.resultTimer);
      global.resultTimer = null;
    }

    // Remove the lock file
    if (fs.existsSync(LOCK_FILE)) {
      try {
        fs.unlinkSync(LOCK_FILE);
        console.log("Removed lock file");
      } catch (e) {
        console.error("Error removing lock file:", e);
      }
    }

    console.log("Cleanup complete, exiting...");
    process.exit(exitCode);
  });
}

// Handle process termination signals
process.on('SIGINT', () => {
  console.log("Received SIGINT signal");
  cleanup();
});

process.on('SIGTERM', () => {
  console.log("Received SIGTERM signal");
  cleanup();
});

process.on('uncaughtException', (err) => {
  console.error("Uncaught exception:", err);
  cleanup(1);
});

// Start the server
const transport = new StdioServerTransport();
server.connect(transport).catch(err => {
  console.error("Error connecting to transport:", err);
  cleanup(1);
});

console.log("mister-ada-mcp simple server started");
