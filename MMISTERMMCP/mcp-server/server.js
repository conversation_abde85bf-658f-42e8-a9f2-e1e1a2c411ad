import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { SSEServerTransport } from "@modelcontextprotocol/sdk/server/sse.js";
import { z } from "zod";
import express from "express";
import dotenv from "dotenv";

// Import tools
import { portfolioTools } from "./tools/portfolio.js";
import { marketDataTools } from "./tools/market-data.js";
import { tradingTools } from "./tools/trading.js";
import { analysisTools } from "./tools/analysis.js";
import { performanceTools } from "./tools/performance.js";
import { settingsTools } from "./tools/settings.js";

// Load environment variables
dotenv.config();

// Create an MCP server
const server = new McpServer({
  name: "mister-ada-mcp",
  version: "1.0.0",
  description: "MCP server for MISTER trading bot integration"
});

// Register all tools
const registerTools = () => {
  // Portfolio tools
  Object.entries(portfolioTools).forEach(([name, tool]) => {
    server.tool(
      name,
      tool.schema,
      tool.handler
    );
  });

  // Market data tools
  Object.entries(marketDataTools).forEach(([name, tool]) => {
    server.tool(
      name,
      tool.schema,
      tool.handler
    );
  });

  // Trading tools
  Object.entries(tradingTools).forEach(([name, tool]) => {
    server.tool(
      name,
      tool.schema,
      tool.handler
    );
  });

  // Analysis tools
  Object.entries(analysisTools).forEach(([name, tool]) => {
    server.tool(
      name,
      tool.schema,
      tool.handler
    );
  });

  // Performance tools
  Object.entries(performanceTools).forEach(([name, tool]) => {
    server.tool(
      name,
      tool.schema,
      tool.handler
    );
  });

  // Settings tools
  Object.entries(settingsTools).forEach(([name, tool]) => {
    server.tool(
      name,
      tool.schema,
      tool.handler
    );
  });
};

// Register all tools
registerTools();

// Determine which transport to use based on environment variables
const useSSE = process.env.MCP_TRANSPORT === "sse";

if (useSSE) {
  // Set up Express server for SSE transport
  const app = express();
  const port = process.env.PORT || 3000;

  // to support multiple simultaneous connections we have a lookup object from
  // sessionId to transport
  const transports = {};

  app.get("/sse", async (_, res) => {
    const transport = new SSEServerTransport('/messages', res);
    transports[transport.sessionId] = transport;
    res.on("close", () => {
      delete transports[transport.sessionId];
    });
    await server.connect(transport);
  });

  app.post("/messages", async (req, res) => {
    const sessionId = req.query.sessionId;
    const transport = transports[sessionId];
    if (transport) {
      await transport.handlePostMessage(req, res);
    } else {
      res.status(400).send('No transport found for sessionId');
    }
  });

  app.listen(port, () => {
    console.log(`MCP server running on http://localhost:${port}`);
  });
} else {
  // Use stdio transport
  const transport = new StdioServerTransport();
  server.connect(transport).catch(err => {
    console.error("Error connecting to transport:", err);
    process.exit(1);
  });
}

console.log("mister-ada-mcp server started");
