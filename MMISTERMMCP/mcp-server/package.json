{"name": "mister-ada-mcp", "version": "1.0.0", "description": "MCP server for <PERSON><PERSON> trading bot integration", "main": "server.js", "type": "module", "scripts": {"start": "node simple-server.js", "dev": "nodemon simple-server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.8.0", "dotenv": "^16.4.7", "express": "^4.18.3", "tsx": "^4.7.1", "zod": "^3.24.2"}, "devDependencies": {"nodemon": "^3.1.0"}}