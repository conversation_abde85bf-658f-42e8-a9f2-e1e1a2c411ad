Okay, here's a step-by-step guide on how to create a basic MCP (Model Context Protocol) server from scratch, based on the concepts and actions shown in the video, primarily using the Cursor IDE and its AI capabilities.

**Goal:** Create an MCP server that exposes custom tools (functions) to an AI agent (like the one in Cursor), allowing the agent to execute your code.

**Prerequisites:**

1.  **Python Installation:** Ensure you have Python 3 installed.
2.  **Cursor IDE (Recommended):** The video heavily features <PERSON><PERSON><PERSON>, an AI-first code editor. While you can use other IDEs, this guide follows the Cursor workflow. Download and install it from [cursor.sh](https://cursor.sh/).
3.  **Basic Understanding of Python:** While the video suggests you don't need deep coding knowledge *if* you use AI effectively, basic familiarity helps.

---

**Guide: Creating Your First MCP Server**

**Step 1: Install the MCP Library**

1.  Open your terminal (or the terminal within Cursor).
2.  Install the necessary Python package using pip:
    ```bash
    pip install mcp
    # or if you use pip3 specifically
    pip3 install mcp
    ```
    *This installs the core library needed to build and run MCP servers.*

**Step 2: Create Your MCP Server Python File**

1.  Open the Cursor IDE.
2.  Create a new folder for your project (e.g., `Test-MCP`).
3.  Inside that folder, create a new Python file (e.g., `first-mcp.py`).
4.  Add the basic code structure. Here's the example from the video (USD to GBP converter):

    ```python
    # first-mcp.py

    # 1. Import necessary class
    from mcp.server.fastmcp import FastMCP

    # 2. Create an MCP server instance (give it a name)
    mcp = FastMCP("Demo") # The name "Demo" is arbitrary

    # 3. Define a tool using the @mcp.tool() decorator
    @mcp.tool()
    def usd_to_gbp(amount: float) -> float:
        """Convert USD(dollars) to GBP(pounds sterling)""" # IMPORTANT: This description tells the AI what the tool does.
        EXCHANGE_RATE = 0.79 # Example fixed rate
        return round(amount * EXCHANGE_RATE, 2)

    # Add more tools here using the @mcp.tool() decorator if needed
    ```

    *   **`FastMCP`:** The class used to create the server.
    *   **`mcp = FastMCP("Demo")`:** Initializes the server.
    *   **`@mcp.tool()`:** A decorator that registers the following function as a tool the AI can use.
    *   **`def usd_to_gbp(...)`:** A standard Python function.
    *   **Type Hinting (`amount: float`, `-> float`):** Helps the AI understand the expected input and output types.
    *   **Docstring (`"""..."""`):** *Crucial for the AI*. This natural language description is how the AI agent understands the tool's purpose and when to use it. Be descriptive!

**Step 3: Integrate the MCP Server with Cursor**

1.  **Get File Path:** Right-click on your `first-mcp.py` file in the Cursor file explorer and select "Copy Path" or "Copy Absolute Path".
2.  **Open Cursor Settings:** Click the gear icon or go to `Cursor > Settings`.
3.  **Navigate to MCP Section:** Find and click on "MCP" in the left-hand settings menu.
4.  **Add New MCP Server:** Click the "+ Add new MCP server" button.
5.  **Configure the Server:**
    *   **Name:** Give your server a nickname (e.g., `first-mcp`). This is how it appears in the settings list.
    *   **Type:** Keep this as `command`.
    *   **Command:** This tells Cursor how to run your server. Enter the following, replacing `<PASTE_YOUR_COPIED_PATH_HERE>` with the actual file path you copied in step 3.1:
        ```bash
        mcp run <PASTE_YOUR_COPIED_PATH_HERE>
        ```
        *(Example: `mcp run /Users/<USER>/Developer/Test-MCP/first-mcp.py`)*
    *   Click "Add".
6.  **Enable the Tool:** Your new server (`first-mcp`) should appear in the list. Underneath it, you should see the tool(s) you defined (e.g., `usd_to_gbp`). Make sure the "Enabled" checkbox for the server is ticked. Cursor automatically detects the tools when the server runs.

**Step 4: Test Your MCP Server**

1.  Go back to the main Cursor chat interface.
2.  Make sure your `first-mcp.py` file is open or referenced. You can add it to the context by typing `@` and selecting the file.
3.  Ask the AI agent a question that should trigger your tool. Use the description in your docstring as a guide.
    *Example prompt:* `convert 20$ into pound for me`
4.  **Observe:**
    *   The agent should state it will use your tool (e.g., "I'll convert 20 USD to GBP for you using the exchange rate tool").
    *   It will show a "Calling MCP tool `usd_to_gbp`" message.
    *   You might see the parameters being passed (e.g., `{"amount": 20}`).
    *   It will show the result returned by your Python function (e.g., `15.8`).
    *   The agent will formulate a final answer using the result (e.g., "20 USD is equal to £15.80...").

**Step 5: Leveraging AI for More Complex Servers (Example: Web Crawler)**

This is where the AI assistance shines. Instead of manually coding everything:

1.  **Find Documentation/Code:** Locate the documentation or example code for the functionality you want (e.g., the `Crawl4AI` example script shown in the video).
2.  **Provide Context to Cursor:**
    *   **(Option A: Code Snippet):** Paste the example script (like the `Crawl4AI` script) into a new file (e.g., `main.py`) or directly into the chat.
    *   **(Option B: Documentation URL):**
        *   Find the URL for the documentation (often a `README.md` on GitHub).
        *   Go to Cursor Settings > Features > Docs.
        *   Click "+ Add new doc", paste the URL, give it a name (e.g., `Python MCP SDK` or `Crawl4AI Docs`), and let Cursor index it.
3.  **Prompt the AI:** Instruct Cursor's AI to integrate the new functionality into your existing MCP server file (`first-mcp.py`).
    *   *Example Prompt (using code snippet context):* "OK, look at the `@main.py` file. Take the web crawling function from it. Now, modify the `@first-mcp.py` file: remove the old `usd_to_gbp` tool and add a new tool called `crawl_web`. This tool should accept a web URL link as a parameter, use the `AsyncWebCrawler` from `crawl4ai` (like in `main.py`) to crawl that URL, and return the crawled data as markdown."
    *   *Example Prompt (using documentation context):* "Using the context from `@Python MCP SDK` documentation, create a tool in `@first-mcp.py` called `crawl_web` that takes a URL string as input, uses the `crawl4ai` library's `AsyncWebCrawler` to fetch the content of that URL, and returns the content as a markdown string."
4.  **Review and Refine:** Cursor will generate the modified code in `first-mcp.py`. Review it. It should now include the imports for `crawl4ai`, the new `@mcp.tool()` decorated function `crawl_web`, etc.
5.  **Update MCP Settings (If needed):** The command in Cursor settings should still point to `first-mcp.py`. Cursor should automatically detect the *new* tool (`crawl_web`) once it restarts the server (which usually happens automatically when you save the file or interact with the agent). You might need to briefly disable/re-enable the server in settings if it doesn't pick it up immediately.
6.  **Test:** Add `@first-mcp.py` to the context and ask the agent to use the new tool: `crawl https://www.cursor.sh/ and tell me the sections in it`.

---

**Key Takeaway (from the video):**

MCP servers act as bridges between an AI agent and your custom code (local scripts, APIs, etc.). They work on a simple input/processing/output model. You define tools (functions) with clear descriptions (docstrings). The AI uses these descriptions to understand when and how to call your code. You *don't* necessarily need to write all the Python code yourself; you can leverage AI (like Cursor) by providing it with the right context (documentation, example code snippets) and prompting it to build the MCP server for you.