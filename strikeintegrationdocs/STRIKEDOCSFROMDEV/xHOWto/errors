Error: ❌ API Error: {}
    at createConsoleError (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:882:71)
    at handleConsoleError (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:1058:54)
    at console.error (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:1223:57)
    at ApiClient.request (http://localhost:3000/_next/static/chunks/src_0d4883b2._.js:338:25)
    at async AuthService.initialize (http://localhost:3000/_next/static/chunks/src_0d4883b2._.js:447:36)
    at async initializeAuth (http://localhost:3000/_next/static/chunks/src_0d4883b2._.js:603:13)






    -----

test wallet: addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3jcu5d8ps7zex2k2xt3uqxgjqnnj0vs2qd4a6gtpc6z3rqgr83dc


i think the wallet needs to be shown on the dashboard like a profile or something so the user knows it is working and can copy it down.



--------




Error: ❌ API Error: {}
    at createConsoleError (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:882:71)
    at handleConsoleError (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:1058:54)
    at console.error (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:1223:57)
    at ApiClient.request (http://localhost:3000/_next/static/chunks/src_0d4883b2._.js:338:25)
    at async forceSignalCheck (http://localhost:3000/_next/static/chunks/src_070fc749._.js:5234:30)





    -------



    Error: ❌ Signal check failed: "Endpoint not found"
    at createConsoleError (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:882:71)
    at handleConsoleError (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:1058:54)
    at console.error (http://localhost:3000/_next/static/chunks/9bf22_next_dist_client_3d678745._.js:1223:57)
    at forceSignalCheck (http://localhost:3000/_next/static/chunks/src_070fc749._.js:5240:25)


