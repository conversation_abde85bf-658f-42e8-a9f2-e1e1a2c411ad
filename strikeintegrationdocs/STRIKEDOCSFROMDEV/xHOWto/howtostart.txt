# 1. Start all services (if not already running)

cd sydney-agents && npm run dev                    # Terminal 1: Mastra system

cd sydney-agents && cd mister-frontend && npm run dev  # Terminal 3: Front-------

cd sydney-agents && node mister-bridge-server.cjs

npx @agentdeskai/browser-tools-server@latest


----
mister ada mcp
cd MMISTERMCP && npm start

https://substantial-scarce-magazin.mastra.cloud/api/agents/strikeAgent/generate





claude mcp add browser-mcp -s user npx @browsermcp/mcp@latest    
claude mcp add sequential-thinking -s user npx @modelcontextprotocol/server-sequential-thinking
claude mcp add context7 -s user npx @upstash/context7-mcp@latest
claude mcp add mastra-docs -s user npx @mastra/mcp-docs-server
claude mcp add magicuidesign -s user npx @magicuidesign/mcp@latest
claude mcp add --transport http bright-data "https://server.smithery.ai/@luminati-io/brightdata-mcp/mcp?api_key=5d46ee62-9232-4bdd-9ba4-34f166cfd819&profile=gradual-herring-KdrLAg


HOW TO PUSH

cd MMISTERMMCP
railway up

cd sydney-agents
git add -A
git commit -m "cashcoldgame"

git push origin main

Here's your current production setup:

1. 🌐 Mastra Cloud (AI Agents)
URL: https://substantial-scarce-magazin.mastra.cloud
Purpose: Hosts your AI agents (Strike Agent, Fibonacci Agent, etc.)
Deployment: Auto-deploys from git commits
Status: ✅ PRODUCTION
2. 🌉 Bridge Server (API Gateway)
URL: https://bridge-server-cjs-production.up.railway.app
Purpose: Bridges frontend ↔ Mastra Cloud ↔ Strike Finance
Deployment: Railway auto-deploys from git
Status: ✅ PRODUCTION
3. 📊 ADA Backtesting Service
URL: https://ada-backtesting-service-production.up.railway.app
Purpose: Provides ADA algorithm signals
Deployment: Railway (separate service)
Status: ✅ PRODUCTION
4. 🤖 CNT Trading Bot
URL: https://cnt-trading-api-production.up.railway.app
Purpose: CNT trading functionality
Deployment: Railway (separate service)
Status: ✅ PRODUCTION
5. 💻 Frontend (MISTER UI)
URL: http://localhost:3000 (LOCAL ONLY)
Purpose: User interface for trading
Deployment: NOT YET DEPLOYED - running locally
Status: ⚠️ LOCAL DEVELOPMENT



Address: addr1vy60rn622dl76ulgqc0lzmkrglyv7c47gk4u38kpfyat50gl68uck
VKH: 34f1cf4a537fed73e8061ff16ec347c8cf62be45abc89ec1493aba3d
Files: keys/agent-wallet.{vkey,skey,addr}

Contract Address: addr1w8hksl7jnqst7e58ypcn4d6k68d6z0rw7hkuch5wad7d07c3zn2nj
Script Hash: ef687fd29820bf668720713ab756d1dba13c6ef5edcc5e8eeb7cd7fb
Network: Cardano Mainnet
Status: DEPLOYED & TESTED ✅