react-dom-client.development.js:25022 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
index.iife.js:1 content script loaded
client.ts:78 🌐 API Request: GET http://localhost:4113/api/auth/validate
client.ts:104 ✅ API Success: /api/auth/validate
client.ts:78 🌐 API Request: GET http://localhost:4113/api/auth/me
report-hmr-latency.ts:26 [Fast Refresh] done in NaNms
read.js:2530 READ - Host validation failed: {hostName: '', hostType: undefined}
client.ts:104 ✅ API Success: /api/auth/me
AuthContext.tsx:47 ✅ User authenticated from stored token
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 184ms
content.js:2524 Host is not supported
content.js:2526 Host is not valid or supported
content.js:2526 Host is not in insights whitelist
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 116ms
page.tsx:128 
            
            
           POST http://localhost:3000/api/wallet/register 404 (Not Found)
registerWalletForTrading @ page.tsx:128
checkWalletConnection @ page.tsx:78
await in checkWalletConnection
TradingPage.useEffect @ page.tsx:29
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<TradingPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
page.tsx:85 ✅ Vespr wallet connected for trading
PositionsSummary.tsx:38 
            
            
           GET http://localhost:3000/api/positions 400 (Bad Request)
fetchPositions @ PositionsSummary.tsx:38
PositionsSummary.useEffect @ PositionsSummary.tsx:31
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
performSyncWorkOnRoot @ react-dom-client.development.js:16361
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<PositionsSummary>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
TradingPage @ page.tsx:249
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
PositionsSummary.tsx:38 
            
            
           GET http://localhost:3000/api/positions 400 (Bad Request)
fetchPositions @ PositionsSummary.tsx:38
PositionsSummary.useEffect @ PositionsSummary.tsx:31
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
performSyncWorkOnRoot @ react-dom-client.development.js:16361
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<PositionsSummary>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
TradingPage @ page.tsx:249
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
page.tsx:128 
            
            
           POST http://localhost:3000/api/wallet/register 404 (Not Found)
registerWalletForTrading @ page.tsx:128
checkWalletConnection @ page.tsx:78
await in checkWalletConnection
executeDispatch @ react-dom-client.development.js:16501
runWithFiberInDEV @ react-dom-client.development.js:844
processDispatchQueue @ react-dom-client.development.js:16551
(anonymous) @ react-dom-client.development.js:17149
batchedUpdates$1 @ react-dom-client.development.js:3262
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16705
dispatchEvent @ react-dom-client.development.js:20815
dispatchDiscreteEvent @ react-dom-client.development.js:20783
page.tsx:85 ✅ Vespr wallet connected for trading

LATEST CONSOLE OUTPUT:
[Add new console output here]
