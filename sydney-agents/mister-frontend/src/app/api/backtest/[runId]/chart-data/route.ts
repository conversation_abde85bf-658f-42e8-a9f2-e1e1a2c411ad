import { NextRequest, NextResponse } from 'next/server';
import { MASTRA_API_URL } from '@/lib/api-config';

/**
 * GET /api/backtest/[runId]/chart-data
 * Get real OHLCV chart data for a specific backtest run
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ runId: string }> }
) {
  try {
    const { runId } = await params;

    if (!runId) {
      return NextResponse.json({
        success: false,
        error: 'Run ID is required'
      }, { status: 400 });
    }

    // Connect to Mastra Multi-Timeframe ADA Strategy
    // Parse the run ID to extract backtest parameters
    // Format: backtest_MultiTimeframeADAStrategy_e77532aed0a80
    const symbol = 'ADAUSD';
    const timeframe = '15m';

    // Use a longer historical period to show more data on the chart
    const endDate = new Date().toISOString();
    const startDate = new Date(Date.now() - (90 * 24 * 60 * 60 * 1000)).toISOString(); // 90 days ago

    console.log(`📊 Chart API: Fetching data from ${startDate} to ${endDate}`);

    // Call the actual Mastra strategy to get real backtest results
    const backtestResults = await runRealMastraBacktest(symbol, startDate, endDate);

    const chartData = backtestResults.chartData;

    return NextResponse.json({
      success: true,
      runId,
      symbol,
      timeframe,
      startDate,
      endDate,
      dataPoints: chartData.length,
      chartData,
      trades: backtestResults.trades,
      performance: backtestResults.performance
    });

  } catch (error) {
    console.error('Error fetching backtest chart data:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

/**
 * Run the actual Mastra Multi-Timeframe ADA Strategy
 * This connects to your real backtesting system
 */
async function runRealMastraBacktest(symbol: string, startDate: string, endDate: string) {
  try {
    console.log('🔄 Attempting to connect to Mastra Multi-Timeframe ADA Strategy...');

    // Check if hosted Mastra is running first
    const healthCheck = await fetch('https://substantial-scarce-magazin.mastra.cloud/health', {
      method: 'GET',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    }).catch(() => null);

    if (!healthCheck || !healthCheck.ok) {
      throw new Error('Hosted Mastra server is not available. Please check the service status.');
    }

    // Call your Mastra agent - using the correct agent name
    const response = await fetch(`${MASTRA_API_URL}/api/agents/cryptoBacktestingAgent/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [{
          role: 'user',
          content: `Use the multi-timeframe-ada-strategy tool to run a comprehensive backtest for ${symbol} from ${startDate} to ${endDate}. Use 15-minute execution timeframe with 10x leverage. I need both the complete OHLCV chart data and all trade results with entry/exit times, prices, and P&L.`
        }]
      }),
      signal: AbortSignal.timeout(240000) // 4 minute timeout for full backtest
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`Mastra API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('🔍 Mastra response:', JSON.stringify(result, null, 2));

    // Extract the backtest results from the Mastra agent response
    // Based on logs, the data is in result.text as a JSON string
    let backtestData: any = null;

    try {
      // First, try to get data from toolResults (where the actual JSON is)
      if (result.toolResults && Array.isArray(result.toolResults) && result.toolResults.length > 0) {
        const toolResult = result.toolResults[0];
        if (toolResult.result && typeof toolResult.result === 'object') {
          console.log('🔍 Found toolResult data:', Object.keys(toolResult.result));
          if (toolResult.result.success && toolResult.result.results) {
            backtestData = toolResult.result.results;
            console.log('✅ Found backtest data in toolResults');
          }
        }
      }

      // Fallback: try to parse the text field which contains the JSON response
      if (!backtestData && result.text && typeof result.text === 'string') {
        try {
          // Remove markdown code blocks if present
          let cleanText = result.text.trim();
          console.log('🔍 Raw text field:', cleanText.substring(0, 200) + '...');

          // Look for JSON block in the text - enhanced to handle voice announcements
          let jsonMatch = cleanText.match(/```json\s*(\{[\s\S]*?\})\s*```/);
          if (jsonMatch) {
            cleanText = jsonMatch[1];
            console.log('🔍 Extracted JSON from markdown:', cleanText.substring(0, 200) + '...');
          } else {
            // Look for any large JSON object in the text (for voice announcement responses)
            const largeJsonMatch = cleanText.match(/(\{[\s\S]{500,}?\})/);
            if (largeJsonMatch) {
              cleanText = largeJsonMatch[1];
              console.log('🔍 Extracted large JSON object:', cleanText.substring(0, 200) + '...');
            } else if (cleanText.startsWith('```json')) {
              cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
            } else if (cleanText.startsWith('```')) {
              cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '');
            }
          }

          console.log('🔍 Cleaned text field:', cleanText.substring(0, 200) + '...');

          const parsedText = JSON.parse(cleanText);
          if (parsedText.success && parsedText.results) {
            backtestData = parsedText.results;
            console.log('✅ Found backtest data in text field');
          }
        } catch (e) {
          console.log('Could not parse text field as JSON:', e instanceof Error ? e.message : String(e));
          console.log('🔍 Text field preview:', result.text.substring(0, 500));
        }
      }

      // Fallback: try tool invocations
      if (!backtestData && result.content && result.content[0] && result.content[0].toolInvocations) {
        const toolResult = result.content[0].toolInvocations[0]?.result;
        if (toolResult && toolResult.results) {
          backtestData = toolResult.results;
          console.log('✅ Found backtest data in tool invocation result');
        }
      }

      // Fallback: try the direct format
      if (!backtestData && result.success && result.results) {
        backtestData = result.results;
        console.log('✅ Found backtest data in direct results');
      }

      if (backtestData) {
        // Extract trades and convert to the format expected by the frontend
        const trades = backtestData.trades || [];
        const performance = backtestData.analysis?.performance || backtestData.performance || {};

        // Get REAL OHLCV data from Kraken for the actual price movements
        const chartData = await getRealADAOHLCVData(startDate, endDate);

        console.log(`✅ Loaded ${chartData.length} OHLCV candles and ${trades.length} trades from Mastra`);
        console.log(`🔍 Chart API - First trade sample:`, trades[0] ? {
          id: trades[0].id,
          entryTime: trades[0].entryTime || trades[0].entryDate,
          side: trades[0].side,
          entryPrice: trades[0].entryPrice,
          pnl: trades[0].pnl
        } : 'No trades');

        return {
          chartData,
          trades: trades.map((trade: any) => ({
            id: trade.id || `trade_${trade.entryTime || trade.entryDate}`,
            entryTime: trade.entryTime || trade.entryDate,
            exitTime: trade.exitTime || trade.exitDate,
            side: trade.side.toUpperCase(),
            entryPrice: trade.entryPrice,
            exitPrice: trade.exitPrice,
            size: trade.quantity,
            netPnl: trade.pnl,
            reason: trade.entryReason || trade.exitReason
          })),
          performance: {
            totalReturn: performance.totalReturn || backtestData.totalReturn,
            winRate: performance.hitRate || backtestData.hitRate,
            totalTrades: performance.totalTrades || backtestData.totalTrades,
            maxDrawdown: performance.maxDrawdown || backtestData.maxDrawdown,
            sharpeRatio: performance.sharpeRatio || backtestData.sharpeRatio
          }
        };
      } else {
        console.log('❌ No valid backtest data found in response structure');
        console.log('🔍 Response keys:', Object.keys(result));
        throw new Error('Failed to get valid backtest results from Mastra');
      }
    } catch (parseError) {
      console.error('❌ Error parsing Mastra response:', parseError);
      throw new Error('Failed to parse backtest results from Mastra');
    }

  } catch (error) {
    console.error('Error calling Mastra strategy:', error);

    // Fallback: Return a message indicating we need real data
    return {
      chartData: [],
      trades: [],
      performance: {},
      error: 'Could not connect to Mastra backtesting system. Please ensure Mastra is running on port 4112.'
    };
  }
}

/**
 * Get REAL ADA/USD OHLCV data from Kraken API
 */
async function getRealADAOHLCVData(startDate: string, endDate: string) {
  try {
    console.log('🔄 Fetching REAL ADA/USD data from Kraken...');

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Kraken API expects Unix timestamps
    const since = Math.floor(start.getTime() / 1000);
    const interval = 15; // 15-minute candles

    // Kraken API call for ADA/USD (ADAUSD pair)
    const krakenUrl = `https://api.kraken.com/0/public/OHLC?pair=ADAUSD&interval=${interval}&since=${since}`;

    console.log('🌐 Kraken API URL:', krakenUrl);

    const response = await fetch(krakenUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'MISTER-Trading-Bot/1.0'
      },
      signal: AbortSignal.timeout(30000) // 30 second timeout
    });

    if (!response.ok) {
      throw new Error(`Kraken API error: ${response.status}`);
    }

    const krakenData = await response.json();

    if (krakenData.error && krakenData.error.length > 0) {
      throw new Error(`Kraken API error: ${krakenData.error.join(', ')}`);
    }

    // Extract OHLCV data from Kraken response
    const ohlcData = krakenData.result?.ADAUSD || krakenData.result?.XXBTZUSD || [];

    if (!ohlcData || ohlcData.length === 0) {
      console.warn('No OHLCV data returned from Kraken, using fallback');
      return generateFallbackData(startDate, endDate);
    }

    // Convert Kraken format to our format
    const chartData = ohlcData
      .filter((candle: any) => {
        const candleTime = new Date(candle[0] * 1000);
        return candleTime >= start && candleTime <= end;
      })
      .map((candle: any) => ({
        time: new Date(candle[0] * 1000).toISOString(),
        open: Number(parseFloat(candle[1]).toFixed(6)),
        high: Number(parseFloat(candle[2]).toFixed(6)),
        low: Number(parseFloat(candle[3]).toFixed(6)),
        close: Number(parseFloat(candle[4]).toFixed(6)),
        volume: Number(parseFloat(candle[6]).toFixed(2))
      }));

    console.log(`✅ Loaded ${chartData.length} REAL ADA/USD candles from Kraken`);
    console.log(`📊 Price range: $${Math.min(...chartData.map((c: any) => c.low)).toFixed(4)} - $${Math.max(...chartData.map((c: any) => c.high)).toFixed(4)}`);

    return chartData;

  } catch (error) {
    console.error('❌ Error fetching real ADA data from Kraken:', error);
    console.log('🔄 Falling back to Phemex API...');

    try {
      return await getPhemexADAData(startDate, endDate);
    } catch (phemexError) {
      console.error('❌ Error fetching from Phemex:', phemexError);
      console.log('⚠️ Using minimal fallback data');
      return generateFallbackData(startDate, endDate);
    }
  }
}

/**
 * Fallback: Get ADA data from Phemex
 */
async function getPhemexADAData(startDate: string, endDate: string) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Phemex API for ADA/USD
  const phemexUrl = `https://api.phemex.com/md/kline?symbol=ADAUSD&resolution=15&from=${Math.floor(start.getTime() / 1000)}&to=${Math.floor(end.getTime() / 1000)}`;

  console.log('🌐 Phemex API URL:', phemexUrl);

  const response = await fetch(phemexUrl, {
    method: 'GET',
    signal: AbortSignal.timeout(30000)
  });

  if (!response.ok) {
    throw new Error(`Phemex API error: ${response.status}`);
  }

  const phemexData = await response.json();

  if (!phemexData.data || !phemexData.data.rows) {
    throw new Error('Invalid Phemex response format');
  }

  const chartData = phemexData.data.rows.map((candle: any) => ({
    time: new Date(candle[0] * 1000).toISOString(),
    open: Number((candle[1] / 10000).toFixed(6)), // Phemex uses scaled prices
    high: Number((candle[2] / 10000).toFixed(6)),
    low: Number((candle[3] / 10000).toFixed(6)),
    close: Number((candle[4] / 10000).toFixed(6)),
    volume: Number(candle[5])
  }));

  console.log(`✅ Loaded ${chartData.length} REAL ADA/USD candles from Phemex`);
  return chartData;
}

/**
 * Minimal fallback data with realistic ADA prices
 */
function generateFallbackData(startDate: string, endDate: string) {
  console.log('⚠️ Generating minimal fallback data with realistic ADA prices');

  const start = new Date(startDate);
  const end = new Date(endDate);
  const data = [];

  // Use realistic ADA price around $0.57 (current range)
  let currentPrice = 0.5700;
  let currentTime = new Date(start);

  while (currentTime <= end && data.length < 100) { // Limit fallback data
    const open = currentPrice;
    const close = currentPrice * (1 + (Math.random() - 0.5) * 0.005); // 0.5% max move
    const high = Math.max(open, close) * (1 + Math.random() * 0.002);
    const low = Math.min(open, close) * (1 - Math.random() * 0.002);

    data.push({
      time: currentTime.toISOString(),
      open: Number(open.toFixed(6)),
      high: Number(high.toFixed(6)),
      low: Number(low.toFixed(6)),
      close: Number(close.toFixed(6)),
      volume: Math.floor(Math.random() * 500000) + 100000
    });

    currentPrice = close;
    currentTime = new Date(currentTime.getTime() + 15 * 60 * 1000); // 15 minutes
  }

  return data;
}
