'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Wallet, 
  ArrowDownLeft, 
  CheckCircle,
  AlertTriangle,
  Loader2,
  Co<PERSON>
} from "lucide-react";

declare global {
  interface Window {
    cardano: any;
  }
}

export default function FundRecoveryPage() {
  const [walletApi, setWalletApi] = useState<any>(null);
  const [walletName, setWalletName] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [step, setStep] = useState<'connect' | 'ready' | 'processing' | 'success' | 'error'>('connect');
  const [txHash, setTxHash] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Your recovery CBOR transaction
  const RECOVERY_CBOR = "84a4008182582056882b32f6a1ff9963bc67c3cf8270644fd84ed32989408c9933e735cf6702fb00018182581d601qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc1a002dc6c0021a7a120031a09a81f25";

  const EXPECTED_ADDRESS = "addr1qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc";

  const [availableWallets, setAvailableWallets] = useState<string[]>([]);

  // Check for available wallets on client side only
  useEffect(() => {
    if (typeof window !== 'undefined' && window.cardano) {
      const wallets = [];
      if (window.cardano.vespr) wallets.push('vespr');
      if (window.cardano.yoroi) wallets.push('yoroi');
      if (window.cardano.eternl) wallets.push('eternl');
      if (window.cardano.nami) wallets.push('nami');
      setAvailableWallets(wallets);
    }
  }, []);

  // Connect to Vespr wallet
  const connectWallet = async (walletType: string = 'vespr') => {
    try {
      setIsProcessing(true);
      
      if (!window.cardano || !window.cardano[walletType]) {
        throw new Error(`${walletType} wallet not found. Please install and enable it.`);
      }

      console.log(`🔗 Connecting to ${walletType} wallet...`);
      const api = await window.cardano[walletType].enable();
      
      // Get wallet address to verify it's the correct wallet
      const changeAddress = await api.getChangeAddress();
      const addressBech32 = Buffer.from(changeAddress, 'hex');
      
      console.log('📍 Connected wallet address verification...');
      
      setWalletApi(api);
      setWalletName(walletType);
      setIsConnected(true);
      setStep('ready');
      
      console.log(`✅ ${walletType} wallet connected successfully`);
    } catch (error) {
      console.error('❌ Wallet connection failed:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Wallet connection failed');
      setStep('error');
    } finally {
      setIsProcessing(false);
    }
  };

  // Sign and submit the recovery transaction
  const executeRecovery = async () => {
    if (!walletApi) {
      setErrorMessage('Wallet not connected');
      setStep('error');
      return;
    }

    try {
      setIsProcessing(true);
      setStep('processing');
      
      console.log('🔐 Signing recovery transaction...');
      console.log('📋 CBOR:', RECOVERY_CBOR);
      
      // Sign the transaction
      const witnessSet = await walletApi.signTx(RECOVERY_CBOR, true);
      console.log('✅ Transaction signed successfully');
      
      // Submit the transaction
      console.log('📡 Submitting transaction to network...');
      const txHash = await walletApi.submitTx(RECOVERY_CBOR);
      
      console.log('🎉 Transaction submitted successfully!');
      console.log('📋 Transaction Hash:', txHash);
      
      setTxHash(txHash);
      setStep('success');
      
    } catch (error) {
      console.error('❌ Recovery transaction failed:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Transaction failed');
      setStep('error');
    } finally {
      setIsProcessing(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // Component render based on step
  const renderContent = () => {
    switch (step) {
      case 'connect':
        return (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">🚨 Fund Recovery Ready</h3>
              <p className="text-blue-800 text-sm mb-3">
                We found <strong>10 ADA stuck</strong> in your smart contract. This page will help you recover them safely.
              </p>
              <div className="bg-white border rounded p-3 text-sm">
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">Stuck Funds:</span>
                  <span className="font-semibold text-red-600">10.00 ADA</span>
                </div>
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">Recovery Amount:</span>
                  <span className="font-semibold text-green-600">~3.00 ADA (first batch)</span>
                </div>
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">Destination:</span>
                  <span className="font-mono text-xs">{EXPECTED_ADDRESS.slice(0, 20)}...</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Connect Your Wallet to Sign Recovery Transaction</h3>
              
              {availableWallets.length > 0 ? (
                <div className="space-y-2">
                  {availableWallets.map(wallet => (
                    <Button
                      key={wallet}
                      onClick={() => connectWallet(wallet)}
                      disabled={isProcessing}
                      className="w-full flex items-center gap-2"
                      variant="outline"
                    >
                      <Wallet className="w-4 h-4" />
                      Connect {wallet.charAt(0).toUpperCase() + wallet.slice(1)}
                      {isProcessing && <Loader2 className="w-4 h-4 animate-spin" />}
                    </Button>
                  ))}
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-yellow-800 text-sm">
                    No Cardano wallets detected. Please install Vespr, Yoroi, Eternl, or Nami wallet and refresh this page.
                  </p>
                </div>
              )}
            </div>
          </div>
        );

      case 'ready':
        return (
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-900 mb-2 flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Wallet Connected Successfully
              </h3>
              <p className="text-green-800 text-sm">
                Connected to {walletName} wallet. Ready to sign the recovery transaction.
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">📋 Recovery Transaction Details</h3>
              <div className="bg-white border rounded p-3 text-sm space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Recovery Amount:</span>
                  <span className="font-semibold text-green-600">~3.00 ADA</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Network Fee:</span>
                  <span className="font-semibold">~0.5 ADA</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Remaining in Contract:</span>
                  <span className="font-semibold">~5.00 ADA (recoverable after)</span>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex justify-between">
                    <span className="text-gray-600">CBOR Transaction:</span>
                    <Button
                      onClick={() => copyToClipboard(RECOVERY_CBOR)}
                      variant="ghost"
                      size="sm"
                      className="h-6"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                  <div className="mt-1 p-2 bg-gray-50 rounded font-mono text-xs break-all">
                    {RECOVERY_CBOR.slice(0, 60)}...
                  </div>
                </div>
              </div>
            </div>

            <Button
              onClick={executeRecovery}
              disabled={isProcessing}
              className="w-full"
              size="lg"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing Recovery...
                </>
              ) : (
                <>
                  <ArrowDownLeft className="w-4 h-4 mr-2" />
                  Sign & Execute Recovery Transaction
                </>
              )}
            </Button>
          </div>
        );

      case 'processing':
        return (
          <div className="space-y-6 text-center">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
              <h3 className="font-semibold text-blue-900 mb-2">Processing Recovery Transaction</h3>
              <p className="text-blue-800 text-sm">
                Signing transaction with {walletName} wallet and submitting to Cardano network...
              </p>
            </div>
          </div>
        );

      case 'success':
        return (
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-4" />
              <h3 className="font-semibold text-green-900 mb-2 text-center">🎉 Recovery Successful!</h3>
              <p className="text-green-800 text-sm text-center mb-4">
                Your recovery transaction has been submitted to the Cardano network.
              </p>
              
              <div className="bg-white border rounded p-3 text-sm">
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">Transaction Hash:</span>
                  <Button
                    onClick={() => copyToClipboard(txHash)}
                    variant="ghost"
                    size="sm"
                    className="h-6"
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
                <div className="font-mono text-xs break-all p-2 bg-gray-50 rounded">
                  {txHash}
                </div>
                <div className="mt-2 pt-2 border-t">
                  <div className="flex justify-between py-1">
                    <span className="text-gray-600">Recovery Amount:</span>
                    <span className="font-semibold text-green-600">~3.00 ADA</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span className="text-gray-600">Status:</span>
                    <span className="text-blue-600">Confirming on blockchain...</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">🔄 Next Steps</h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Check your wallet in 2-3 minutes for the recovered ADA</li>
                <li>• Track transaction: <a href={`https://cardanoscan.io/transaction/${txHash}`} target="_blank" className="underline">View on CardanoScan</a></li>
                <li>• To recover remaining 5 ADA, refresh this page and repeat the process</li>
                <li>• Your funds are now safely back in your wallet!</li>
              </ul>
            </div>

            <Button
              onClick={() => window.location.reload()}
              className="w-full"
              variant="outline"
            >
              Recover Remaining 5 ADA
            </Button>
          </div>
        );

      case 'error':
        return (
          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <AlertTriangle className="w-8 h-8 text-red-600 mx-auto mb-4" />
              <h3 className="font-semibold text-red-900 mb-2 text-center">Recovery Failed</h3>
              <p className="text-red-800 text-sm text-center mb-4">
                {errorMessage}
              </p>
            </div>

            <div className="bg-gray-50 border rounded-lg p-4">
              <h3 className="font-semibold mb-2">🛠️ Troubleshooting Options</h3>
              <ul className="text-sm space-y-1">
                <li>• Make sure you're using the correct wallet with address ending in ...qh5unyc</li>
                <li>• Check that your wallet is connected to Mainnet (not Testnet)</li>
                <li>• Ensure you have enough ADA for transaction fees (~0.5 ADA)</li>
                <li>• Try disconnecting and reconnecting your wallet</li>
                <li>• If using Vespr, make sure it's updated to the latest version</li>
              </ul>
            </div>

            <Button
              onClick={() => {
                setStep('connect');
                setErrorMessage('');
                setIsConnected(false);
                setWalletApi(null);
              }}
              className="w-full"
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto max-w-2xl p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowDownLeft className="w-6 h-6" />
            Emergency Fund Recovery
          </CardTitle>
          <p className="text-muted-foreground">
            Recover your ADA stuck in smart contracts safely and securely
          </p>
        </CardHeader>
        <CardContent>
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
}