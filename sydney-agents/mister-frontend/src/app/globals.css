@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.5512 0.1177 218.91);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.97 0 23.07);
  --secondary-foreground: oklch(0.2686 0 23.43);
  --muted: oklch(0.985 0 22.65);
  --muted-foreground: oklch(0.4411 0 24.82);
  --accent: oklch(0.985 0 22.65);
  --accent-foreground: oklch(0.2686 0 23.43);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9224 0 23.19);
  --input: oklch(0.8696 0 23.46);
  --ring: oklch(0.5512 0.1177 218.91);
  --chart-1: oklch(0.5512 0.1177 218.91);
  --chart-2: oklch(0.9148 0.0906 206.08);
  --chart-3: oklch(0.7876 0.173 211.42);
  --chart-4: oklch(0.8632 0.1432 207.1);
  --chart-5: oklch(0.9536 0.0508 205.46);
  --sidebar: oklch(0.97 0 23.07);
  --sidebar-foreground: oklch(0.2686 0 23.43);
  --sidebar-primary: oklch(0.5512 0.1177 218.91);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9224 0 23.19);
  --sidebar-accent-foreground: oklch(0.2686 0 23.43);
  --sidebar-border: oklch(0.9224 0 23.19);
  --sidebar-ring: oklch(0.5512 0.1177 218.91);
}

.dark {
  --background: oklch(0.1458 0 22.58);
  --foreground: oklch(0.9224 0 23.19);
  --card: oklch(0.1458 0 22.58);
  --card-foreground: oklch(0.9224 0 23.19);
  --popover: oklch(0.1458 0 22.58);
  --popover-foreground: oklch(0.9224 0 23.19);
  --primary: oklch(0.5512 0.1177 218.91);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.2686 0 23.43);
  --secondary-foreground: oklch(0.9224 0 23.19);
  --muted: oklch(0.2096 0 23.33);
  --muted-foreground: oklch(0.7084 0 23.42);
  --accent: oklch(0.2096 0 23.33);
  --accent-foreground: oklch(0.9224 0 23.19);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.2686 0 23.43);
  --input: oklch(0.3722 0 24.37);
  --ring: oklch(0.5512 0.1177 218.91);
  --chart-1: oklch(0.5512 0.1177 218.91);
  --chart-2: oklch(0.9148 0.0906 206.08);
  --chart-3: oklch(0.7876 0.173 211.42);
  --chart-4: oklch(0.8632 0.1432 207.1);
  --chart-5: oklch(0.9536 0.0508 205.46);
  --sidebar: oklch(0.2096 0 23.33);
  --sidebar-foreground: oklch(0.9224 0 23.19);
  --sidebar-primary: oklch(0.5512 0.1177 218.91);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.2686 0 23.43);
  --sidebar-accent-foreground: oklch(0.9224 0 23.19);
  --sidebar-border: oklch(0.2686 0 23.43);
  --sidebar-ring: oklch(0.5512 0.1177 218.91);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* TradingView Widget Alignment Fixes */
.tradingview-widget-container {
  position: relative;
  overflow: hidden;
}

.tradingview-widget-container iframe {
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: inherit;
}

.tradingview-widget-container__widget {
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: inherit;
}

/* Fix TradingView popup modal centering */
.tv-dialog {
  border-radius: 8px !important;
}

.tv-dialog__section {
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure chart fills container properly */
.tradingview-widget-container > div {
  width: 100% !important;
  height: 100% !important;
  border-radius: inherit;
}

/* Agent Vault V2 Custom Styles */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
