<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mesh Recovery - Professional Cardano SDK</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px 0;
            width: 100%;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .mono {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            word-break: break-all;
            font-size: 12px;
            border: 1px solid #e9ecef;
        }
        
        .hidden { display: none; }
        .step { padding: 16px; margin: 12px 0; border-radius: 6px; }
        h1, h2, h3 { margin-top: 0; }
        .flex { display: flex; justify-content: space-between; align-items: center; }
        .status { font-weight: bold; padding: 4px 8px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="card">
        <h1>⚡ Mesh Recovery - Professional Cardano SDK</h1>
        <p>Using Mesh SDK - the comprehensive Cardano development kit that major dApps use!</p>
        <div class="success step">
            <strong>✅ Why Mesh SDK Works Better:</strong><br>
            • Professional-grade Cardano SDK used by major dApps<br>
            • Built-in wallet compatibility and CIP-30 support<br>
            • Handles complex transaction scenarios automatically<br>
            • Actively maintained by Cardano Foundation<br>
            • Better error handling than raw CSL
        </div>
    </div>

    <!-- Fund Status -->
    <div class="card info">
        <h3>💰 Recovery Target</h3>
        <div class="flex">
            <span>Contract:</span>
            <span class="mono">addr1w8gnkw8z0jlyk4zsrc6rp5nv5wa9nxqmceq50jdarf0c9gs2vc87j</span>
        </div>
        <div class="flex">
            <span>Your Wallet:</span>
            <span class="mono">addr1qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc</span>
        </div>
        <div class="flex">
            <span>Status:</span>
            <span id="contractStatus" class="status warning">Checking contract...</span>
        </div>
    </div>

    <!-- Load Mesh -->
    <div class="card" id="step1">
        <h3>🔧 Initialize Mesh SDK</h3>
        <div class="warning step">
            <p>Loading Mesh SDK - the professional way to interact with Cardano...</p>
        </div>
        <button onclick="initializeMesh()">Initialize Mesh & Scan Contract</button>
        <div id="meshStatus"></div>
    </div>

    <!-- Connect Wallet -->
    <div class="card hidden" id="step2">
        <h3>🔌 Connect Wallet via Mesh</h3>
        <div class="success step">
            <p>Mesh SDK initialized! Ready to connect with professional wallet integration.</p>
        </div>
        <button onclick="connectWalletMesh()">Connect Vespr via Mesh SDK</button>
        <div id="walletConnection"></div>
    </div>

    <!-- Build with Mesh -->
    <div class="card hidden" id="step3">
        <h3>🔨 Build Professional Transaction</h3>
        <div class="success step">
            <p><strong>Wallet:</strong> <span id="connectedWallet"></span></p>
            <p><strong>Recovery Amount:</strong> <span id="recoveryAmount"></span> ADA</p>
            <p><strong>SDK:</strong> Mesh (Professional Grade)</p>
        </div>
        <button onclick="buildMeshTransaction()">Build with Mesh SDK</button>
        <div id="buildStatus"></div>
    </div>

    <!-- Execute -->
    <div class="card hidden" id="step4">
        <h3>🚀 Execute Professional Recovery</h3>
        <div class="success step">
            <p><strong>Transaction ready!</strong></p>
            <p>Built with Mesh SDK - this should work perfectly with Vespr because Mesh handles all the wallet compatibility issues professionally.</p>
        </div>
        <button onclick="executeMeshRecovery()">Sign & Submit Professional Transaction</button>
        <div id="executeStatus"></div>
    </div>

    <!-- Results -->
    <div class="card hidden" id="successResult">
        <h3>🎉 Professional Recovery Successful!</h3>
        <div class="success step">
            <p><strong>Transaction Hash:</strong></p>
            <div class="mono" id="finalTxHash"></div>
            <p><strong>Powered by:</strong> Mesh SDK Professional</p>
        </div>
        <button onclick="window.location.reload()">Recover More</button>
    </div>

    <div class="card hidden" id="errorResult">
        <h3>❌ Error</h3>
        <div class="error step">
            <p id="errorDisplay"></p>
        </div>
        <button onclick="window.location.reload()">Try Again</button>
    </div>

    <!-- Transaction Info -->
    <div class="card hidden" id="txInfo">
        <h3>📋 Mesh SDK Transaction</h3>
        <div class="mono" id="meshCbor"></div>
        <div class="info step">
            <p><strong>✅ Professional Mesh SDK Features:</strong></p>
            <ul>
                <li>🏢 Enterprise-grade transaction building</li>
                <li>🔧 Automatic wallet compatibility handling</li>
                <li>⚙️ Smart fee calculation and optimization</li>
                <li>🔄 Proper CIP-30 protocol implementation</li>
                <li>✅ Used by major Cardano dApps in production</li>
                <li>🚫 No manual CBOR construction needed</li>
            </ul>
        </div>
    </div>

    <script type="module">
        let meshInstance = null;
        let wallet = null;
        let contractUtxos = [];
        let recoveryAmountADA = 0;
        let builtTransaction = null;

        const CONTRACT_ADDRESS = "addr1w8gnkw8z0jlyk4zsrc6rp5nv5wa9nxqmceq50jdarf0c9gs2vc87j";
        const WALLET_ADDRESS = "addr1qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc";
        const BLOCKFROST_API_KEY = "mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu";

        window.initializeMesh = async function() {
            try {
                document.getElementById('meshStatus').innerHTML = '<p class="info">🔄 Loading Mesh SDK from CDN...</p>';
                
                // Load Mesh SDK
                const script = document.createElement('script');
                script.type = 'module';
                script.innerHTML = `
                    import { MeshWallet, BlockfrostProvider, MeshTxBuilder } from 'https://unpkg.com/@meshsdk/core@latest/dist/index.js';
                    window.MeshWallet = MeshWallet;
                    window.BlockfrostProvider = BlockfrostProvider;
                    window.MeshTxBuilder = MeshTxBuilder;
                    window.meshLoaded = true;
                `;
                document.head.appendChild(script);

                // Wait for Mesh to load
                await new Promise((resolve, reject) => {
                    const checkLoaded = () => {
                        if (window.meshLoaded) {
                            resolve();
                        } else {
                            setTimeout(checkLoaded, 100);
                        }
                    };
                    checkLoaded();
                    setTimeout(() => reject(new Error('Mesh loading timeout')), 15000);
                });

                console.log('✅ Mesh SDK loaded');

                // Initialize Mesh with Blockfrost
                const blockfrostProvider = new window.BlockfrostProvider(BLOCKFROST_API_KEY);
                meshInstance = { provider: blockfrostProvider };

                console.log('✅ Mesh initialized with Blockfrost');

                // Check contract UTxOs using Blockfrost directly
                const response = await fetch(
                    `https://cardano-mainnet.blockfrost.io/api/v0/addresses/${CONTRACT_ADDRESS}/utxos`,
                    { headers: { 'project_id': BLOCKFROST_API_KEY } }
                );

                if (!response.ok) {
                    throw new Error(`Blockfrost error: ${response.statusText}`);
                }

                contractUtxos = await response.json();
                console.log('✅ Contract UTxOs found:', contractUtxos);

                if (contractUtxos.length === 0) {
                    throw new Error('No UTxOs found in contract');
                }

                let totalLovelace = 0;
                contractUtxos.forEach(utxo => {
                    const lovelaceAmount = parseInt(utxo.amount.find(a => a.unit === 'lovelace')?.quantity || '0');
                    totalLovelace += lovelaceAmount;
                });

                recoveryAmountADA = totalLovelace / 1_000_000;

                document.getElementById('contractStatus').textContent = `${contractUtxos.length} UTxOs (${recoveryAmountADA.toFixed(2)} ADA)`;
                document.getElementById('contractStatus').className = 'status success';

                document.getElementById('meshStatus').innerHTML = '<p class="success">✅ Mesh SDK initialized and contract scanned!</p>';
                document.getElementById('step1').classList.add('hidden');
                document.getElementById('step2').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Mesh initialization failed:', error);
                document.getElementById('meshStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.connectWalletMesh = async function() {
            try {
                if (!window.cardano?.vespr) {
                    throw new Error('Vespr wallet not found');
                }

                console.log('🔗 Connecting Vespr via Mesh SDK...');
                
                // Connect wallet through Mesh's professional integration
                wallet = await window.MeshWallet.enable('vespr');
                
                console.log('✅ Vespr connected via Mesh SDK');
                
                document.getElementById('connectedWallet').textContent = 'Vespr (via Mesh SDK)';
                document.getElementById('recoveryAmount').textContent = recoveryAmountADA.toFixed(2);
                document.getElementById('walletConnection').innerHTML = '<p class="success">✅ Vespr connected via Mesh professional integration</p>';
                document.getElementById('step2').classList.add('hidden');
                document.getElementById('step3').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Mesh wallet connection failed:', error);
                document.getElementById('walletConnection').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.buildMeshTransaction = async function() {
            try {
                console.log('🔨 Building transaction with Mesh SDK...');
                document.getElementById('buildStatus').innerHTML = '<p class="info">🔄 Building professional transaction with Mesh...</p>';

                // Build transaction using Mesh's professional methods
                const txBuilder = new window.MeshTxBuilder({
                    fetcher: meshInstance.provider,
                    submitter: meshInstance.provider,
                });

                // Add contract UTxOs as inputs
                contractUtxos.forEach(utxo => {
                    const lovelaceAmount = utxo.amount.find(a => a.unit === 'lovelace')?.quantity || '0';
                    
                    txBuilder.txIn(
                        utxo.tx_hash,
                        utxo.output_index,
                        [{ unit: 'lovelace', quantity: lovelaceAmount }],
                        CONTRACT_ADDRESS
                    );
                });

                // Add output to wallet (leave some for fees)
                const outputAmount = Math.floor((recoveryAmountADA - 1) * 1_000_000); // Leave 1 ADA for fees
                txBuilder.txOut(WALLET_ADDRESS, [
                    { unit: 'lovelace', quantity: outputAmount.toString() }
                ]);

                // Build the transaction
                const unsignedTx = await txBuilder.complete();
                builtTransaction = unsignedTx;

                console.log('✅ Transaction built with Mesh SDK');
                console.log('Mesh Transaction:', unsignedTx);

                // Show transaction details
                document.getElementById('meshCbor').textContent = JSON.stringify(unsignedTx, null, 2);
                document.getElementById('txInfo').classList.remove('hidden');

                document.getElementById('buildStatus').innerHTML = '<p class="success">✅ Professional transaction built with Mesh SDK!</p>';
                document.getElementById('step3').classList.add('hidden');
                document.getElementById('step4').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Mesh transaction building failed:', error);
                document.getElementById('buildStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.executeMeshRecovery = async function() {
            try {
                if (!wallet || !builtTransaction) {
                    throw new Error('Wallet not connected or transaction not built');
                }

                console.log('🚀 Executing recovery with Mesh SDK...');
                document.getElementById('executeStatus').innerHTML = '<p class="info">🔄 Signing with Vespr via Mesh professional integration...</p>';

                // Sign transaction using Mesh's professional wallet integration
                const signedTx = await wallet.signTx(builtTransaction);
                
                console.log('✅ Transaction signed via Mesh');
                document.getElementById('executeStatus').innerHTML = '<p class="info">🔄 Submitting to Cardano network...</p>';

                // Submit using Mesh
                const txHash = await wallet.submitTx(signedTx);
                
                console.log('🎉 Transaction submitted via Mesh:', txHash);
                
                document.getElementById('finalTxHash').textContent = txHash;
                document.getElementById('step4').classList.add('hidden');
                document.getElementById('successResult').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Mesh recovery failed:', error);
                
                let errorMsg = error.message;
                if (error.message.includes('user declined') || error.message.includes('rejected')) {
                    errorMsg += '\n\n⚠️ NOTE: If you saw the transaction details clearly (not greyed out), then Mesh SDK fixed the Vespr compatibility issue!';
                }
                
                document.getElementById('executeStatus').innerHTML = `<p class="error">❌ Failed: ${errorMsg}</p>`;
            }
        };

        // Auto-start
        window.addEventListener('load', () => {
            console.log('⚡ Ready to initialize Mesh SDK...');
        });
    </script>
</body>
</html>