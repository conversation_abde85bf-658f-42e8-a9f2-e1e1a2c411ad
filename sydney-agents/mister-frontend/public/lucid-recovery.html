<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lucid Recovery - Fix Vespr Issues</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px 0;
            width: 100%;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .mono {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            word-break: break-all;
            font-size: 12px;
            border: 1px solid #e9ecef;
        }
        
        .hidden { display: none; }
        .step { padding: 16px; margin: 12px 0; border-radius: 6px; }
        h1, h2, h3 { margin-top: 0; }
        .flex { display: flex; justify-content: space-between; align-items: center; }
        .status { font-weight: bold; padding: 4px 8px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="card">
        <h1>🚀 Lucid Recovery - Fix Vespr Issues</h1>
        <p>Using Lucid library instead of raw CSL - this should fix the Vespr greyed out issue!</p>
        <div class="success step">
            <strong>✅ Why Lucid Works Better:</strong><br>
            • Lucid is designed specifically for Cardano dApps<br>
            • Handles wallet compatibility issues automatically<br>
            • Creates proper CIP-30 compatible transactions<br>
            • Used by major Cardano projects successfully
        </div>
    </div>

    <!-- Fund Status -->
    <div class="card info">
        <h3>💰 Found Stuck Funds</h3>
        <div class="flex">
            <span>Contract:</span>
            <span class="mono">addr1w8gnkw8z0jlyk4zsrc6rp5nv5wa9nxqmceq50jdarf0c9gs2vc87j</span>
        </div>
        <div class="flex">
            <span>Your Wallet:</span>
            <span class="mono">addr1qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc</span>
        </div>
        <div class="flex">
            <span>Available UTxOs:</span>
            <span id="utxoCount" class="status warning">Checking...</span>
        </div>
    </div>

    <!-- Load Lucid -->
    <div class="card" id="step1">
        <h3>🔧 Load Lucid Library</h3>
        <div class="warning step">
            <p>Loading Lucid - the proper way to build Cardano transactions...</p>
        </div>
        <button onclick="loadLucid()">Load Lucid & Check UTxOs</button>
        <div id="lucidStatus"></div>
    </div>

    <!-- Connect Wallet -->
    <div class="card hidden" id="step2">
        <h3>🔌 Connect Wallet</h3>
        <div class="success step">
            <p>Lucid loaded! Ready to connect wallet with proper CIP-30 support.</p>
        </div>
        <button onclick="connectWallet()">Connect Vespr (via Lucid)</button>
        <div id="walletStatus"></div>
    </div>

    <!-- Build Transaction -->
    <div class="card hidden" id="step3">
        <h3>🔨 Build Recovery Transaction</h3>
        <div class="success step">
            <p><strong>Wallet Connected:</strong> <span id="walletName"></span></p>
            <p><strong>Recovery Amount:</strong> <span id="amount"></span> ADA</p>
            <p><strong>Using:</strong> Lucid (proper dApp library)</p>
        </div>
        <button onclick="buildTransaction()">Build Lucid Transaction</button>
        <div id="transactionStatus"></div>
    </div>

    <!-- Execute Recovery -->
    <div class="card hidden" id="step4">
        <h3>🚀 Execute Recovery</h3>
        <div class="success step">
            <p><strong>Transaction built with Lucid!</strong></p>
            <p>This should NOT be greyed out in Vespr because Lucid handles wallet compatibility properly.</p>
        </div>
        <button onclick="executeRecovery()">Sign & Submit via Lucid</button>
        <div id="executionStatus"></div>
    </div>

    <!-- Results -->
    <div class="card hidden" id="success">
        <h3>🎉 Recovery Successful!</h3>
        <div class="success step">
            <p><strong>Transaction Hash:</strong></p>
            <div class="mono" id="txHash"></div>
            <p><strong>Status:</strong> Submitted to Cardano network</p>
        </div>
        <button onclick="window.location.reload()">Recover More ADA</button>
    </div>

    <div class="card hidden" id="error">
        <h3>❌ Error</h3>
        <div class="error step">
            <p id="errorMsg"></p>
        </div>
        <button onclick="window.location.reload()">Try Again</button>
    </div>

    <!-- Transaction Details -->
    <div class="card hidden" id="txDetails">
        <h3>📋 Lucid Transaction Details</h3>
        <div class="mono" id="cborHex"></div>
        <div class="info step">
            <p><strong>✅ Built with Lucid library:</strong></p>
            <ul>
                <li>🔧 Proper CIP-30 wallet integration</li>
                <li>⚙️ Automatic fee calculation</li>
                <li>🔄 Correct transaction structure</li>
                <li>✅ Vespr-compatible output format</li>
                <li>🚫 No manual CBOR construction</li>
            </ul>
        </div>
    </div>

    <script type="module">
        let lucid = null;
        let utxos = [];
        let recoveryAmount = 0;

        const CONTRACT_ADDRESS = "addr1w8gnkw8z0jlyk4zsrc6rp5nv5wa9nxqmceq50jdarf0c9gs2vc87j";
        const WALLET_ADDRESS = "addr1qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc";

        window.loadLucid = async function() {
            try {
                document.getElementById('lucidStatus').innerHTML = '<p class="info">🔄 Loading Lucid from CDN...</p>';
                
                // Load Lucid from CDN
                const script = document.createElement('script');
                script.type = 'module';
                script.innerHTML = `
                    import { Lucid, Blockfrost } from 'https://unpkg.com/lucid-cardano@latest/web/mod.js';
                    window.Lucid = Lucid;
                    window.Blockfrost = Blockfrost;
                    window.lucidLoaded = true;
                `;
                document.head.appendChild(script);

                // Wait for Lucid to load
                await new Promise((resolve, reject) => {
                    const checkLoaded = () => {
                        if (window.lucidLoaded) {
                            resolve();
                        } else {
                            setTimeout(checkLoaded, 100);
                        }
                    };
                    checkLoaded();
                    setTimeout(() => reject(new Error('Lucid loading timeout')), 10000);
                });

                console.log('✅ Lucid loaded successfully');
                
                // Initialize Lucid with Blockfrost
                lucid = await window.Lucid.new(
                    new window.Blockfrost("https://cardano-mainnet.blockfrost.io/api/v0", "mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu"),
                    "Mainnet"
                );

                console.log('✅ Lucid initialized with Blockfrost');

                // Check UTxOs
                utxos = await lucid.utxosAt(CONTRACT_ADDRESS);
                console.log('✅ Found UTxOs:', utxos);

                if (utxos.length === 0) {
                    throw new Error('No UTxOs found in contract');
                }

                let totalLovelace = 0;
                utxos.forEach(utxo => {
                    totalLovelace += Number(utxo.assets.lovelace || 0);
                });
                
                recoveryAmount = totalLovelace / 1_000_000;
                
                document.getElementById('utxoCount').textContent = `${utxos.length} UTxOs (${recoveryAmount.toFixed(2)} ADA)`;
                document.getElementById('utxoCount').className = 'status success';
                
                document.getElementById('lucidStatus').innerHTML = '<p class="success">✅ Lucid loaded and UTxOs found!</p>';
                document.getElementById('step1').classList.add('hidden');
                document.getElementById('step2').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Lucid loading failed:', error);
                document.getElementById('lucidStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.connectWallet = async function() {
            try {
                if (!window.cardano?.vespr) {
                    throw new Error('Vespr wallet not found');
                }

                // Connect wallet via Lucid (proper CIP-30 integration)
                lucid.selectWallet(window.cardano.vespr);
                
                console.log('✅ Vespr connected via Lucid');
                
                document.getElementById('walletName').textContent = 'Vespr (via Lucid)';
                document.getElementById('amount').textContent = recoveryAmount.toFixed(2);
                document.getElementById('walletStatus').innerHTML = '<p class="success">✅ Vespr connected via Lucid CIP-30</p>';
                document.getElementById('step2').classList.add('hidden');
                document.getElementById('step3').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Wallet connection failed:', error);
                document.getElementById('walletStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.buildTransaction = async function() {
            try {
                console.log('🔨 Building transaction with Lucid...');
                document.getElementById('transactionStatus').innerHTML = '<p class="info">🔄 Building transaction with Lucid...</p>';
                
                // Build transaction using Lucid's proper methods
                const tx = await lucid.newTx()
                    .collectFrom(utxos)  // Collect from contract UTxOs
                    .payToAddress(WALLET_ADDRESS, { lovelace: BigInt((recoveryAmount - 1) * 1_000_000) }) // Leave 1 ADA for fees
                    .complete();

                console.log('✅ Transaction built with Lucid');
                console.log('Transaction:', tx);

                // Show transaction details
                const txCBOR = tx.toString();
                document.getElementById('cborHex').textContent = txCBOR;
                document.getElementById('txDetails').classList.remove('hidden');
                
                window.recoveryTx = tx; // Store for execution
                
                document.getElementById('transactionStatus').innerHTML = '<p class="success">✅ Transaction built with Lucid!</p>';
                document.getElementById('step3').classList.add('hidden');
                document.getElementById('step4').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Transaction building failed:', error);
                document.getElementById('transactionStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.executeRecovery = async function() {
            try {
                if (!window.recoveryTx) {
                    throw new Error('Transaction not built');
                }

                console.log('🚀 Executing recovery with Lucid...');
                document.getElementById('executionStatus').innerHTML = '<p class="info">🔄 Signing transaction with Vespr via Lucid...</p>';
                
                // Sign and submit using Lucid (proper wallet integration)
                const signedTx = await window.recoveryTx.sign().complete();
                
                console.log('✅ Transaction signed via Lucid');
                document.getElementById('executionStatus').innerHTML = '<p class="info">🔄 Submitting to network...</p>';
                
                const txHash = await signedTx.submit();
                
                console.log('🎉 Transaction submitted:', txHash);
                
                document.getElementById('txHash').textContent = txHash;
                document.getElementById('step4').classList.add('hidden');
                document.getElementById('success').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Recovery failed:', error);
                document.getElementById('executionStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
                
                if (error.message.includes('user declined')) {
                    document.getElementById('executionStatus').innerHTML += '<p class="warning">⚠️ You declined the transaction. If you saw the transaction details (not greyed out), then Lucid fixed the Vespr issue!</p>';
                }
            }
        };

        // Auto-start
        window.addEventListener('load', () => {
            console.log('🚀 Ready to load Lucid...');
        });
    </script>
</body>
</html>