<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIXED Lucid Recovery</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px 0;
            width: 100%;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .mono {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            word-break: break-all;
            font-size: 12px;
            border: 1px solid #e9ecef;
        }
        
        .hidden { display: none; }
        .step { padding: 16px; margin: 12px 0; border-radius: 6px; }
        h1, h2, h3 { margin-top: 0; }
        .flex { display: flex; justify-content: space-between; align-items: center; }
        .status { font-weight: bold; padding: 4px 8px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="card">
        <h1>🔥 FIXED Lucid Recovery - WE'RE SO CLOSE!</h1>
        <p>Fixed the getUtxos issue - this should work now!</p>
        <div class="success step">
            <strong>✅ PROGRESS SO FAR:</strong><br>
            • ✅ Lucid loaded successfully<br>
            • ✅ Lucid initialized with Blockfrost<br>
            • ✅ Found UTxOs in contract<br>
            • ✅ Vespr connected via Lucid<br>
            • ❌ Fixed: getUtxos function issue
        </div>
    </div>

    <!-- Fund Status -->
    <div class="card info">
        <h3>💰 Recovery Status</h3>
        <div class="flex">
            <span>Contract:</span>
            <span class="mono">addr1w8gnkw8z0jlyk4zsrc6rp5nv5wa9nxqmceq50jdarf0c9gs2vc87j</span>
        </div>
        <div class="flex">
            <span>Your Wallet:</span>
            <span class="mono">addr1qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc</span>
        </div>
        <div class="flex">
            <span>UTxOs Found:</span>
            <span id="utxoStatus" class="status warning">Loading...</span>
        </div>
    </div>

    <!-- Initialize -->
    <div class="card" id="step1">
        <h3>🚀 Initialize Fixed Lucid</h3>
        <div class="warning step">
            <p>Ready to load Lucid with proper wallet integration...</p>
        </div>
        <button onclick="initializeFixedLucid()">Load Fixed Lucid</button>
        <div id="initStatus"></div>
    </div>

    <!-- Connect -->
    <div class="card hidden" id="step2">
        <h3>🔌 Connect Vespr</h3>
        <div class="success step">
            <p>Lucid ready! Connecting Vespr with proper CIP-30 support.</p>
        </div>
        <button onclick="connectVespr()">Connect Vespr (Fixed)</button>
        <div id="connectStatus"></div>
    </div>

    <!-- Build -->
    <div class="card hidden" id="step3">
        <h3>🔨 Build Fixed Transaction</h3>
        <div class="success step">
            <p><strong>Wallet:</strong> <span id="walletName"></span></p>
            <p><strong>Amount:</strong> <span id="amount"></span> ADA</p>
            <p><strong>Status:</strong> Ready to build with FIXED Lucid</p>
        </div>
        <button onclick="buildFixedTransaction()">Build FIXED Transaction</button>
        <div id="buildStatus"></div>
    </div>

    <!-- Test -->
    <div class="card hidden" id="step4">
        <h3>🧪 Test Transaction</h3>
        <div class="success step">
            <p>Transaction built! Let's test if Vespr shows it properly (not greyed out).</p>
        </div>
        <button onclick="testTransaction()">Test with Vespr</button>
        <div id="testStatus"></div>
    </div>

    <!-- Execute -->
    <div class="card hidden" id="step5">
        <h3>🎯 Execute Recovery</h3>
        <div class="success step">
            <p>Transaction tested! Ready for final execution.</p>
        </div>
        <button onclick="executeRecovery()">EXECUTE RECOVERY</button>
        <div id="executeStatus"></div>
    </div>

    <!-- Success -->
    <div class="card hidden" id="success">
        <h3>🎉 RECOVERY SUCCESSFUL!</h3>
        <div class="success step">
            <p><strong>Transaction Hash:</strong></p>
            <div class="mono" id="txHash"></div>
            <p><strong>🔥 VESPR WORKED WITH LUCID! 🔥</strong></p>
        </div>
        <button onclick="window.location.reload()">Recover More</button>
    </div>

    <script type="module">
        let lucid = null;
        let walletApi = null;
        let contractUtxos = [];
        let recoveryAmount = 0;
        let builtTx = null;

        const CONTRACT_ADDRESS = "addr1w8gnkw8z0jlyk4zsrc6rp5nv5wa9nxqmceq50jdarf0c9gs2vc87j";
        const WALLET_ADDRESS = "addr1qxtkdjl87894tg6juz20jzyjqy3uyn02pr9xtq7mlh0gm2ss5dpkcny95dktp5qmyyrx82t68sge4m94qwxyrfr8f86qh5unyc";

        window.initializeFixedLucid = async function() {
            try {
                document.getElementById('initStatus').innerHTML = '<p class="info">🔄 Loading Lucid...</p>';
                
                // Load Lucid from a specific working version
                const script = document.createElement('script');
                script.type = 'module';
                script.innerHTML = `
                    import { Lucid, Blockfrost } from 'https://unpkg.com/lucid-cardano@0.10.7/web/mod.js';
                    window.Lucid = Lucid;
                    window.Blockfrost = Blockfrost;
                    window.lucidLoaded = true;
                `;
                document.head.appendChild(script);

                // Wait for load
                await new Promise((resolve, reject) => {
                    const check = () => {
                        if (window.lucidLoaded) resolve();
                        else setTimeout(check, 100);
                    };
                    check();
                    setTimeout(() => reject(new Error('Timeout')), 10000);
                });

                console.log('✅ Lucid loaded');
                
                // Initialize Lucid
                lucid = await window.Lucid.new(
                    new window.Blockfrost("https://cardano-mainnet.blockfrost.io/api/v0", "mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu"),
                    "Mainnet"
                );

                console.log('✅ Lucid initialized');

                // Get UTxOs manually via Blockfrost
                const response = await fetch(
                    `https://cardano-mainnet.blockfrost.io/api/v0/addresses/${CONTRACT_ADDRESS}/utxos`,
                    { headers: { 'project_id': 'mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu' } }
                );

                const utxosData = await response.json();
                console.log('✅ UTxOs fetched:', utxosData);

                if (utxosData.length === 0) {
                    throw new Error('No UTxOs found');
                }

                // Convert to Lucid format
                contractUtxos = utxosData.map(utxo => ({
                    txHash: utxo.tx_hash,
                    outputIndex: utxo.output_index,
                    assets: { lovelace: BigInt(utxo.amount.find(a => a.unit === 'lovelace')?.quantity || '0') },
                    address: CONTRACT_ADDRESS
                }));

                let totalLovelace = 0n;
                contractUtxos.forEach(utxo => {
                    totalLovelace += utxo.assets.lovelace;
                });
                
                recoveryAmount = Number(totalLovelace) / 1_000_000;
                
                document.getElementById('utxoStatus').textContent = `${contractUtxos.length} UTxOs (${recoveryAmount.toFixed(2)} ADA)`;
                document.getElementById('utxoStatus').className = 'status success';
                
                document.getElementById('initStatus').innerHTML = '<p class="success">✅ Fixed Lucid initialized!</p>';
                document.getElementById('step1').classList.add('hidden');
                document.getElementById('step2').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Failed:', error);
                document.getElementById('initStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.connectVespr = async function() {
            try {
                if (!window.cardano?.vespr) {
                    throw new Error('Vespr not found');
                }

                // Connect wallet via Lucid
                lucid.selectWallet(window.cardano.vespr);
                walletApi = window.cardano.vespr;
                
                console.log('✅ Vespr connected via Lucid');
                
                document.getElementById('walletName').textContent = 'Vespr';
                document.getElementById('amount').textContent = recoveryAmount.toFixed(2);
                document.getElementById('connectStatus').innerHTML = '<p class="success">✅ Vespr connected!</p>';
                document.getElementById('step2').classList.add('hidden');
                document.getElementById('step3').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Connection failed:', error);
                document.getElementById('connectStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.buildFixedTransaction = async function() {
            try {
                console.log('🔨 Building FIXED transaction...');
                document.getElementById('buildStatus').innerHTML = '<p class="info">🔄 Building...</p>';
                
                // Build transaction with manual UTxO handling to avoid getUtxos issue
                const outputAmount = (recoveryAmount - 1) * 1_000_000; // Leave 1 ADA for fees
                
                const tx = await lucid.newTx()
                    .collectFrom(contractUtxos)  // Use our manually fetched UTxOs
                    .payToAddress(WALLET_ADDRESS, { lovelace: BigInt(Math.floor(outputAmount)) })
                    .complete();

                builtTx = tx;
                console.log('✅ Transaction built with FIXED Lucid!');
                
                document.getElementById('buildStatus').innerHTML = '<p class="success">✅ FIXED transaction built!</p>';
                document.getElementById('step3').classList.add('hidden');
                document.getElementById('step4').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Build failed:', error);
                document.getElementById('buildStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };

        window.testTransaction = async function() {
            try {
                if (!builtTx) {
                    throw new Error('No transaction built');
                }

                console.log('🧪 Testing transaction...');
                document.getElementById('testStatus').innerHTML = '<p class="info">🔄 Testing with Vespr...</p>';
                
                // Get the CBOR to test
                const txCBOR = builtTx.toString();
                console.log('CBOR:', txCBOR);
                
                // Try to sign (this will show if Vespr is greyed out or not)
                try {
                    const signed = await builtTx.sign().complete();
                    console.log('✅ TEST PASSED - Vespr showed transaction properly!');
                    document.getElementById('testStatus').innerHTML = '<p class="success">✅ TEST PASSED! Vespr is NOT greyed out!</p>';
                    document.getElementById('step4').classList.add('hidden');
                    document.getElementById('step5').classList.remove('hidden');
                } catch (signError) {
                    if (signError.message.includes('user declined') || signError.message.includes('rejected')) {
                        console.log('✅ TEST PASSED - User declined but Vespr showed properly!');
                        document.getElementById('testStatus').innerHTML = '<p class="success">✅ TEST PASSED! You declined but Vespr showed the transaction (not greyed out)!</p>';
                        document.getElementById('step4').classList.add('hidden');
                        document.getElementById('step5').classList.remove('hidden');
                    } else {
                        throw signError;
                    }
                }

            } catch (error) {
                console.error('❌ Test failed:', error);
                document.getElementById('testStatus').innerHTML = `<p class="error">❌ Test failed: ${error.message}</p>`;
            }
        };

        window.executeRecovery = async function() {
            try {
                if (!builtTx) {
                    throw new Error('No transaction built');
                }

                console.log('🎯 Executing recovery...');
                document.getElementById('executeStatus').innerHTML = '<p class="info">🔄 Signing final transaction...</p>';
                
                // Sign and submit
                const signedTx = await builtTx.sign().complete();
                console.log('✅ Signed!');
                
                document.getElementById('executeStatus').innerHTML = '<p class="info">🔄 Submitting...</p>';
                
                const txHash = await signedTx.submit();
                console.log('🎉 SUCCESS:', txHash);
                
                document.getElementById('txHash').textContent = txHash;
                document.getElementById('step5').classList.add('hidden');
                document.getElementById('success').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Execution failed:', error);
                document.getElementById('executeStatus').innerHTML = `<p class="error">❌ Failed: ${error.message}</p>`;
            }
        };
    </script>
</body>
</html>