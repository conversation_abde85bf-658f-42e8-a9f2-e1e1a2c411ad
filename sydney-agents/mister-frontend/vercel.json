{"version": 2, "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "framework": "nextjs", "regions": ["cle1"], "env": {"NEXT_PUBLIC_API_URL": "https://bridge-server-cjs-production.up.railway.app", "NEXT_PUBLIC_MASTRA_API_URL": "https://substantial-scarce-magazin.mastra.cloud", "NEXT_PUBLIC_CNT_API_URL": "https://cnt-trading-api-production.up.railway.app", "NEXT_PUBLIC_STRIKE_API_URL": "https://bridge-server-cjs-production.up.railway.app", "NEXT_PUBLIC_BLOCKFROST_PROJECT_ID": "mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu", "NEXT_PUBLIC_TAPTOOLS_API_KEY": "WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO", "NEXT_PUBLIC_WS_URL": "wss://strike-bridge-server-production.up.railway.app", "NEXT_PUBLIC_DISCORD_WEBHOOK_URL": "https://discord.com/api/webhooks/1398703610430230548/UKHnlT45pCZWLAYizmSlAJbSZVBg_FJw4r2FMrCzdYyEdFhFN_e77nRja2m7liankAXW"}, "functions": {"app/api/**": {"runtime": "nodejs20.x"}}, "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}]}