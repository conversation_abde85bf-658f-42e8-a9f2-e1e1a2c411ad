{"name": "mister-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@blockfrost/blockfrost-js": "^6.0.0", "@emurgo/cardano-serialization-lib-browser": "^15.0.0", "@emurgo/cardano-serialization-lib-nodejs": "^14.1.2", "@meshsdk/core": "^1.9.0-beta.65", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@supabase/supabase-js": "^2.50.2", "@types/bip39": "^3.0.4", "@types/recharts": "^1.8.29", "apexcharts": "^5.2.0", "axios": "^1.10.0", "bech32": "^2.0.0", "bip39": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lightweight-charts": "^5.0.7", "lucide-react": "^0.522.0", "next": "15.3.4", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "recharts": "^2.15.4", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "buffer": "^6.0.3", "eslint": "^9", "eslint-config-next": "15.3.4", "stream-browserify": "^3.0.0", "string-replace-loader": "^3.2.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}