{"name": "sydney-agents", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "echo 'No build needed for bridge server'", "start": "node mister-bridge-server.cjs", "start:production": "NODE_ENV=production node mister-bridge-server.cjs", "mister": "tsx src/mastra/start-mister.ts", "mister:simple": "tsx src/mastra/start-simple-mister.ts", "mister:demo": "tsx demo-mister.ts", "mister:bridge": "node mister-bridge-server.cjs"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.23", "@emurgo/cardano-serialization-lib-nodejs": "^12.1.1", "@mastra/core": "^0.10.10", "@mastra/fastembed": "^0.10.0", "@mastra/libsql": "^0.10.3", "@mastra/mcp": "^0.10.5", "@mastra/memory": "^0.10.4", "@modelcontextprotocol/sdk": "^1.12.3", "@stricahq/bip32ed25519": "^1.1.1", "axios": "^1.7.9", "bip39": "^3.1.0", "cors": "^2.8.5", "danfojs-node": "^1.1.2", "express": "^4.21.2", "node-fetch": "^3.3.2", "playwright": "^1.53.0", "ws": "^8.18.0", "zod": "^3.25.67"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.3", "mastra": "^0.10.10", "typescript": "^5.8.3"}}