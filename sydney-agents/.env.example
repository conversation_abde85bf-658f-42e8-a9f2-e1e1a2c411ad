# MISTER Bridge Server Environment Configuration

# Server Configuration
PORT=4113
NODE_ENV=production

# Mastra Configuration
MASTRA_API_URL=http://localhost:4112
MASTRA_API_KEY=your_mastra_api_key_here

# Strike Finance API Configuration
STRIKE_API_URL=https://app.strikefinance.org
CNT_API_URL=https://cnt-trading-api-production.up.railway.app

# Discord Webhook Configuration
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/1398703610430230548/UKHnlT45pCZWLAYizmSlAJbSZVBg_FJw4r2FMrCzdYyEdFhFN_e77nRja2m7liankAXW

# Blockfrost API Configuration (for Cardano data)
BLOCKFROST_PROJECT_ID_MAINNET=mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu
BLOCKFROST_PROJECT_ID_TESTNET=testnet_project_id_here

# Taptools API Configuration (for ADA handle resolution)
TAPTOOLS_API_KEY=WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO

# Twitter API Configuration
TWITTER_API_URL=https://twitscap-production.up.railway.app

# LLM Configuration
LLM_PROVIDER=google
LLM_MODEL=gemini-2.5-flash
LLM_MAX_TOKENS=10000

# CORS Configuration (add your production domains)
ALLOWED_ORIGINS=http://localhost:3000,https://your-production-domain.com

# Rate Limiting Configuration
STRIKE_API_CACHE_DURATION=900000
WALLET_REGISTRATION_CACHE_DURATION=60000
API_REQUEST_DELAY=5000

# Security Configuration
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Database Configuration (if needed)
DATABASE_URL=your_database_url_here

# Logging Configuration
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_AUDIT_LOGGING=true
