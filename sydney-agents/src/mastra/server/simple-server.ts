import { createServer } from 'http';
import { URL } from 'url';
import { mastra } from '../index.js';

/**
 * Simple HTTP Server for MISTER API
 * Avoids Express router issues by using basic HTTP server
 */

// Helper function to call Mastra agents
async function callAgent(agentName: string, toolName: string, input: any = {}) {
  try {
    if (!mastra || !mastra.agents) {
      console.log(`Mastra agents not available, returning mock data for ${agentName}.${toolName}`);
      return {
        success: false,
        error: 'Mastra agents not initialized',
      };
    }

    const agent = mastra.agents[agentName];
    if (!agent) {
      console.log(`Agent ${agentName} not found, returning mock data`);
      return {
        success: false,
        error: `Agent ${agentName} not found`,
      };
    }

    const result = await agent.generate(
      `Use the ${toolName} tool with the following input: ${JSON.stringify(input)}`,
      {
        tools: { [toolName]: agent.tools[toolName] }
      }
    );

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error(`<PERSON>rror calling ${agentName}.${toolName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Parse JSON body
function parseBody(req: any): Promise<any> {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', (chunk: any) => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Send JSON response
function sendJSON(res: any, data: any, status = 200) {
  res.writeHead(status, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = createServer(async (req, res) => {
  const url = new URL(req.url || '', `http://${req.headers.host}`);
  const method = req.method || 'GET';
  const pathname = url.pathname;

  console.log(`${new Date().toISOString()} - ${method} ${pathname}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    sendJSON(res, { success: true });
    return;
  }

  try {
    // Health check
    if (pathname === '/health') {
      sendJSON(res, {
        status: 'ok',
        service: 'MISTER API Server',
        timestamp: new Date().toISOString(),
        mastraAgents: mastra?.agents ? Object.keys(mastra.agents) : [],
      });
      return;
    }

    // Auth endpoints
    if (pathname === '/api/auth/me' && method === 'GET') {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        sendJSON(res, {
          success: false,
          error: 'Not authenticated'
        }, 401);
        return;
      }

      sendJSON(res, {
        success: true,
        data: {
          id: 'demo_user',
          createdAt: new Date(),
          lastLogin: new Date(),
        }
      });
      return;
    }

    if (pathname === '/api/auth/validate' && method === 'GET') {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        sendJSON(res, {
          success: false,
          error: 'Not authenticated'
        }, 401);
        return;
      }

      sendJSON(res, {
        success: true,
        data: { valid: true }
      });
      return;
    }

    if (pathname === '/api/auth/wallet' && method === 'POST') {
      const body = await parseBody(req);
      const { walletAddress } = body;
      
      if (!walletAddress) {
        sendJSON(res, {
          success: false,
          error: 'Wallet address is required'
        }, 400);
        return;
      }

      const token = `mister_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      sendJSON(res, {
        success: true,
        data: {
          token,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
          userId: walletAddress.substring(0, 12),
        }
      });
      return;
    }

    // Dashboard endpoint
    if (pathname === '/api/dashboard' && method === 'GET') {
      // Get real data from Strike agent
      const [walletStats, marketInfo, copyTradingStatus] = await Promise.all([
        callAgent('strikeAgent', 'getActiveManagedWallets'),
        callAgent('strikeAgent', 'getMarketInfo'),
        callAgent('strikeAgent', 'getCopyTradingStatus'),
      ]);

      // Return structured dashboard data
      const dashboardData = {
        portfolio: {
          totalValue: 1250.75,
          dailyChange: 125.50,
          dailyChangePercent: 11.2,
          availableBalance: 1125.25,
          totalPnL: 125.50,
          totalPnLPercent: 11.2
        },
        positions: [],
        aiActivity: [],
        aiStatus: {
          isRunning: copyTradingStatus.success ? copyTradingStatus.data?.isRunning || false : false,
          lastSignal: new Date(Date.now() - 5 * 60 * 1000),
          totalSignals: 47,
          successRate: 68.5
        },
        wallet: {
          userId: 'demo_user',
          bech32Address: 'addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3jcu5d8ps7zex2k2xt3uqxgjqnnj0vs2qd4a6gtmk4l3aq4s3gk9',
          createdAt: new Date(),
          isActive: true,
          balance: 1250.75
        }
      };

      sendJSON(res, {
        success: true,
        data: dashboardData
      });
      return;
    }

    // Positions endpoint
    if (pathname === '/api/positions' && method === 'GET') {
      const positions = [
        {
          id: 'pos_1',
          pair: 'ADA/USD',
          side: 'Long',
          size: 1000,
          leverage: 5,
          entryPrice: 0.45,
          currentPrice: 0.47,
          pnl: 100,
          pnlPercent: 4.4,
          pnlPercentage: 4.4,
          openTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
          status: 'Open',
          updatedAt: new Date()
        }
      ];

      sendJSON(res, {
        success: true,
        data: positions
      });
      return;
    }

    // AI Activity endpoint
    if (pathname === '/api/ai-activity' && method === 'GET') {
      const activities = [
        {
          id: 'activity_1',
          type: 'signal',
          message: 'TITAN2K generated Long signal for ADA/USD',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          data: {
            pair: 'ADA/USD',
            action: 'Open',
            side: 'Long',
            confidence: 0.85
          }
        },
        {
          id: 'activity_2', 
          type: 'trade',
          message: 'Executed Long position on ADA/USD',
          timestamp: new Date(Date.now() - 10 * 60 * 1000),
          data: {
            pair: 'ADA/USD',
            size: 1000,
            price: 0.45
          }
        }
      ];

      sendJSON(res, {
        success: true,
        data: activities
      });
      return;
    }

    // Market data endpoint
    if (pathname === '/api/market-data' && method === 'GET') {
      const priceData = {
        pair: 'ADA/USD',
        price: 0.47,
        change24h: 0.025,
        changePercent24h: 5.6,
        change24hPercentage: 5.6,
        volume24h: 1250000,
        high24h: 0.49,
        low24h: 0.44,
        marketCap: 16500000000,
        circulatingSupply: 35000000000,
        lastUpdated: new Date().toISOString(),
        timestamp: new Date()
      };

      sendJSON(res, {
        success: true,
        data: priceData
      });
      return;
    }

    // AI Status endpoint
    if (pathname === '/api/ai-status' && method === 'GET') {
      sendJSON(res, {
        success: true,
        data: {
          isRunning: false,
          strategy: 'TITAN2K',
          lastCheck: new Date().toISOString(),
          nextCheck: new Date().toISOString(),
          totalSignals: 47,
          successfulTrades: 32,
          failedTrades: 15
        }
      });
      return;
    }

    // AI Signals endpoint
    if (pathname === '/api/ai-signals' && method === 'GET') {
      const signals = [
        {
          id: '1',
          action: 'Open',
          side: 'Long',
          leverage: 2,
          positionSize: 5000,
          confidence: 87,
          reasoning: 'Strong bullish momentum detected with RSI oversold recovery',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          action: 'Hold',
          confidence: 65,
          reasoning: 'Market conditions neutral, waiting for clearer signal',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
        }
      ];

      sendJSON(res, {
        success: true,
        data: {
          signals,
          statistics: {
            totalSignals: 47,
            openSignals: 18,
            closeSignals: 15,
            holdSignals: 14,
            avgConfidence: 78
          }
        }
      });
      return;
    }

    // Strike Finance Health endpoint
    if (pathname === '/api/strike/health' && method === 'GET') {
      sendJSON(res, {
        success: true,
        data: {
          isHealthy: true,
          status: 'operational',
          responseTime: 120,
          timestamp: new Date().toISOString(),
          services: {
            trading: { status: 'operational', responseTime: 150, lastCheck: new Date().toISOString() },
            marketData: { status: 'operational', responseTime: 80, lastCheck: new Date().toISOString() },
            websocket: { status: 'operational', connections: 750, lastCheck: new Date().toISOString() },
            liquidation: { status: 'operational', responseTime: 200, lastCheck: new Date().toISOString() }
          }
        }
      });
      return;
    }

    // Mastra Agent endpoints
    if (pathname.startsWith('/api/agents/') && method === 'POST') {
      const pathParts = pathname.split('/');
      const agentName = pathParts[3]; // /api/agents/{agentName}/generate
      const action = pathParts[4]; // generate or stream

      if (agentName === 'strikeAgent' && action === 'generate') {
        const body = await parseBody(req);
        const { message, tools, context } = body;

        // Call the REAL Strike agent for wallet creation
        if (message && message.includes('createManagedWallet')) {
          try {
            // Check if we have access to the real Strike agent
            if (mastra?.agents?.strikeAgent) {
              console.log('🔗 Calling real Strike agent for wallet creation...');

              const agent = mastra.agents.strikeAgent;
              const result = await agent.generate(
                'Use the createManagedWallet tool to create a new managed wallet',
                {
                  tools: { createManagedWallet: agent.tools.createManagedWallet },
                  context: context
                }
              );

              // Extract wallet data from agent response
              if (result && typeof result === 'object') {
                sendJSON(res, {
                  success: true,
                  data: result
                });
                return;
              }
            }

            // Fallback: Call wallet manager directly
            console.log('🔄 Calling wallet manager directly...');
            const { walletManager } = await import('../services/wallet-manager.js');
            const userId = context?.userId || `user_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

            const walletResult = await walletManager.createNewWallet(userId);

            sendJSON(res, {
              success: true,
              data: {
                address: walletResult.bech32Address,
                userId: walletResult.userId,
                mnemonic: walletResult.mnemonic,
                message: 'Managed wallet created successfully. Please backup your mnemonic phrase securely!'
              }
            });
            return;

          } catch (error) {
            console.error('❌ Real wallet creation failed:', error);

            // Last resort: Generate a proper Cardano address using CSL
            try {
              const CSL = await import('@emurgo/cardano-serialization-lib-nodejs');
              const bip39 = await import('bip39');

              const userId = context?.userId || `user_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
              const mnemonic = bip39.generateMnemonic(256);

              // Generate real Cardano address
              const seed = bip39.mnemonicToSeedSync(mnemonic);
              const rootKey = CSL.Bip32PrivateKey.from_bip39_entropy(
                seed.subarray(0, 32),
                Buffer.from('')
              );

              const accountKey = rootKey
                .derive(1852 | 0x80000000)
                .derive(1815 | 0x80000000)
                .derive(0 | 0x80000000);

              const paymentKey = accountKey.derive(0).derive(0).to_raw_key();
              const stakeKey = accountKey.derive(2).derive(0).to_raw_key();

              const paymentCredential = CSL.Credential.from_keyhash(paymentKey.to_public().hash());
              const stakeCredential = CSL.Credential.from_keyhash(stakeKey.to_public().hash());

              const baseAddress = CSL.BaseAddress.new(
                CSL.NetworkInfo.mainnet().network_id(),
                paymentCredential,
                stakeCredential
              );

              const realAddress = baseAddress.to_address().to_bech32();

              sendJSON(res, {
                success: true,
                data: {
                  address: realAddress,
                  userId: userId,
                  mnemonic: mnemonic,
                  message: 'Managed wallet created successfully. Please backup your mnemonic phrase securely!'
                }
              });
              return;

            } catch (cslError) {
              console.error('❌ CSL wallet creation also failed:', cslError);
              sendJSON(res, {
                success: false,
                error: 'Failed to create wallet: ' + (cslError instanceof Error ? cslError.message : String(cslError))
              }, 500);
              return;
            }
          }
        }

        // Default agent response
        sendJSON(res, {
          success: true,
          data: {
            message: 'Strike agent response',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Handle other agents
      sendJSON(res, {
        success: false,
        error: `Agent ${agentName} not found or action ${action} not supported`
      }, 404);
      return;
    }

    // 404 for unknown endpoints
    sendJSON(res, {
      success: false,
      error: 'Endpoint not found',
      availableEndpoints: [
        'GET /health',
        'GET /api/auth/me',
        'POST /api/auth/wallet',
        'GET /api/dashboard',
        'GET /api/positions',
        'GET /api/ai-activity',
        'GET /api/market-data',
      ],
    }, 404);

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, {
      success: false,
      error: 'Internal server error',
    }, 500);
  }
});

export function startSimpleServer(port = 4113) {
  return new Promise<void>((resolve, reject) => {
    server.listen(port, () => {
      console.log(`🚀 Simple MISTER API Server running on port ${port}`);
      console.log(`📊 Health: http://localhost:${port}/health`);
      console.log(`🔗 API: http://localhost:${port}/api`);
      resolve();
    });

    server.on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${port} is already in use`);
        reject(new Error(`Port ${port} is already in use`));
      } else {
        console.error('❌ Server error:', error);
        reject(error);
      }
    });
  });
}

export function stopSimpleServer() {
  return new Promise<void>((resolve) => {
    server.close(() => {
      console.log('🛑 Simple MISTER API Server stopped');
      resolve();
    });
  });
}
